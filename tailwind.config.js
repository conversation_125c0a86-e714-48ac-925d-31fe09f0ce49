{
  import('tailwindcss').Config
}
export default {
  content: [
    './options.html',
    './options/*.{js,ts,jsx,tsx,vue}',
    './src/**/*.{js,ts,jsx,tsx,vue}',
    './side_panel.html',
    './side_panel/*.{js,ts,jsx,tsx,vue}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
  // important: '#_app',
  important: '.prompt-manager',
  darkMode: 'class',
  corePlugins: {
    preflight: false,
  },
}
