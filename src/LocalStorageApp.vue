<template>
  <div class="localStorage-app">
    <LocalStorageStandalone :isDarkTheme="isDarkTheme" />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

import LocalStorageStandalone from './content/components/LocalStorageStandalone.vue'
import { useOptimizedTheme } from './content/composables/useOptimizedTheme.js'

// Component performance optimization
defineOptions({
  name: 'LocalStorageApp',
  inheritAttrs: false,
})

// ============================================================================
// THEME MANAGEMENT
// ============================================================================

const { isDarkTheme, initializeTheme, cleanupTheme } = useOptimizedTheme()

// ============================================================================
// LIFECYCLE HOOKS
// ============================================================================

onMounted(async () => {
  try {
    // Initialize theme detection
    initializeTheme()
  } catch (error) {
    console.error('Error during LocalStorage app initialization:', error)
  }
})

onUnmounted(() => {
  try {
    // Cleanup theme listeners
    cleanupTheme()
  } catch (error) {
    console.error('Error during LocalStorage app cleanup:', error)
  }
})
</script>

<style scoped>
.localStorage-app {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

/* Dark theme support */
.localStorage-app.dark {
  background: var(--el-bg-color-page);
}
</style>
