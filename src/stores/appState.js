import { doc } from 'firebase/firestore'
import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser, useDocument } from 'vuefire'

import { setCreditsResetDate, setUserCredits, usersRef } from '@/content/firebase'
import {
  getFromLocalStorage,
  removeFromLocalStorage,
  setToLocalStorage,
  setToPluginStorageLocal,
} from '@/content/localStorage'
import { chatSite } from '@/content/sites_index'
import {
  clearAiStorage,
  getCreditsResetDate,
  getMaxCreditsByPlan,
  MAX_CREDITS,
  MAX_FREE_PROMPT_BARS,
  TABS,
} from '@/content/utils'

export const useAppStateStore = defineStore('appState', () => {
  const { t, locale } = useI18n()

  const user = useCurrentUser()
  const userRef = computed(() => user?.value?.uid && doc(usersRef, user.value.uid))
  const userDoc = useDocument(userRef)
  const isUserLoaded = computed(() => {
    if (user.value === undefined) {
      return false
    } else {
      return true
    }
  })
  const isLoggedIn = computed(() =>
    user?.value?.uid && doc(usersRef, user.value.uid) ? true : false,
  )
  const isLoggingIn = ref(false)
  const isSigningUp = ref(false)
  const hasSubscription = computed(() => {
    if (user.value === undefined) {
      return false
    } else {
      if (!userDoc?.value) {
        return false
      }
      return userDoc.value?.subscription?.maxMembers > 0
    }
  })

  // --- State ---
  const showApp = ref(true) // showApp is true when the app is visible in the browser
  const supportedUrls = ref([])
  const outsideTextareaPrompt = ref('') // outside chat textarea
  const activeTab = ref(getFromLocalStorage('activeTab') || TABS.Popular) // active tab from local storage or default to Popular
  const selectedBarId = ref(getFromLocalStorage(activeTab.value)?.id || '') // selected bar id from local storage or default to empty string
  const currentURLForNewBar = ref('') // current URL for new bar
  const currentChatNameForNewBar = ref('')
  const isPromptBookmark = ref(false)

  const areCreditsSet = ref(false)
  const creditsMax = ref(0)
  const nameForGeneratedPrompt = ref('')
  const textToCreatePrompt = ref('')
  const automaticNewBar = ref(false)
  const isGeneratingPromptName = ref(false) // Track AI name generation status
  const selectRef = ref(null)
  const promptBarLanguage = ref('en')
  const selectedLangInPopularDropdown = ref(localStorage.getItem('barLangPopular') || locale.value)
  const selectedLangInLibraryDropdown = ref(localStorage.getItem('barLangLibrary') || locale.value)
  const loginPopoverVisible = ref(false)
  const languages = ref(['en', 'pl'])

  const selectedTeamId = ref(getFromLocalStorage('selectedTeamId'))
  const selectedTeamIdForDialog = ref(null)
  const subscriptionDialogSelectedTab = ref('Plans')

  let selectedBar = ref(getFromLocalStorage(activeTab.value) || {}) //object of selected bar from local storage or default to empty object
  const isSelectedBarLoading = ref(false) // Loading state of selected bar, set skeleton loaders when true or prompts when false

  const isShareDialogPromptBarOpen = ref(false)
  const isSubscriptionDialogOpen = ref(false)
  const isEditPromptBarDialogOpen = ref(false)
  const isAddPromptBarDialogOpen = ref(false)

  const sharePrompt = ref('')
  const redirectedPrompt = ref('')
  const promo = ref('')
  const sharedBarId = ref('')

  const barSortFieldName = ref(getFromLocalStorage('barSortFieldName') || 'name')
  const barSortDirection = ref(getFromLocalStorage('barSortDirection') || 1)

  const areAllBarsLoaded = ref(false)
  const arePopularBarsLoaded = ref(false)
  const arePublicBarsLoaded = ref(false)
  const areMyPromptBarsLoaded = ref(false)
  const areLibraryBarsLoaded = ref(false)
  const areTeamBarsLoaded = ref(false)

  const allBars = ref([])
  const popularBars = ref([])
  const publicBars = ref([])
  const myPromptBars = ref([])
  const libraryBars = ref([])
  const teamBars = ref([])

  const isMyPromptsTabActiveAndEmpty = computed(() => {
    return (
      activeTab.value === TABS.MyPrompts &&
      !areMyPromptBarsLoaded.value &&
      myPromptBars.value?.length === 0
    )
  })
  const isTabEmpty = ref(isMyPromptsTabActiveAndEmpty.value ? true : false)

  const savedBarFromAllTab = getFromLocalStorage(TABS.All)
  const savedBarFromPopularTab = getFromLocalStorage(TABS.Popular)
  const savedBarFromPublicTab = getFromLocalStorage(TABS.Public)
  const savedBarFromMyPromptsTab = getFromLocalStorage(TABS.MyPrompts)
  const savedBarFromLibraryTab = getFromLocalStorage(TABS.Library)
  const savedBarFromFavoritesTab = getFromLocalStorage(TABS.Favorites)
  const savedBarFromTeamTab = getFromLocalStorage(TABS.Team)

  const activeBarIdsByTab = ref({
    [TABS.All]: savedBarFromAllTab?.id || null,
    [TABS.Popular]: savedBarFromPopularTab?.id || null,
    [TABS.Public]: savedBarFromPublicTab?.id || null,
    [TABS.MyPrompts]: savedBarFromMyPromptsTab?.id || null,
    [TABS.Favorites]: savedBarFromFavoritesTab?.id || null,
    [TABS.Library]: savedBarFromLibraryTab?.id || null,
    [TABS.Team]: savedBarFromTeamTab?.id || null,
    [TABS.Company]: null,
  })

  const defaultAllBars = []
  if (savedBarFromAllTab) {
    defaultAllBars.push(savedBarFromAllTab)
  }
  if (savedBarFromFavoritesTab) {
    defaultAllBars.push(savedBarFromFavoritesTab)
  }

  // Update the store instead of local variables
  setBarsForTab(TABS.All, defaultAllBars)
  setBarsForTab(TABS.Popular, savedBarFromPopularTab ? [savedBarFromPopularTab] : [])
  setBarsForTab(TABS.Public, savedBarFromPublicTab ? [savedBarFromPublicTab] : [])
  setBarsForTab(TABS.MyPrompts, savedBarFromMyPromptsTab ? [savedBarFromMyPromptsTab] : [])
  setBarsForTab(TABS.Library, savedBarFromLibraryTab ? [savedBarFromLibraryTab] : [])
  setBarsForTab(TABS.Team, savedBarFromTeamTab ? [savedBarFromTeamTab] : [])

  // --- Getters ---
  const isAdmin = computed(() => {
    return isLoggedIn.value && userDoc.value?.role?.toLowerCase().includes('admin')
  })

  const creditsOwnedByUser = computed(() => {
    return userDoc.value ? userDoc.value.creditsOwned || 0 : 0
  })

  const availableTabs = computed(() => {
    const userRole = userDoc.value?.role
    if (isLoggedIn.value && userRole === 'SUPER_ADMIN') {
      return TABS
    }
    const { All, ...tabs } = TABS
    return tabs
  })

  const canAddMoreFreePromptBars = computed(() => {
    return myPromptBars.value?.length < MAX_FREE_PROMPT_BARS
  })

  const hasActiveMyPromptsBar = computed(() => {
    return Boolean(activeBarIdsByTab.value[TABS.MyPrompts])
  })

  const supportedLanguages = computed(() => {
    return (languages.value || ['en']).map((langCode) => ({
      value: langCode,
      label: t(`smallMenu.language.${langCode}`),
    }))
  })

  // --- Actions ---
  function openEditPromptBarDialog(isBookmarkMode = false) {
    console.log('openEditPromptBarDialog: ', isBookmarkMode)
    isPromptBookmark.value = isBookmarkMode
    isEditPromptBarDialogOpen.value = true
  }

  function goToOrigin(ev, origin, barId, blank) {
    ev.preventDefault()
    ev.stopPropagation()

    setToPluginStorageLocal('redirectedPrompt', {
      barId,
      activeTabName: activeTab.value,
    })

    if (blank) {
      window.open(origin, '_blank', 'noopener,noreferrer')
    } else {
      window.location.href = origin
    }
  }

  function openAddPromptBarDialog(setCurrentUrl = true, focusName = true) {
    if (setCurrentUrl) {
      currentURLForNewBar.value = window.location.href
      // Get title from document for chat name
      if (focusName) {
        currentChatNameForNewBar.value = document.title
        currentChatNameForNewBar.value = currentChatNameForNewBar.value.replace(
          /(\s*-\s*ChatGPT$|ChatGPT\s*-\s*|ChatGPT$)/,
          '',
        )
        currentChatNameForNewBar.value = currentChatNameForNewBar.value.replace(
          /(\s*-\s*Bard$|Bard\s*-\s*|Bard$)/,
          '',
        )
        currentChatNameForNewBar.value = currentChatNameForNewBar.value.replace(
          /(\s*-\s*Bing$|Bing\s*-\s*|Bing$)/,
          '',
        )
        currentChatNameForNewBar.value = currentChatNameForNewBar.value.replace(
          /(\s*-\s*Perplexity$|Perplexity\s*-\s*|Perplexity$)/,
          '',
        )
        currentChatNameForNewBar.value = currentChatNameForNewBar.value.trim()
      } else {
        currentChatNameForNewBar.value = ''
      }
    } else {
      currentURLForNewBar.value = ''
      currentChatNameForNewBar.value = ''
    }
    isAddPromptBarDialogOpen.value = true
  }

  function setAutomaticNewBar(value) {
    automaticNewBar.value = Boolean(value)
  }

  function setOutsideTextareaPrompt(newPrompt) {
    outsideTextareaPrompt.value = newPrompt
  }

  function setNameForGeneratedPrompt(name) {
    nameForGeneratedPrompt.value = name
  }

  function setIsGeneratingPromptName(isGenerating) {
    isGeneratingPromptName.value = isGenerating
  }

  function replaceSelectedBar(bar) {
    selectedBar = bar
  }

  function setActiveTab(tabName) {
    if (activeTab.value !== tabName) {
      activeTab.value = tabName
      setToLocalStorage('activeTab', tabName)
      selectedBarId.value = activeBarIdsByTab.value[tabName] || ''
    }
  }

  function openPromptBarDropdown() {
    if (selectRef.value && typeof selectRef.value.focus === 'function') {
      selectRef.value.focus()
    }
    if (selectRef.value && typeof selectRef.value.toggleMenu === 'function') {
      selectRef.value.toggleMenu()
    }
  }

  function sortBarsByField(fieldName) {
    const isSameField = fieldName === barSortFieldName.value
    barSortFieldName.value = fieldName
    barSortDirection.value = isSameField ? -barSortDirection.value : 1
    setToLocalStorage('barSortFieldName', fieldName)
    setToLocalStorage('barSortDirection', barSortDirection.value)
  }

  function setBarsForTab(tabName, bars) {
    switch (tabName) {
      case TABS.All:
        allBars.value = bars
        break
      case TABS.Popular:
        popularBars.value = bars
        break
      case TABS.Public:
        publicBars.value = bars
        break
      case TABS.MyPrompts:
        myPromptBars.value = bars
        break
      case TABS.Library:
        libraryBars.value = bars
        break
      case TABS.Team:
        teamBars.value = bars
        break
    }
  }

  function getBarsForTab(tabName) {
    const availableBars = allBars?.value || []
    switch (tabName) {
      case TABS.Popular:
        return popularBars.value
      case TABS.Public:
        return publicBars.value
      case TABS.MyPrompts:
        return myPromptBars.value
      case TABS.Favorites:
        return availableBars.filter((bar) => userDoc?.value?.favorites?.includes(bar.id))
      case TABS.Library:
        return libraryBars.value
      case TABS.Team:
        return teamBars.value
      case TABS.All:
        return availableBars
      default:
        return []
    }
  }

  function getBarsForCurrentTab() {
    return getBarsForTab(activeTab.value).filter((bar) => bar.name)
  }

  const getOutsideSelectorFromChatSite = (sel) => {
    let outsideSelector = document.querySelector(chatSite.selector[sel])

    if (chatSite.selectorAlters && chatSite.selectorAlters[sel]) {
      for (let i = 0; i < chatSite.selectorAlters[sel].length; i++) {
        outsideSelector = outsideSelector || document.querySelector(chatSite.selectorAlters[sel][i])
      }
    }
    if (chatSite.selectorShadows && chatSite.selectorShadows[sel]) {
      for (let i = 0; i < chatSite.selectorShadows[sel].length; i++) {
        outsideSelector = outsideSelector.shadowRoot.querySelector(chatSite.selectorShadows[sel][i])
      }
    }
    // console.log('whole outsideSelector: ', outsideSelector)
    return outsideSelector
  }

  function setSharedBarId(id) {
    sharedBarId.value = id
  }

  function setRedirectedPrompt(prompt) {
    redirectedPrompt.value = prompt
  }

  function setSharePrompt(prompt) {
    sharePrompt.value = prompt
  }

  function setPromo(promoValue) {
    promo.value = promoValue
  }

  function openSharePromptBarDialog() {
    isShareDialogPromptBarOpen.value = true
  }

  function closeSharePromptBarDialog() {
    isShareDialogPromptBarOpen.value = false
  }

  function openSubscriptionDialog(selectedTab = 'Plans') {
    subscriptionDialogSelectedTab.value = selectedTab
    isSubscriptionDialogOpen.value = true
  }

  function closeSubscriptionDialog() {
    isSubscriptionDialogOpen.value = false
    subscriptionDialogSelectedTab.value = 'Plans'
  }

  function closeEditPromptBarDialog() {
    isEditPromptBarDialogOpen.value = false
    // Reset AI generation state when dialog closes
    setIsGeneratingPromptName(false)
  }

  function setTabEmpty(isEmpty) {
    isTabEmpty.value = isEmpty
  }

  function setCreditsOwnedByUser(credits) {
    if (userDoc.value) {
      userDoc.value.creditsOwned = credits
    }
  }

  function setLoginPopoverVisible(visible) {
    loginPopoverVisible.value = visible
  }

  // Add a method to update active bar ID for a specific tab
  function updateActiveBarIdForTab(tabName, barId) {
    activeBarIdsByTab.value = {
      ...activeBarIdsByTab.value,
      [tabName]: barId,
    }
    // If this is the current tab, also update selectedBarId
    if (tabName === activeTab.value) {
      selectedBarId.value = barId
    }
  }

  async function setMaxAndOwnedCredits(newUserDoc) {
    // console.log('Setting max and owned credits', newUserDoc)
    if (!newUserDoc?.id) return

    if (isAdmin.value) {
      console.log('Setting credits for Admin', isAdmin.value, MAX_CREDITS.PREMIUM)
      creditsMax.value = MAX_CREDITS.PREMIUM
    } else {
      const subscriptionPlan =
        newUserDoc?.subscription?.plan || (hasSubscription.value ? 'Standard' : '')
      const maxCredits = getMaxCreditsByPlan(subscriptionPlan)
      creditsMax.value = maxCredits

      let currentCredits = newUserDoc.creditsOwned

      if (typeof currentCredits !== 'number') {
        try {
          const resetDate = await getCreditsResetDate(newUserDoc, hasSubscription.value)
          await setCreditsResetDate(newUserDoc, resetDate)
          console.log('Initializing credits')
          currentCredits = maxCredits
          setCreditsOwnedByUser(maxCredits)
          try {
            await setUserCredits(newUserDoc.id, maxCredits)
          } catch (error) {
            console.error('Error setting user credits:', error)
          }
        } catch (error) {
          console.error('Error initializing user credits:', error)
          currentCredits = 0
          setCreditsOwnedByUser(0)
        }
      } else {
        const resetDateInDoc = newUserDoc.creditsResetDate
        if (!resetDateInDoc) {
          try {
            const resetDate = await getCreditsResetDate(newUserDoc, hasSubscription.value)
            await setCreditsResetDate(newUserDoc, resetDate)
            console.log('Setting missing reset date and credits')
            currentCredits = maxCredits
            setCreditsOwnedByUser(maxCredits)
            try {
              await setUserCredits(newUserDoc.id, maxCredits)
            } catch (error) {
              console.error('Error setting user credits:', error)
            }
          } catch (error) {
            console.error('Error setting missing reset date:', error)
          }
        } else {
          const creditsResetDateObject = resetDateInDoc.toDate()
          const creditsResetDatePlusOneMonth = new Date(creditsResetDateObject)
          creditsResetDatePlusOneMonth.setMonth(creditsResetDatePlusOneMonth.getMonth() + 1)

          if (creditsResetDatePlusOneMonth < new Date()) {
            try {
              await setCreditsResetDate(newUserDoc, creditsResetDatePlusOneMonth)
              console.log('Resetting credits based on date')
              currentCredits = maxCredits
              setCreditsOwnedByUser(maxCredits)
              try {
                await setUserCredits(newUserDoc.id, maxCredits)
              } catch (error) {
                console.error('Error setting user credits:', error)
              }
            } catch (error) {
              console.error('Error resetting credits:', error)
            }
          }
        }
      }
    }
  }

  const setSelectedLanguageInLibraryDropdown = (lang) => {
    selectedLangInLibraryDropdown.value = lang
    localStorage.setItem('barLangLibrary', lang)
  }

  const setSelectedTeamIdForDialog = (teamId) => {
    selectedTeamIdForDialog.value = teamId
  }

  function setSelectedTeamId(teamId, teamBarsList = null) {
    selectedTeamId.value = teamId
    setToLocalStorage('selectedTeamId', teamId)

    if (teamId && (teamBarsList || teamBars.value) && selectedBarId.value) {
      const barsToCheck = teamBarsList || teamBars.value
      const found = barsToCheck.find((bar) => bar.id === selectedBarId.value)
      if (!found) {
        selectedBarId.value = ''
        updateActiveBarIdForTab(TABS.Team, '')
      }
    }

    const savedTeamBar = getFromLocalStorage(TABS.Team)
    if (savedTeamBar?.id && teamId && (teamBarsList || teamBars.value)) {
      const barsToCheck = teamBarsList || teamBars.value
      const found = barsToCheck.find((bar) => bar.id === savedTeamBar.id)
      if (!found) {
        removeFromLocalStorage(TABS.Team)
        updateActiveBarIdForTab(TABS.Team, '')
      }
    }
  }

  watch(
    userDoc,
    async (newUserDoc) => {
      if (newUserDoc) {
        const userTeams = newUserDoc?.teams || []

        const teamsFullyLoaded =
          userTeams.length === 0 || userTeams.every((team) => team && team.id)

        if (teamsFullyLoaded) {
          if (
            !selectedTeamId.value ||
            !userTeams.some((team) => team.id === selectedTeamId.value)
          ) {
            setSelectedTeamId(userTeams.length > 0 ? userTeams[0].id : null)
          }
        }

        selectedTeamIdForDialog.value = selectedTeamId.value
        clearAiStorage()
        if (areCreditsSet.value || isSigningUp.value) return
        areCreditsSet.value = true
        await setMaxAndOwnedCredits(newUserDoc)
      } else {
        areCreditsSet.value = false
      }
    },
    { immediate: true, deep: true },
  )

  // Add a watch to ensure selectedBarId stays in sync with the active tab's bar ID
  watch(
    () => activeBarIdsByTab.value[activeTab.value],
    (newActiveBarId) => {
      if (newActiveBarId !== selectedBarId.value) {
        selectedBarId.value = newActiveBarId || ''
      }
    },
  )

  // Add a watch to update local storage and corresponding state when selectedBarId changes
  watch(
    () => selectedBarId.value,
    (newSelectedBarId) => {
      if (newSelectedBarId) {
        const currentTab = activeTab.value
        updateActiveBarIdForTab(currentTab, newSelectedBarId)
      }
    },
  )

  watch(
    () => teamBars.value,
    (newTeamBars) => {
      if (activeTab.value === TABS.Team && selectedTeamId.value && newTeamBars?.length >= 0) {
        const savedTeamBar = getFromLocalStorage(TABS.Team)
        if (savedTeamBar?.id && !selectedBarId.value) {
          const found = newTeamBars.find((bar) => bar.id === savedTeamBar.id)
          if (found) {
            selectedBarId.value = savedTeamBar.id
            updateActiveBarIdForTab(TABS.Team, savedTeamBar.id)
          } else {
            removeFromLocalStorage(TABS.Team)
            updateActiveBarIdForTab(TABS.Team, '')
          }
        }
      }
    },
    { immediate: true },
  )

  const promptBarPopoverVisible = ref(false)

  function openPromptBarPopover() {
    promptBarPopoverVisible.value = true
  }

  function closePromptBarPopover() {
    promptBarPopoverVisible.value = false
  }

  // Function to refresh team bars - will be set by PromptBarDropdown component
  const refreshTeamBars = ref(() => {})

  return {
    // State
    showApp,
    hasSubscription,
    supportedUrls,
    outsideTextareaPrompt,
    isLoggedIn,
    isLoggingIn,
    isSigningUp,
    selectedBarId,
    isPromptBookmark,
    isAddPromptBarDialogOpen,
    userDoc,
    activeTab,
    isShareDialogPromptBarOpen,
    nameForGeneratedPrompt,
    textToCreatePrompt,
    automaticNewBar,
    isGeneratingPromptName,
    currentURLForNewBar,
    currentChatNameForNewBar,
    isSubscriptionDialogOpen,
    isEditPromptBarDialogOpen,
    sharePrompt,
    redirectedPrompt,
    promo,
    sharedBarId,
    barSortFieldName,
    barSortDirection,
    areAllBarsLoaded,
    arePopularBarsLoaded,
    arePublicBarsLoaded,
    areMyPromptBarsLoaded,
    areLibraryBarsLoaded,
    areTeamBarsLoaded,
    allBars,
    popularBars,
    publicBars,
    myPromptBars,
    libraryBars,
    teamBars,
    selectedTeamId,
    selectedTeamIdForDialog,
    subscriptionDialogSelectedTab,
    selectedBar,
    refreshTeamBars,
    isSelectedBarLoading,
    isTabEmpty,
    activeBarIdsByTab,
    selectRef,
    promptBarLanguage,
    selectedLangInPopularDropdown,
    selectedLangInLibraryDropdown,
    loginPopoverVisible,
    creditsMax,
    languages,
    supportedLanguages,
    promptBarPopoverVisible,
    // Getters
    isAdmin,
    isUserLoaded,
    userRef,
    creditsOwnedByUser,
    availableTabs,
    isMyPromptsTabActiveAndEmpty,
    canAddMoreFreePromptBars,
    hasActiveMyPromptsBar,

    // Actions
    setActiveTab,
    openEditPromptBarDialog,
    openAddPromptBarDialog,
    setOutsideTextareaPrompt,
    replaceSelectedBar,
    setNameForGeneratedPrompt,
    setIsGeneratingPromptName,
    openSharePromptBarDialog,
    closeSharePromptBarDialog,
    setAutomaticNewBar,
    openSubscriptionDialog,
    closeSubscriptionDialog,
    closeEditPromptBarDialog,
    sortBarsByField,
    getBarsForTab,
    getBarsForCurrentTab,
    setSharedBarId,
    setRedirectedPrompt,
    setSharePrompt,
    setPromo,
    setTabEmpty,
    setCreditsOwnedByUser,
    openPromptBarDropdown,
    goToOrigin,
    getOutsideSelectorFromChatSite,
    updateActiveBarIdForTab,
    setLoginPopoverVisible,
    setSelectedLanguageInLibraryDropdown,
    setSelectedTeamIdForDialog,
    setSelectedTeamId,
    openPromptBarPopover,
    closePromptBarPopover,
  }
})
