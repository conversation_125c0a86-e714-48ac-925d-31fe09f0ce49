import { defineManifest } from '@crxjs/vite-plugin'

import { version } from '../package'

export default defineManifest({
  name: 'AI Prompt Lab',
  description: '',
  version,
  manifest_version: 3,
  permissions: ['storage', 'offscreen', 'tabs', 'sidePanel'],
  icons: {
    16: 'img/<EMAIL>',
    32: 'img/<EMAIL>',
    48: 'img/<EMAIL>',
    128: 'img/<EMAIL>',
  },
  action: {
    default_icon: 'img/<EMAIL>',
  },
  options_page: 'options.html',
  side_panel: {
    default_path: 'side_panel.html',
  },
  background: {
    service_worker: 'src/background/index.js',
    type: 'module',
  },
  content_scripts: [
    {
      matches: ['<all_urls>'],
      js: ['src/content/index.js'],
    },
  ],
  web_accessible_resources: [
    {
      resources: [
        'img/<EMAIL>',
        'img/<EMAIL>',
        'img/<EMAIL>',
        'img/<EMAIL>',
        'icons/Copywriting.svg',
        'icons/Marketing.svg',
        'icons/Productivity.svg',
        'icons/Professionals.svg',
        'icons/SEO.svg',
        'icons/Social_Media.svg',
        'icons/Copywriting--kopia.svg',
        'icons/Marketing--kopia.svg',
        'icons/Productivity--kopia.svg',
        'icons/Professionals--kopia.svg',
        'icons/SEO--kopia.svg',
        'icons/Social_Media--kopia.svg',
        'icons/logo.svg',
        'icons/AI-PromptLab.ico',
        'src/assets/logo.png',
        'src/assets/logo-dark.png',
        'vue-debug.html',
        'local_storage.html',
      ],
      matches: ['<all_urls>'],
    },
  ],
})
