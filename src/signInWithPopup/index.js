import { getAuth, GoogleAuthProvider, signInWithPopup } from 'firebase/auth'

import { firebaseApp } from '../content/firebase'

const auth = getAuth(firebaseApp)
const googleProvider = new GoogleAuthProvider()

const PARENT_FRAME = document.location.ancestorOrigins[0]

function sendResponse(result) {
  globalThis.parent.self.postMessage(JSON.stringify(result), PARENT_FRAME)
}

globalThis.addEventListener('message', function ({ data }) {
  if (data.initAuth) {
    // Opens the Google sign-in page in a popup, inside of an iframe in the
    // extension's offscreen document.
    // To centralize logic, all respones are forwarded to the parent frame,
    // which goes on to forward them to the extension's service worker.
    signInWithPopup(auth, googleProvider).then(sendResponse).catch(sendResponse)
  }
})
