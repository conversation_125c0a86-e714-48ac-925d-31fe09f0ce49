{"btn": {"add": "Add", "newTag": "New tag", "enterTab": "Enter tab", "edit": "Edit", "copy": "Copy", "delete": "Delete", "cancel": "Cancel", "save": "Save", "close": "Close", "ok": "OK", "openUrl": "Open url", "openInNewTab": "Open in new tab", "openUrlInNewTab": "Open url in new tab", "insert": "Insert prompt", "regenerate": "Regenerate", "addToMyPrompts": "Add to My Prompts", "findInLibrary": "Find in library", "remove": "Remove", "cancelAndClose": "Cancel & Close", "setToPublic": "Set to Public", "setToPrivate": "Set to Private", "share": "Share"}, "team": {"select": "Select Team"}, "bar": {"select": "Select Prompt Bar", "noBarSelected": "No bar selected. Please select bar from above.", "createNew": "Add Prompt Bar", "copyToYour": "Copy to your bar", "editCurrent": "Edit current bar", "empty": "Bar is currently empty", "click": "Click", "prompts": "Bar prompts", "links": "Bar links", "description": "Description", "destination": "Link", "sortBy": "Sort By", "date": "Date", "noPromptBars": "No prompt bars", "tags": "Tags", "newPromptBarName": "New Prompt Bar Name", "selectAI": "Select AI"}, "prompt": {"bookmark": "Bookmark", "contents": "Prompt contents", "description": "Prompt description", "destination": "Link", "enterText": "Enter prompt text to activate", "enterCustomURL": "Enter custom URL"}, "tabs": {"myPrompts": "My Prompts", "library": "Library", "public": "Public", "popular": "Popular", "team": "Team", "all": "All", "empty": "Tab is empty", "favorites": "Favorites", "description": {"popular": "Most Popular prompts selected for best usability", "public": "All Public prompts from our community", "myPrompts": "Custom prompts made by you", "favorites": "Your favorite prompts", "library": "Collection of thousands of prompts created by professional AI writers", "all": "All prompts only for admin users", "team": "Team prompts"}}, "tooltip": {"selectBar": "Select bar in My Prompts to activate", "addBookmark": "Add new Bookmark", "addPrompt": "Add new Prompt", "noCredits": "You have used all your credits"}, "subscription": {"required": "Please upgrade your subscription to add more bars", "buyPremium": "Buy Premium", "buySubscription": "Buy Subscription"}, "messages": {"signIn": "Sign In", "signUp": "Sign Up", "signInWithGoogle": "Sign In with Google", "signUpWithGoogle": "Sign Up with Google", "name": "Name", "enterName": "Enter your Name", "email": "Email", "enterEmail": "Enter your Email", "password": "Password", "enterPassword": "Enter your Password", "passwordConfirmation": "Password Confirmation", "couponCode": "Coupon Code (optional)", "enterCouponCode": "Enter your Coupon Code", "resetPassword": "Reset password", "iAgree": "I agree", "iAgreeToTerms": "I agree to the terms", "licenseAgreement": "License agreement", "generationInProgress": "Generation in progress...", "comparePrompts": "This prompt is better than original by:", "noFavorites": "No Favorites", "noTeamMembership": "You are not a member of any team", "loginRequired": "Please login to use this feature.", "loginRequiredTabText": "Create an account or login to get access to current tab", "subscriptionRequiredToUse": "Buy a subscription to use", "canNotAddMoreFreeBars": "You can't add more free bars. Please select a bar or upgrade your", "comparing": "Comparing...", "thisPromptIsBetterThanOriginalBy": "This prompt is better than original by", "howIsItCalculated": "How is it calculated", "regenerationLimitReached": "Regeneration limit reached", "addBookmarkToSelectedPromptBar": "Add new Bookmark", "bookmarkCurrentPageToSelectedBar": "Bookmark current page", "selectBarInMyPromptsToActivate": "Select bar in My Prompts to activate", "addPromptToSelectedPromptBar": "Add new Prompt", "enterPromptTextAndSelectBarInMyPromptsToActivate": "Enter prompt text and select bar in My Prompts to activate", "searchTheInternetWhenRespondingToTheGivenPrompt": "Search the internet when responding to the given prompt", "searchTheWeb": "Search the web", "enterPromptTextToActivate": "Enter prompt text to activate", "copyToYourBar": "Copy to your bar", "selectPromptBarToCopyToYourBar": "Select a prompt bar to copy to your bar", "addANewPromptBar": "Add a new prompt bar", "addNewLink": "Add new Link", "addNewVideoLink": "Add video Link", "addNewPopup": "Add new Popup", "addNewFolder": "Add new folder", "editCurrentPromptBar": "Edit current prompt bar", "areYouSureToDeleteThis": "Are you sure to delete this?", "noBarSelected": "No bar selected", "toggleFavorite": "Toggle favorite", "buySubscriptionToUseFavorites": "Buy a subscription to use favorites", "invalidJson": "Invalid JSON format", "invalidPromptData": "Invalid prompt data", "showMenu": "Show menu", "hideMenu": "Hide menu", "addSomePrompts": "to add some prompts or", "fromAnotherBar": "them from another bar", "empty": "No prompts", "newFolder": "New folder", "newPrompt": "New prompt", "generatingName": "Generating name...", "generatingNameDescription": "AI is creating a descriptive name based on your prompt content. You can continue editing while this happens.", "newBookmark": "New bookmark", "unverifiedEmail": "Unverified email", "missingPassword": "Missing password", "wrongPassword": "Wrong password", "invalidEmail": "Invalid email", "invalidLoginCredentials": "Invalid login credentials", "userNotFound": "User not found", "googleUser": "Google User", "googleEmailNotVerified": "Email associated with Google account is not verified.", "resetPasswordEmailSent": "Reset Password Email sent", "missingEmail": "<PERSON><PERSON> is missing", "verificationEmailSent": "Verification Email sent", "tooManyRequests": "Too many requests", "emailCannotBeEmpty": "Email cannot be empty", "passwordCannotBeEmpty": "Password cannot be empty", "passwordsMustMatch": "Passwords have to match", "mustAgreeToLicense": "You must agree to the license agreement", "emailAlreadyInUse": "Email already in use", "passwordTooShort": "Password should be at least 6 characters", "signingInWithGoogle": "Signing in with Google...", "sendVerificationEmail": "Send verification email", "barAddedToPopular": "Bar added to popular", "barRemovedFromPopular": "Bar removed from popular", "barAddedToLibrary": "Bar added to library", "barRemovedFromLibrary": "Bar removed from library", "privateBarAccessDenied": "This bar is private and cannot be accessed."}, "app": {"discoverThePowerOf": "AI-PromptLab.com - Browser Extension where you can create, store and manage you prompts", "keyFeaturesOf": "Key features of AI Prompt Lab include", "intelligentPromptGenerationBasedOnYourInput": "Intelligent prompt generation based on your input", "customizableOptionsToFineTuneYourPrompts": "Customizable options to fine-tune your prompts", "integrationWithPopularAIImageGenerationPlatforms": "Integration with popular AI image generation platforms", "userFriendlyInterfaceForEffortlessPromptCreation": "User-friendly interface for effortless prompt creation", "continuousUpdatesToImprovePromptQualityAndRelevance": "Continuous updates to improve prompt quality and relevance"}, "smallMenu": {"language": {"en": "English", "pl": "Polish", "fr": "French", "de": "German", "es": "Spanish", "it": "Italian", "ja": "Japanese", "ko": "Korean", "pt": "Portuguese", "ru": "Russian"}, "version": "Version", "newVersion": "New version", "credits": "Credits", "subscription": "Subscription", "buySubscription": "Buy Subscription", "account": "Account", "signInSignUp": "Sign In / Sign Up", "hello": "Hello", "manageTeam": "Manage Team", "teams": "Teams", "teamSubscription": "You are on a Team Subscription", "signOut": "Sign Out"}, "dialog": {"editBar": "Edit Bar", "rawJson": "Raw JSON", "invalidFormat": "Invalid format", "availableInPublicBars": "Available in Public bars", "linkAccessOnly": "Link access only, hidden from public search", "allowOthersToEdit": "Allow others to edit", "notEditableByOthers": "Not editable by others", "sharePrompt": "Share prompt", "shareLink": "Share link", "sharePromptBar": "Share prompt bar", "bar": "Bar", "shareOn": "Share on", "shareImage": "Share image", "copied": "<PERSON>pied", "setToPublicToShare": "To share your bar, you need to set it to public."}, "sort": {"name": "Name", "date": "Date", "destination": "Link", "ascending": "Ascending", "descending": "Descending", "filter": "Filter prompts", "filterByTags": "Filter by tags", "clearFilter": "Clear filter"}, "search": {"placeholder": "Search prompts...", "noResults": "No prompts found", "byName": "Search by name", "byTags": "Search by tags", "inDescription": "Search in description"}, "promptConfig": {"title": "Prompt Configuration", "description": "Description", "destination": "Link", "openUrl": "Open url", "openInNewTab": "Open in new tab", "insertPrompt": "Insert prompt", "cancel": "Cancel", "noDescription": "No description available", "promptContents": "Prompt contents", "premium": {"required": "Premium prompts require a subscription", "adminMode": "Admin mode - prompt enabled", "enjoy": "Enjoy!"}, "selectIcon": "Select an icon", "emptyIcon": "Empty icon", "copied": "<PERSON>pied", "copyFailed": "Co<PERSON> failed"}, "menu": {"folderEmpty": "Folder Empty", "youAreHere": "You are here", "goToBookmark": "Go to bookmark: ", "bookmarkWithoutDestination": "Bookmark without link", "insertPrompt": "Insert prompt: ", "openPromptConfig": "Open prompt config", "goToDestinationUrl": "Go to destination url:", "openUrl": "Open url", "openInNewTab": "Open in new tab", "promptPremiumEnabled": "Prompt Premium - prompt enabled for admin", "premiumPromptsRequireSubscription": "Premium prompts require a subscription", "buySubscription": "Buy Subscription"}, "copyTab": {"copyTo": "Copy to", "copyFrom": "Copy from", "selectBar": "Select Bar", "copyFromRight": "Copy from right:", "copyAll": "All", "copyAllSelected": "All selected", "copyOnlyPrompts": "Prompts", "onlyPrompts": "Only prompts", "copyFolders": "Folders", "actionOnLeft": "Action on left:", "remove": "Remove", "cancel": "Cancel", "save": "Save", "uncheckAll": "(Un)Check all", "featureOnlyForSubscription": "Feature only available for users with subscriptions.", "openPromptConfig": "Open prompt config", "barSavedSuccessfully": "Bar {name} saved successfully", "copied": {"prompts": "Copied 0 prompts | Copied {count} prompt | Copied {count} prompts", "folders": "and 0 folders | and {count} folder | and {count} folders", "successfully": "successfully"}, "empty": "No prompts to copy", "selectBarToCopyFrom": "Select bar to copy from", "errors": {"somethingWentWrong": "Something went wrong, please try again.", "noPromptsToCopy": "No prompts to copy"}, "copy": "Copy", "toSelectedUsers": "Copied to selected users", "copyToUser": "Copy prompt bar"}, "translationTab": {"selectUserPlaceholder": "Select user to copy to"}, "editPromptBar": {"description": "Description", "tabs": {"promptsTab": "Bar prompts", "linksTab": "Bar links", "copyTab": "Copy prompts", "translationTab": "Translation"}, "collapse": {"barPrompts": "Prompts", "barDesc": "Description", "barRawJson": "Raw JSON", "barChatLink": "Link", "barLinks": "Links", "folderChatLink": "Link to folder", "promptContents": "Prompt contents", "promptDesc": "Prompt description", "promptChatLink": "Link to prompt"}, "folderOptions": {"title": "Folder options:", "premium": "Premium:", "setPremium": "Set/Unset all prompts in this folder as Premium"}, "promptOptions": {"bookmark": "Bookmark", "prompt": "Prompt", "premium": "Premium", "free": "Free"}, "share": {"link": "Share link", "update": "Update share page", "visit": "Visit link", "copy": "Copy link", "public": "Public", "private": "Private", "listed": "Available in Public bars", "unlisted": "Link access only, hidden from public search", "editable": "Allow others to edit", "notEditable": "Not editable by others"}, "errors": {"invalidJson": "Invalid JSON!", "invalidUrl": "Invalid URL!"}, "buttons": {"input": "Input", "textarea": "Textarea", "select": "Select", "close": "Close"}}, "aiTools": {"betterPrompt": "Better Prompt", "frameworks": "Frameworks", "finder": "Finder", "default": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "tips": "Tips", "improvePrompt": "Improve your prompt using AI", "rtfTooltip": "Role, Task, Format", "tagTooltip": "Task, Action, Goal", "babTooltip": "Before, After, Middle", "careTooltip": "Challenge, Action, Result, Development", "riseTooltip": "Role, Input, Steps, Expectations", "coastTooltip": "Context, Goal, Action, <PERSON><PERSON>rio, Task", "takeTooltip": "Task, Action, Expectations, Result", "painTooltip": "Problem, Action, Information, Next step", "createTooltip": "Character, Request, Example, Adaptation, Output type, Add-ons", "applyFrameworks": "Apply different prompt frameworks using AI", "defaultGoogle": "<PERSON><PERSON><PERSON> (Google)", "findPrompts": "Find prompts in the library", "yourCurrentCreditBalance": "Your current credit balance", "comparing": "Comparing...", "cancelAndClose": "Cancel and Close", "insert": "Insert", "addToMyPrompts": "Add to My Prompts", "findInLibrary": "Find in Library", "close": "Close", "discoverPower": "Discover the Power of AI Prompt Lab", "revolutionaryTool": "AI Prompt Lab is a revolutionary tool designed to streamline your creative process when working with AI image generation tools. As an extension of your browser, it seamlessly integrates with your workflow, providing powerful capabilities for generating prompts at your fingertips.", "keyFeatures": "Key features of AI Prompt Lab:", "intelligentGeneration": "Intelligent prompt generation based on your input", "customizableOptions": "Customizable options to fine-tune your prompts", "platformIntegration": "Integration with popular AI image generation platforms", "userFriendly": "User-friendly interface for effortless prompt creation", "continuousUpdates": "Continuous updates improve prompt quality and relevance", "creativeProfessional": "Regardless of whether you are an artist, designer, or creative professional, AI Prompt Lab gives you the ability to discover new levels of creativity and efficiency in your AI image generation projects.", "supportedPlatforms": "Supported AI platforms:", "notEnoughCredits": "Not enough credits.", "invalidResponse": "Received invalid response from server.", "errorOccurred": "An error occurred while processing the request.", "comparisonFailed": "Comparison failed or was interrupted.", "comparisonUnavailable": "Comparison unavailable.", "errorLoadingComparison": "Error loading comparison.", "untitledPrompt": "Untitled prompt", "cost": "Cost", "credits": "credits", "credit": "credit", "costWithCredits": "Cost: 0 credits | Cost: {n} credit | Cost: {n} credits", "noResults": "No results"}, "subscriptionDialog": {"title": "Subscription - AI Prompt Lab", "tabs": {"plans": "Plans", "history": "History", "manageTeam": "Manage Team", "teams": "Teams"}, "current": "Current", "premiumActivated": "Premium activated", "signInToUse": "Sign in to use", "teamSize": "Team size:", "buyNow": "Buy now", "signInToBuy": "Sign in to buy", "plans": {"features": {"promptBars": "5 Prompt bars", "unlimitedPromptsPerBar": "Unlimited prompts per bar", "unlimitedBars": "Unlimited prompt bars", "credits": "{credits} Credits per month", "promptFolders": "Prompt folders", "copyPrompts": "Copy prompts", "accessToLibrary": "Access to library of prompts", "premiumAccess": "Access to premium prompts", "manageTeam": "Manage team"}, "team": {"title": "Team"}, "free": {"title": "Free"}, "premium": {"title": "Premium"}, "standard": {"title": "Standard"}}, "coupon": {"firstTimeOnly": "This coupon is only available for first time transactions", "discount": "{percent}% off of the first {interval} with the {code} coupon", "applied": "{code} applied successfully", "note": "* Coupons could be also applied during the payment process", "placeholder": "Enter your Coupon Code", "apply": "Apply", "errors": {"invalid": "Invalid coupon code", "alreadyUsed": "Coupon code already used"}}, "subscription": {"endsOn": "Subscription ends on {date}", "manage": "Manage Subscription"}, "team": {"members": "Members ({current}/{max})", "invitePlaceholder": "Invite existing or new users", "invite": "Invite", "email": "Email", "actions": "Actions", "owner": "Owner", "remove": "Remove"}, "close": "Close", "intervals": {"monthly": "Monthly", "annually": "Annual"}, "interval": {"month": "Month", "year": "Year"}, "history": {"start": "Start", "end": "End", "price": "Price", "noData": "No data"}, "priceFormat": "${price}{interval}"}, "componentControlBar": {"hide": "<PERSON>de", "show": "Show", "loading": "Loading...", "error": "Error", "moveUp": "Move up", "moveDown": "Move down", "profile": {"default": "<PERSON><PERSON><PERSON>", "label": "Profile:", "add": "Add", "overwrite": "Overwrite", "delete": "Delete", "save": "Save", "cancel": "Cancel"}, "dialog": {"saveProfile": "Save profile", "profileName": "Profile name:", "profileNamePlaceholder": "Enter profile name..."}}, "themeSwitcher": {"light": "Light", "dark": "Dark"}, "profileSwitcher": {"tooltip": "Switch Profile", "default": "<PERSON><PERSON><PERSON>", "prompt": "Prompt State", "search": "Search State"}, "profilePopup": {"title": "Select Profile", "close": "Close", "profileList": "Available profiles", "keyboardHints": {"navigate": "↑↓ Navigate", "select": "Enter Select", "close": "Esc Close"}}, "myAIFavorites": {"title": {"withTags": "Favorites with tags", "default": "Favorite AI tools"}, "editMode": {"info": "Edit mode: drag to reorder, gear to configure"}, "filtering": {"info": "Filtering favorites:"}, "empty": {"withTags": "No favorites with selected tags", "default": "No favorite tools", "noTagsMessage": "None of your favorite tools contain tags:", "addFavoritesMessage": "Click the heart icon on AI tool cards to add them to favorites."}, "actions": {"clearFilters": "Clear filters", "browseTools": "Browse AI tools"}, "tooltips": {"clearFilters": "Clear filters", "editMode": "Edit favorites", "finishEdit": "Finish editing"}, "config": {"title": "Configure card", "name": "Name:", "namePlaceholder": "Enter tool name", "url": "URL:", "urlPlaceholder": "Enter URL address"}}, "siteCard": {"config": {"tooltip": "Configure card"}}, "sitesGrid": {"tabs": {"searchAI": "Search AI", "myAI": "My AI"}, "loading": {"sites": "Loading AI tools...", "firebase": "Loading from Firebase (real-time data)", "localStorage": "Loading from local storage (offline data)"}, "status": {"firebaseConnected": "Connected to Firebase", "realTimeUpdates": "Real-time updates enabled", "live": "LIVE", "usingFallback": "Using Static Data", "staticData": "Loading from static fallback data", "offline": "OFFLINE"}, "error": {"loadFailed": "Failed to load AI tools"}, "results": {"found": "Found {count} of {total} AI tools"}, "empty": {"noResults": "No AI tools found matching the selected criteria."}, "actions": {"clearFilters": "Clear filters", "open": "Open", "openNewTab": "New tab", "openInCurrentTab": "Open in this tab", "openInNewTab": "Open in new tab"}, "viewToggle": {"switchToList": "Switch to list view", "switchToCard": "Switch to card view"}}, "tagFilter": {"actions": {"clearAll": "Clear all"}}, "searchInput": {"placeholder": "Search AI tools or tags..."}, "textField": {"placeholder": "Enter multi-line text...", "insertTooltip": "Insert content", "insertAriaLabel": "Insert content into text field", "copyTooltip": "Copy to clipboard", "copyAriaLabel": "Copy content to clipboard", "copySuccess": "Copied!", "copyDisabled": "No text to copy", "copySuccessMessage": "Text copied to clipboard", "copyErrorMessage": "Failed to copy text", "insertSuccessExternal": "Content inserted into page text field", "insertSuccessInternal": "Content inserted into text field", "insertErrorExternal": "Failed to automatically insert content into external text field, please click the text field manually first", "insertEmptyWarning": "Text field is empty - no content to insert", "insertCurrentContentInfo": "Content from this field was inserted into external text field"}, "tabNavigation": {"reorder": {"tooltip": "Click to change tab order"}}, "menuHideControl": {"dialog": {"title": "AI Tools & Sites"}, "collapseButton": {"expand": "Click to expand or drag to move", "hide": "Click to hide"}}, "menuButtons": {"show": {"tooltip": "Show menu"}, "hide": {"tooltip": "Hide menu"}}, "appControlButtons": {"toggleMainApp": {"tooltip": "Toggle Main Menu"}, "togglePromptEnhancement": {"tooltip": "Toggle Prompt Enhancement"}, "toggleChromePanel": {"close": "Close Chrome Sidebar", "open": "Open Chrome Sidebar"}, "browseAITools": {"tooltip": "Browse AI Tools & Sites"}, "openPluginConfig": {"tooltip": "Open plugin configuration"}, "openLocalStoragePanel": {"tooltip": "Manage localStorage data"}, "settingsMenu": {"tooltip": "Settings", "pluginConfiguration": "Plugin Configuration", "localStorageManagement": "localStorage Management"}}, "sidePanel": {"textFieldPlaceholder": "Enter text to test copy functionality...", "addToFavorites": "Add to favorites", "removeFromFavorites": "Remove from favorites"}, "sidePanelPopup": {"title": "Welcome to AI Prompt Lab", "welcomeTitle": "Welcome to Your AI Assistant!", "welcomeDescription": "Discover the power of AI-driven prompt management and enhance your productivity with our comprehensive toolkit.", "featuresTitle": "What you can do:", "feature1": "Create and manage custom AI prompts", "feature2": "Access a library of pre-built prompts", "feature3": "Integrate with popular AI platforms", "feature4": "Organize your workflow efficiently", "settingsTitle": "Customize your experience:", "settingsDescription": "Configure these essential settings to personalize your AI assistant interface and optimize your workflow for maximum productivity.", "themeLabel": "Theme", "languageLabel": "Language", "profileLabel": "Profile", "expansionTip": "💡 Tip: You can expand or resize the side panel by dragging its edges for better readability and an improved user experience.", "dontShowAgain": "Don't show this again", "dismiss": "<PERSON><PERSON><PERSON>", "getStarted": "Get Started"}, "localStoragePanel": {"title": "localStorage Management", "tabs": {"applicationState": "Application State", "userPreferences": "User Preferences", "userData": "User Data", "componentState": "Component State", "byComponents": "By Components", "allItems": "All Items", "aiManagement": "AI Management"}, "search": {"placeholder": "Search localStorage keys..."}, "table": {"key": "Key", "description": "Description", "value": "Value", "type": "Type", "size": "Size", "actions": "Actions"}, "actions": {"view": "View", "delete": "Delete", "export": "Export All", "import": "Import", "clearAll": "Clear All", "toggleTheme": "Toggle Theme", "switchLanguage": "Switch Language"}, "controls": {"language": {"tooltip": "Switch Language", "english": "English", "polish": "Polish"}, "theme": {"tooltip": "Toggle Dark/Light Theme", "light": "Light Theme", "dark": "Dark Theme"}}, "confirmations": {"deleteItem": "Are you sure you want to delete this item?", "clearAll": "Are you sure you want to clear all localStorage data? This action cannot be undone.", "importData": "Are you sure you want to import data? Existing data may be overwritten."}, "messages": {"itemDeleted": "Item has been deleted", "allCleared": "All data has been cleared", "exportSuccess": "Data has been exported", "importSuccess": "Data has been imported", "importError": "Error importing data", "invalidJson": "Invalid JSON format", "noData": "No localStorage data"}, "types": {"string": "String", "object": "Object", "array": "Array", "number": "Number", "boolean": "Boolean"}, "empty": {"title": "No localStorage data", "description": "No localStorage data found. Data will appear here as you use the application."}, "viewDialog": {"title": "View localStorage Item", "key": "Key:", "value": "Value:", "type": "Type:", "size": "Size:"}, "descriptions": {"activeTab": "Currently active tab in the main application interface", "All": "Cached data for the 'All' tab including selected prompt bar", "Popular": "Cached data for the 'Popular' tab including selected prompt bar", "Public": "Cached data for the 'Public' tab including selected prompt bar", "My Prompts": "Cached data for the 'My Prompts' tab including selected prompt bar", "Library": "Cached data for the 'Library' tab including selected prompt bar", "Favorites": "Cached data for the 'Favorites' tab including selected prompt bar", "lang": "User's preferred language setting for the application", "barLangPopular": "Language filter selection for the Popular tab", "barLangLibrary": "Language filter selection for the Library tab", "barSortFieldName": "Field name used for sorting bars and prompts", "barSortDirection": "Sort direction (ascending or descending)", "aiPromptManager_viewMode": "View mode preference (card or list view)", "aiPromptManager_tabOrder": "Custom order of tabs in the AI tools interface", "ai-tools-favorites": "User's collection of favorite AI tools and sites", "ai-tools-myai": "User's personal AI tools collection", "component_state": "Component state persistence data", "menuControlState": "Site-specific menu visibility and control settings", "version": "Data version for migration and compatibility", "theme": "Theme-related preferences and settings", "view": "View mode and display preferences", "sort": "Sorting preferences and configurations", "locale": "Localization and language settings", "default": "Application data storage"}}, "aiManagement": {"addForm": {"title": "Add New AI Tool"}, "form": {"name": "Name", "namePlaceholder": "Enter AI tool name", "website": "Website URL", "websitePlaceholder": "https://example.com", "tags": "Tags", "tagsPlaceholder": "Select or create tags", "description": "Description", "descriptionPlaceholder": "Enter description (optional)"}, "table": {"title": "AI Tools", "name": "Name", "website": "Website", "tags": "Tags", "description": "Description", "dateAdded": "Date Added", "actions": "Actions", "empty": "No AI tools found"}, "search": {"placeholder": "Search AI tools..."}, "actions": {"add": "<PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "reset": "Reset"}, "editDialog": {"title": "Edit AI <PERSON>l"}, "confirmations": {"delete": "Are you sure you want to delete this AI tool?"}, "messages": {"addSuccess": "AI tool added successfully", "updateSuccess": "AI tool updated successfully", "deleteSuccess": "AI tool deleted successfully", "invalidUrl": "Please enter a valid URL"}}}