console.info('chrome-ext template-vue-js background script')

chrome.storage.session.setAccessLevel({ accessLevel: 'TRUSTED_AND_UNTRUSTED_CONTEXTS' })

let sidePanelState = {
  isOpen: false,
  windowId: null,
  lastHeartbeat: null,
}

let sidePanelOpenWindows = new Set()

const notifyTabsOfStateChange = (isOpen) => {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id) {
        chrome.tabs
          .sendMessage(tab.id, {
            action: 'sidePanelStateChanged',
            isOpen: isOpen,
          })
          .catch(() => {})
      }
    })
  })
}

async function ensureOffscreenDocument() {
  const existingOffscreen = await chrome.offscreen.hasDocument()

  if (!existingOffscreen) {
    await chrome.offscreen.createDocument({
      url: chrome.runtime.getURL('offscreen.html'),
      reasons: [chrome.offscreen.Reason.DOM_SCRAPING],
      justification: 'authentication',
    })
  }
}

chrome.runtime.onInstalled.addListener(({ reason }) => {
  if (reason == chrome.runtime.OnInstalledReason.INSTALL) {
    chrome.tabs.create({
      url: 'https://ai-promptlab.com/app/',
      active: true,
    })
  }

  chrome.sidePanel
    .setPanelBehavior({ openPanelOnActionClick: true })
    .catch((error) => console.error('Error setting side panel behavior:', error))
})

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'loginWithGoogle') {
    ensureOffscreenDocument().then(() => {
      chrome.runtime.sendMessage({ action: 'loginWithGoogle', target: 'offscreen' }, (response) => {
        sendResponse(response)
      })
    })
    return true // Keep the message channel open for async response
  }

  if (message.action === 'openSidePanel') {
    if (sender.tab && sender.tab.windowId) {
      chrome.sidePanel
        .open({ windowId: sender.tab.windowId })
        .then(() => {
          sidePanelOpenWindows.add(sender.tab.windowId)
          sidePanelState.isOpen = true
          sidePanelState.windowId = sender.tab.windowId
          sidePanelState.lastHeartbeat = Date.now()
          sendResponse({ success: true, isOpen: true })

          notifyTabsOfStateChange(true)
        })
        .catch((error) => {
          sendResponse({ success: false, error: error.message })
        })
    } else {
      sendResponse({ success: false, error: 'No tab information' })
    }
    return true // Keep the message channel open for async response
  }

  if (message.action === 'closeSidePanel') {
    console.log('Background: Received closeSidePanel request')

    // Try to close the side panel using Chrome API
    if (sender.tab && sender.tab.windowId) {
      // First try to send close message to side panel if it exists
      const closeSidePanelPromise = new Promise((resolve) => {
        chrome.runtime.sendMessage({ action: 'closeSidePanelWindow' }, (response) => {
          if (chrome.runtime.lastError) {
            console.log(
              'Background: Side panel not available for close message:',
              chrome.runtime.lastError.message,
            )
          } else {
            console.log('Background: Close message sent to side panel')
          }
          resolve()
        })
      })

      closeSidePanelPromise.then(() => {
        // Update state regardless of message success
        if (sidePanelOpenWindows.has(sender.tab.windowId)) {
          sidePanelOpenWindows.delete(sender.tab.windowId)
        }

        sidePanelState.isOpen = sidePanelOpenWindows.size > 0
        if (!sidePanelState.isOpen) {
          sidePanelState.windowId = null
          sidePanelState.lastHeartbeat = null
        }

        sendResponse({ success: true, isOpen: false })

        // Notify all tabs about state change
        notifyTabsOfStateChange(false)
      })
    } else {
      sendResponse({ success: false, error: 'No tab information' })
    }

    return true // Keep the message channel open for async response
  }

  if (message.action === 'checkIfSidePanelClosed') {
    if (sender.tab) {
      chrome.sidePanel
        .getOptions({ tabId: sender.tab.id })
        .then((options) => {
          const isOpen = options.enabled !== false
          sendResponse({ success: true, isOpen })
        })
        .catch((error) => {
          sendResponse({ success: false, isOpen: false })
        })
    } else {
      sendResponse({ success: false, isOpen: false })
    }
    return true // Keep the message channel open for async response
  }

  if (message.action === 'getSidePanelStatus') {
    sendResponse({ success: true, isOpen: sidePanelState.isOpen })
    return true // Keep the message channel open for async response
  }

  if (message.action === 'sidePanelOpened') {
    sidePanelState.isOpen = true
    sidePanelState.lastHeartbeat = Date.now()
    if (sender.tab && sender.tab.windowId) {
      sidePanelOpenWindows.add(sender.tab.windowId)
      sidePanelState.windowId = sender.tab.windowId
    }

    notifyTabsOfStateChange(true)
    sendResponse({ success: true })
  }

  if (message.action === 'sidePanelClosed') {
    sidePanelState.isOpen = false
    sidePanelState.windowId = null
    sidePanelState.lastHeartbeat = null
    sidePanelOpenWindows.clear()

    // Notify all tabs about state change
    notifyTabsOfStateChange(false)
    sendResponse({ success: true })
  }

  if (message.action === 'sidePanelHeartbeat') {
    sidePanelState.lastHeartbeat = Date.now()
    if (!sidePanelState.isOpen) {
      sidePanelState.isOpen = true
      chrome.tabs
        .query({})
        .then((tabs) => {
          tabs.forEach((tab) => {
            if (tab.id) {
              chrome.tabs
                .sendMessage(tab.id, {
                  action: 'sidePanelStateChanged',
                  isOpen: true,
                })
                .catch(() => {
                  // Ignore errors
                })
            }
          })
        })
        .catch(console.error)
    }
    sendResponse({ success: true })
  }

  if (message.action === 'themeChanged') {
    chrome.runtime
      .sendMessage({
        action: 'updateThemeFromMainPage',
        isDarkTheme: message.isDarkTheme,
      })
      .catch(() => {
        // Ignore errors
      })
    sendResponse({ success: true })
  }

  if (message.action === 'requestCurrentTheme') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'getCurrentTheme' }, (response) => {
          if (response && typeof response.isDarkTheme === 'boolean') {
            chrome.runtime
              .sendMessage({
                action: 'updateThemeFromMainPage',
                isDarkTheme: response.isDarkTheme,
              })
              .catch(() => {
                console.log('Background: side panel already closed')
              })
          }
        })
      }
    })
    sendResponse({ success: true })
  }

  if (message.action === 'getCurrentTabInfo') {
    console.log('Background: getCurrentTabInfo request received')
    console.log('Background: sidePanelState:', sidePanelState)
    console.log('Background: isDevelopmentMode:', message.isDevelopmentMode)
    console.log('Background: currentLocation:', message.currentLocation)

    // In development mode (local file), we always want to get the active browser tab
    // regardless of side panel state, because the side panel is just a local HTML file
    if (message.isDevelopmentMode) {
      console.log('Background: Development mode detected, getting active browser tab')

      // Get all tabs and find the best content tab (not local files or extensions)
      chrome.tabs.query({}, (allTabs) => {
        console.log('Background: Total tabs found in development mode:', allTabs.length)

        // Filter to only web content tabs (HTTP/HTTPS)
        const webTabs = allTabs.filter((tab) => {
          const isWebUrl = tab.url.startsWith('http://') || tab.url.startsWith('https://')
          const isNotExtension = !tab.url.includes('chrome-extension://')
          const isNotLocalFile = !tab.url.startsWith('file://')

          console.log(`Background: Dev mode checking tab ${tab.url}:`, {
            isWebUrl,
            isNotExtension,
            isNotLocalFile,
            active: tab.active,
            passes: isWebUrl && isNotExtension && isNotLocalFile,
          })

          return isWebUrl && isNotExtension && isNotLocalFile
        })

        console.log('Background: Found', webTabs.length, 'valid web tabs in development mode')

        if (webTabs.length === 0) {
          console.log('Background: No valid web tabs found in development mode')
          sendResponse({
            success: false,
            error: 'No valid web tabs found in development mode',
          })
          return
        }

        // Prefer active tabs, then most recently accessed
        const sortedWebTabs = webTabs.sort((a, b) => {
          if (a.active && !b.active) return -1
          if (!a.active && b.active) return 1
          return (b.lastAccessed || 0) - (a.lastAccessed || 0)
        })

        const selectedTab = sortedWebTabs[0]

        console.log('Background: Selected tab in development mode:', {
          url: selectedTab.url,
          title: selectedTab.title,
          id: selectedTab.id,
          active: selectedTab.active,
        })

        sendResponse({
          success: true,
          data: {
            url: selectedTab.url,
            title: selectedTab.title,
            id: selectedTab.id,
          },
        })
      })

      return true // Keep message channel open for async response
    }

    // Try multiple approaches in sequence for production extension mode

    // Approach 1: Try to get the last focused window and its active tab
    chrome.windows.getLastFocused({ populate: true }, (lastFocusedWindow) => {
      console.log('Background: Last focused window:', lastFocusedWindow.id)
      console.log('Background: Tabs in last focused window:', lastFocusedWindow.tabs.length)

      // Find active tab in the last focused window that's not an extension
      const activeTabInFocusedWindow = lastFocusedWindow.tabs.find(
        (tab) =>
          tab.active &&
          (tab.url.startsWith('http://') || tab.url.startsWith('https://')) &&
          !tab.url.includes('chrome-extension://') &&
          !tab.url.includes('side_panel.html'),
      )

      if (activeTabInFocusedWindow) {
        console.log('Background: Found active content tab in focused window:', {
          url: activeTabInFocusedWindow.url,
          title: activeTabInFocusedWindow.title,
          id: activeTabInFocusedWindow.id,
        })

        sendResponse({
          success: true,
          data: {
            url: activeTabInFocusedWindow.url,
            title: activeTabInFocusedWindow.title,
            id: activeTabInFocusedWindow.id,
          },
        })
        return
      }

      console.log('Background: No active content tab in focused window, trying all tabs approach')

      // Approach 2: Get all tabs and find the best content tab
      chrome.tabs.query({}, (allTabs) => {
        console.log('Background: Total tabs found:', allTabs.length)

        // Log all tabs for debugging
        allTabs.forEach((tab, index) => {
          console.log(
            `Background: Tab ${index + 1}: ${tab.active ? '[ACTIVE]' : '[inactive]'} ${
              tab.url
            } - "${tab.title}" (Window: ${tab.windowId})`,
          )
        })

        // Filter to only content tabs (not extension or browser internal)
        const contentTabs = allTabs.filter((tab) => {
          const isValidUrl = tab.url.startsWith('http://') || tab.url.startsWith('https://')
          const isNotExtension = !tab.url.includes('chrome-extension://')
          const isNotSidePanel = !tab.url.includes('side_panel.html')
          const isNotInternal =
            !tab.url.startsWith('chrome://') &&
            !tab.url.startsWith('moz-extension://') &&
            !tab.url.startsWith('about:')

          const passes = isValidUrl && isNotExtension && isNotSidePanel && isNotInternal

          if (!passes) {
            console.log(`Background: Filtering out tab ${tab.url}:`, {
              isValidUrl,
              isNotExtension,
              isNotSidePanel,
              isNotInternal,
            })
          }

          return passes
        })

        console.log('Background: Found', contentTabs.length, 'valid content tabs')

        if (contentTabs.length === 0) {
          console.log('Background: No valid content tabs found')
          sendResponse({
            success: false,
            error: 'No valid content tabs found',
          })
          return
        }

        // Sort content tabs by priority:
        // 1. Active tabs first
        // 2. Then by most recent access time
        const sortedTabs = contentTabs.sort((a, b) => {
          // Active tabs get priority
          if (a.active && !b.active) return -1
          if (!a.active && b.active) return 1

          // Then sort by last accessed time (most recent first)
          return (b.lastAccessed || 0) - (a.lastAccessed || 0)
        })

        const selectedTab = sortedTabs[0]

        console.log('Background: Selected tab:', {
          url: selectedTab.url,
          title: selectedTab.title,
          id: selectedTab.id,
          active: selectedTab.active,
          lastAccessed: selectedTab.lastAccessed,
          windowId: selectedTab.windowId,
        })

        sendResponse({
          success: true,
          data: {
            url: selectedTab.url,
            title: selectedTab.title,
            id: selectedTab.id,
          },
        })
      })
    })

    return true // Keep message channel open for async response
  }

  if (message.action === 'testMessage') {
    console.log('Background: Received testMessage from Side Panel:', message.timestamp)
    sendResponse({
      success: true,
      message: 'Background received test message',
      timestamp: Date.now(),
      sidePanelState: sidePanelState,
    })
  }

  // Handle text field operations from side panel
  if (
    message.action === 'insertTextIntoFocusedField' ||
    message.action === 'findAvailableTextFields' ||
    message.action === 'insertTextIntoFirstAvailableField'
  ) {
    console.log('Background: routing text field operation to content script:', message.action)

    // Find the active tab and send message to content script
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
          if (chrome.runtime.lastError) {
            console.log('Background: content script not available:', chrome.runtime.lastError)
            sendResponse({
              success: false,
              error: 'Content script not available on this page',
              insertedIntoExternal: false,
            })
          } else {
            console.log('Background: content script response:', response)
            sendResponse(response)
          }
        })
      } else {
        sendResponse({
          success: false,
          error: 'No active tab found',
          insertedIntoExternal: false,
        })
      }
    })
    return true // Keep message channel open for async response
  }

  // --- SYNC LANGUAGE BETWEEN CONTEXTS ---
  if (message.type === 'LANGUAGE_CHANGED') {
    if (message.source === 'side_panel') {
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach((tab) => {
          chrome.tabs.sendMessage(tab.id, {
            type: 'LANGUAGE_CHANGED_BROADCAST',
            lang: message.lang,
          })
        })
      })
    } else {
      chrome.runtime.sendMessage({ type: 'LANGUAGE_CHANGED_BROADCAST', lang: message.lang })
    }
    return
  }

  // --- SYNC PROFILE BETWEEN CONTEXTS ---
  if (message.type === 'PROFILE_CHANGED') {
    if (message.source === 'side_panel') {
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach((tab) => {
          chrome.tabs.sendMessage(tab.id, {
            type: 'PROFILE_CHANGED_BROADCAST',
            profileId: message.profileId,
          })
        })
      })
    } else {
      chrome.runtime.sendMessage({
        type: 'PROFILE_CHANGED_BROADCAST',
        profileId: message.profileId,
      })
    }
    return
  }
})

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (tab.url.startsWith('https://accounts.google.com/o/oauth2/auth/')) {
    chrome.windows.update(tab.windowId, { focused: true })
    return
  }
})

export {}
