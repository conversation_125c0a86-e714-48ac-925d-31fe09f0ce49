import { getStripePayments } from '@invertase/firestore-stripe-payments'
import { getApps, initializeApp } from 'firebase/app'
import { connectAuthEmulator, getAuth } from 'firebase/auth'
import {
  arrayRemove,
  arrayUnion,
  collection,
  connectFirestoreEmulator,
  deleteField,
  doc,
  getDoc,
  getDocs,
  getFirestore,
  limit,
  query,
  setDoc,
  Timestamp,
  updateDoc,
  where,
} from 'firebase/firestore'
import { connectFunctionsEmulator, getFunctions } from 'firebase/functions'
import stripe from 'stripe'

import { setToPluginStorageLocal, updateMyPromptsBarKeyInLocalStorage } from './localStorage'

export const firebaseApp =
  getApps().length === 0
    ? initializeApp({
        apiKey: import.meta.env.VITE_FIREBASE_APIKEY,
        authDomain: import.meta.env.VITE_FIREBASE_AUTHDOMAIN,
        projectId: import.meta.env.VITE_FIREBASE_PROJECTID,
        storageBucket: import.meta.env.VITE_FIREBASE_STORAGEBUCKET,
        messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGINGSENDERID,
        appId: import.meta.env.VITE_FIREBASE_APPID,
        measurementId: import.meta.env.VITE_MEASUREMENTID,
      })
    : getApps()[0]

const stripeApiKey = import.meta.env.VITE_STRIPE_API_KEY
const stripeApi = stripe(stripeApiKey)

export const db = getFirestore(firebaseApp)
const auth = getAuth(firebaseApp)

const shouldUseEmulators = import.meta.env.VITE_USE_EMULATORS === 'true'
console.log('shouldUseEmulators:', shouldUseEmulators, process.env.NODE_ENV)
if (process.env.NODE_ENV === 'development' && shouldUseEmulators) {
  const functions = getFunctions(firebaseApp)
  connectFunctionsEmulator(functions, 'localhost', 5001)
  connectFirestoreEmulator(db, 'localhost', 8080)
  connectAuthEmulator(auth, 'http://localhost:9099')
  console.log('Emulators connected')
} else {
  console.log('Emulators not connected')
}

export const barsRef = collection(db, 'bars')
export const usersRef = collection(db, 'users')
export const userBarsRef = collection(db, 'user_bars')
export const barInfoRef = collection(db, 'bar_info')
export const allBarsRef = doc(barInfoRef, 'all')
export const popularBarsRef = doc(barInfoRef, 'popular')
export const publicBarsRef = doc(barInfoRef, 'public')
export const libraryBarsRef = doc(barInfoRef, 'library')
export const teamBarsRef = collection(db, 'team_bars')

export const getPopularBarsRefForLang = (lang) => {
  return doc(barInfoRef, lang === 'en' ? 'popular' : `popular_${lang}`)
}

export const getLibraryBarsRefForLang = (lang) => {
  return doc(barInfoRef, lang === 'en' ? 'library' : `library_${lang}`)
}

export const settingsRef = collection(db, 'settings')
export const chatsRef = collection(db, 'chats')
export const customersRef = collection(db, 'customers')
export const emailsRef = collection(db, 'emails')

export async function tryCustomCouponCode(promoCode, userId) {
  const codes = (await getDoc(doc(settingsRef, 'codes'))).data()
  const code = codes[promoCode]
  if (!code) return null

  await setUserData(userId, { coupon: promoCode })

  return { code: promoCode, isValidCustomCode: true }
}

export async function getStripePromotionCode(promoCode) {
  const response = await stripeApi.promotionCodes.list({
    active: true,
    code: promoCode,
    expand: ['data.coupon.applies_to'],
  })
  return response.data[0]
}

export const ROLES = {
  ADMIN: 'Admin',
  SUPER_ADMIN: 'Super Admin',
}

export function getUserBarsRef(userId) {
  return doc(userBarsRef, userId)
}

export function getTeamBarsRef(teamId) {
  return doc(teamBarsRef, teamId)
}

export const payments = getStripePayments(firebaseApp, {
  productsCollection: 'products',
  customersCollection: 'customers',
})

export const getAllBars = async () => {
  const allBarsByUserId = (await getDoc(allBarsRef)).data()
  const allUserBars = []
  Object.entries(allBarsByUserId).forEach(([, userBarsById]) => {
    Object.entries(userBarsById).forEach(([barId, bar]) => {
      allUserBars.push({
        ...bar,
        id: barId,
      })
    })
  })
  return allUserBars
    .filter((bar) => bar.name)
    .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }))
}

export const getUserBars = async (userId) => {
  if (!userId) return []
  // console.log('getUserBars: ', userId)
  const userBarRef = getUserBarsRef(userId)
  const userBarsDoc = (await getDoc(userBarRef)).data() ?? {}
  const userBars = Object.entries(userBarsDoc)
    .map(([barId, bar]) => ({
      ...bar,
      id: barId,
    }))
    .filter((bar) => bar.name)
  // console.log('getUserBars', userBars)
  return userBars.sort((a, b) =>
    a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }),
  )
}

export const getTeamBars = async (teamId) => {
  // console.log('getTeamBars: ')
  const teamBarRef = getTeamBarsRef(teamId)
  const teamBarsDoc = (await getDoc(teamBarRef)).data() ?? {}
  const teamBars = Object.entries(teamBarsDoc)
    .map(([barId, bar]) => ({
      ...bar,
      id: barId,
    }))
    .filter((bar) => bar.name)
  // console.log('getTeamBars', teamBars)
  return teamBars.sort((a, b) =>
    a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }),
  )
}

export const isAdminOrSuperAdmin = (user) => {
  // console.log('isAdminOrSuperAdmin', user)
  if (!user) return false
  return user.role === ROLES.ADMIN || user.role === ROLES.SUPER_ADMIN
}

export async function editBar(bar, changesObject) {
  if (!bar?.id) return
  const barRef = doc(barsRef, bar.id)
  const editedAtDate = new Date()
  const editedAt = Timestamp.fromDate(editedAtDate)
  bar.editedAt = editedAt
  await setDoc(barRef, { ...changesObject, editedAt }, { merge: true })

  // console.log(editedAtDate.toUTCString() + ' Write-> barsRef: editBar')

  updateMyPromptsBarKeyInLocalStorage('editedAt', editedAt)
  setToPluginStorageLocal('editDateByBarId', { [bar.id]: editedAtDate.toISOString() }, true)
}

export async function syncBarProperty(bar, propertyName) {
  if (!bar?.id || !propertyName) return

  const changeObject = { [bar.id]: { [propertyName]: bar[propertyName] } }
  const userId = bar.owner.id

  await setDoc(allBarsRef, { [userId]: changeObject }, { merge: true })
  if (bar.isPublic) {
    await setDoc(publicBarsRef, changeObject, { merge: true })
  }
  if (bar.isPopular) {
    await setDoc(popularBarsRef, changeObject, { merge: true })
  }
  if (bar.isInLibrary) {
    await setDoc(libraryBarsRef, changeObject, { merge: true })
  }
  if (bar.teams && Array.isArray(bar.teams) && bar.teams.length > 0) {
    await Promise.all(
      bar.teams.map((teamId) => setDoc(getTeamBarsRef(teamId), changeObject, { merge: true })),
    )
  }

  const userBarRef = doc(userBarsRef, userId)
  await setDoc(userBarRef, changeObject, { merge: true })
}

export const syncBarWithPublicBars = async ({ bar, isPublic = true, isListed = false }) => {
  if (!bar?.id) return

  const {
    id,
    name = '',
    description = '',
    tags = [],
    icon = '',
    destination = '',
    createdAt = Timestamp.now(),
  } = bar

  await updateDoc(publicBarsRef, {
    [id]:
      isPublic && isListed
        ? {
            name,
            description,
            tags,
            icon,
            destination,
            createdAt: new Timestamp(createdAt.seconds, createdAt.nanoseconds).toDate(),
          }
        : deleteField(),
  })
  // console.log(new Date().toUTCString() + ' Write-> publicBarsRef: syncBarWithPublicBars')
}

export const syncBarWithPopularBars = async ({ bar, isPopular, language }) => {
  if (!bar?.id) return

  const {
    id,
    name = '',
    description = '',
    tags = [],
    icon = '',
    destination = '',
    createdAt = new Date(),
  } = bar

  // console.log('syncBarWithPopularBars', bar, isPopular, language)

  await updateDoc(getPopularBarsRefForLang(language), {
    [id]: isPopular
      ? {
          name,
          description,
          tags,
          icon,
          destination,
          createdAt,
        }
      : deleteField(),
  })
  // console.log(new Date().toUTCString() + ' Write-> popularBarsRef: syncBarWithPopularBars')
}

export const syncBarWithTeamBars = async ({ bar, teamId, shouldRemove = false }) => {
  if (!bar?.id) return

  const {
    id,
    name = '',
    description = '',
    tags = [],
    icon = '',
    destination = '',
    createdAt = new Date(),
  } = bar

  await setDoc(
    getTeamBarsRef(teamId),
    {
      [id]: shouldRemove
        ? deleteField()
        : { name, description, tags, icon, destination, createdAt },
    },
    { merge: true },
  )
}

export const syncBarWithUserBars = async ({ bar, userId, shouldRemove = false }) => {
  if (!bar?.id) return

  const {
    id,
    name = '',
    description = '',
    tags = [],
    icon = '',
    destination = '',
    createdAt = new Date(),
  } = bar

  const userBarRef = getUserBarsRef(userId)

  await setDoc(
    userBarRef,
    {
      [id]: shouldRemove
        ? deleteField()
        : {
            name,
            description,
            tags,
            icon,
            destination,
            createdAt,
          },
    },
    { merge: true },
  )

  await setDoc(
    allBarsRef,
    {
      [userId]: {
        [id]: shouldRemove
          ? deleteField()
          : {
              name,
              description,
              tags,
              icon,
              destination,
              createdAt,
            },
      },
    },
    { merge: true },
  )
}

export const syncBarWithLibraryBars = async ({ bar, isInLibrary, language }) => {
  if (!bar?.id) return

  const {
    id,
    name = '',
    description = '',
    tags = [],
    icon = '',
    destination = '',
    createdAt = new Date(),
  } = bar

  await updateDoc(getLibraryBarsRefForLang(language), {
    [id]: isInLibrary
      ? {
          name,
          description,
          tags,
          icon,
          destination,
          createdAt,
        }
      : deleteField(),
  })
  // console.log(new Date().toUTCString() + ' Write-> libraryBarsRef: syncBarWithLibraryBars')
}

export async function createOrEditChat({ prompt, answer, user }) {
  const link = location.href
  const q = query(chatsRef, where('link', '==', link), limit(1))
  const querySnapshot = await getDocs(q)
  // console.log(new Date().toUTCString() + ' Read-> chatsRef: createOrEditChat')
  const doc = querySnapshot.docs[0]
  const chatId = doc?.id
  // console.log('chatId', chatId)

  if (chatId) {
    await addChatHistory({ prompt, answer, chatId })
  } else {
    await createChat({ prompt, answer, user })
  }
}

export async function createChat({ prompt, answer, user }) {
  // console.log('createChat', { prompt, answer, user })

  const chatRef = doc(chatsRef)
  // console.log(new Date().toUTCString() + ' Read-> chatsRef: createChat')

  const createdAt = new Date()

  const chatDoc = {
    name: document.title,
    description: '',
    createdAt,
    editedAt: createdAt,
    domain: location.host,
    link: location.href,
    owner: doc(usersRef, user.value.uid),
    history: [
      {
        createdAt,
        prompt,
        answer,
      },
    ],
  }

  // console.log('chatDoc', chatDoc)
  await setDoc(chatRef, chatDoc)
  // console.log(new Date().toUTCString() + ' Write-> chatsRef: createChat')
}

export async function addChatHistory({ prompt, answer, chatId }) {
  // console.log('addChatHistory', { prompt, answer, chatId })
  const chatRef = doc(chatsRef, chatId)
  // console.log(new Date().toUTCString() + ' Read-> chatsRef: addChatHistory')

  const createdAt = new Date()

  const historyItem = {
    createdAt,
    prompt,
    answer,
  }

  await setDoc(chatRef, { history: arrayUnion(historyItem), editedAt: createdAt }, { merge: true })
  // console.log(new Date().toUTCString() + ' Write-> chatsRef: addChatHistory')
}

export async function setUserData(userId, data) {
  // console.log('setUserData', userId, data)
  const userRef = doc(usersRef, userId)
  // // console.log(new Date().toUTCString() + ' Read-> usersRef: setUserData')

  await setDoc(userRef, data, { merge: true })
  // console.log(new Date().toUTCString() + ' Write-> usersRef: setUserData')
}

export async function inviteWithSubscription({ user, emails = [], teamId }) {
  const { uid: userId, displayName, email } = user
  const subscriberName = `${displayName} (${email})`
  const emailRef = doc(emailsRef)

  const userQuery = query(usersRef, where('email', 'in', emails))
  const userQuerySnapshot = await getDocs(userQuery)
  const userDocs = userQuerySnapshot.docs

  for (const userDoc of userDocs) {
    const updateData = {
      subscription: { plan: 'Team', maxMembers: 1 },
      hasTeamSubscription: true,
    }

    if (teamId) {
      updateData.teams = arrayUnion(doc(usersRef, teamId))
    }

    await setDoc(doc(usersRef, userDoc.id), updateData, { merge: true })
  }

  await setDoc(emailRef, {
    bcc: emails,
    template: {
      name: 'subscription_invite',
      data: { name: 'User', subscriberName },
    },
  })

  const userRef = doc(usersRef, userId)
  await setDoc(userRef, { subscription: { members: arrayUnion(...emails) } }, { merge: true })
}

export async function removeMember(userId, email, teamId) {
  const userRef = doc(usersRef, userId)

  await setDoc(userRef, { subscription: { members: arrayRemove(email) } }, { merge: true })

  const userQuery = query(usersRef, where('email', '==', email))
  const userQuerySnapshot = await getDocs(userQuery)
  const userDoc = userQuerySnapshot.docs[0]

  if (!userDoc) return

  const updateData = {
    subscription: { plan: '', maxMembers: 0 },
    hasTeamSubscription: false,
  }

  if (teamId) {
    updateData.teams = arrayRemove(doc(usersRef, teamId))
  }

  await setDoc(doc(usersRef, userDoc.id), updateData, { merge: true })
}

export async function setUserCredits(userId, creditsOwned) {
  if (!userId) return 0

  const userDocRef = doc(usersRef, userId)
  await setDoc(userDocRef, { creditsOwned }, { merge: true })
  // console.log(new Date().toUTCString() + ' Write-> usersRef: setUserCredits')

  return creditsOwned
}

export async function updateBarLanguage(barId, lang) {
  if (!barId || !lang) return
  const barRef = doc(barsRef, barId)
  await updateDoc(barRef, { lang })
}

export async function setCreditsResetDate(newUserDoc, creditsResetDate) {
  // console.log('-> setCreditsResetDate', newUserDoc, creditsResetDate)
  await setDoc(doc(usersRef, newUserDoc.id), { creditsResetDate }, { merge: true })
}

export async function getCopyToUserList() {
  const docSnap = await getDoc(doc(settingsRef, 'copyToUser'))
  const data = docSnap.data()
  return data?.users || []
}
