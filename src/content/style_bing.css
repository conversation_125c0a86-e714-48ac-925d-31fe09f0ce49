@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  .prompt-manager button:not(:hover),
  .prompt-manager [type='button']:not(:hover) {
    background-color: var(--el-button-bg-color, var(--el-color-transparent));
  }
  .prompt-manager input[type='text'] {
    color: var(--el-input-text-color, var(--el-text-color-regular));
    border: none;
  }
}
@layer components {
  *,
  ::before,
  ::after {
    box-sizing: initial;
  }
  :root {
    --shadowColor: #e4ecf9;
  }
  .dark {
    --shadowColor: var(--cib-color-background-surface-app-primary);
  }
  #_appBing {
    position: relative;
    margin-top: -40px;
    opacity: 1;
    transition: all 300ms ease-in-out 500ms;
    /* max-width: 1440px; */
    box-shadow: 0px 5px 20px 15px var(--shadowColor);
  }

  body:not(.b_sydConvMode) #_appBing {
    margin-top: 0;
    opacity: 0;
    pointer-events: none;
    height: 0;
    transition-delay: 0s;
  }

  cib-serp.cib-serp-main {
    height: calc(100% - 205px);
    top: 205px;
  }

  .chat-prompt-textarea {
    /* margin-top: 1em; */
  }
  .chat-prompt-button {
    /* transform: translateX(-1.2em); */
  }
}
