<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useSidePanelTheme } from '../composables/useSidePanelTheme'

// Use i18n for translations
const { t } = useI18n()

// Use the side panel theme composable
const { THEME_OPTIONS, selectedTheme, setTheme } = useSidePanelTheme()

// Handle theme change
const handleThemeChange = (value) => {
  setTheme(value)
}

// Computed property for theme options with translated labels
const translatedThemeOptions = computed(() => {
  return THEME_OPTIONS.map((option) => ({
    ...option,
    label: t(`themeSwitcher.${option.value}`),
  }))
})

// Computed property for current theme option
const currentThemeOption = computed(() => {
  return (
    translatedThemeOptions.value.find((option) => option.value === selectedTheme.value) ||
    translatedThemeOptions.value[0]
  )
})
</script>

<template>
  <el-select
    v-model="selectedTheme"
    class="theme-select"
    size="small"
    @change="handleThemeChange"
    style="min-width: 79px"
    popper-class="theme-select-dropdown"
    data-testid="theme-selector"
  >
    <el-option
      v-for="theme in translatedThemeOptions"
      :key="theme.value"
      :label="theme.label"
      :value="theme.value"
      data-testid="theme-selector-option"
    >
      <div class="theme-option">
        <el-icon class="theme-icon">
          <component :is="theme.icon" />
        </el-icon>
        <span class="theme-label">{{ theme.label }}</span>
      </div>
    </el-option>
  </el-select>
</template>

<style scoped>
.theme-select :deep(.el-input__wrapper) {
  padding: 0 8px;
}

.theme-select :deep(.el-select__caret) {
  margin-left: 4px;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.theme-label {
  flex: 1;
}

/* Dropdown styling consistency */
:deep(.theme-select-dropdown) {
  z-index: 3000;
}

/* Selected option styling */
.theme-select :deep(.el-input__inner) {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
