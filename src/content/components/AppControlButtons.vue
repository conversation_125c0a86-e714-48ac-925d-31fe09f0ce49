<template>
  <div class="app-control-buttons" :class="{ 'dark-theme': isDarkTheme }">
    <button
      @click="$emit('toggleMainApp')"
      :class="['control-button', { active: mainAppVisible }]"
      :title="t('appControlButtons.toggleMainApp.tooltip')"
    >
      <el-icon class="button-icon">
        <Operation />
      </el-icon>
    </button>
    <button
      @click="$emit('toggleChatButton')"
      :class="['control-button', { active: chatButtonVisible }]"
      :title="t('appControlButtons.togglePromptEnhancement.tooltip')"
    >
      <el-icon class="button-icon">
        <MagicStick />
      </el-icon>
    </button>
    <button
      @click="$emit('openSitesGrid')"
      :class="['control-button', { active: sitesGridVisible }]"
      :title="t('appControlButtons.browseAITools.tooltip')"
    >
      <el-icon class="button-icon">
        <Grid />
      </el-icon>
    </button>

    <!-- Visual separator between functional groups -->
    <div class="control-separator"></div>

    <button
      @click="$emit('toggleChromePanel')"
      :class="['control-button', { active: sidePanelVisible }]"
      :title="
        sidePanelVisible
          ? t('appControlButtons.toggleChromePanel.close')
          : t('appControlButtons.toggleChromePanel.open')
      "
      data-testid="button-chrome-sidebar"
    >
      <el-icon class="button-icon">
        <Menu />
      </el-icon>
    </button>
    <!-- Settings Dropdown Menu - Only visible to admin users -->
    <el-popover
      v-if="isAdmin"
      placement="left"
      :width="280"
      trigger="click"
      :teleported="false"
      v-model:visible="settingsMenuVisible"
    >
      <template #reference>
        <button class="control-button" :title="t('appControlButtons.settingsMenu.tooltip')">
          <el-icon class="button-icon">
            <Setting />
          </el-icon>
        </button>
      </template>

      <div class="settings-menu">
        <el-button type="text" class="settings-menu-item" @click="handlePluginConfig">
          <el-icon><Setting /></el-icon>
          {{ t('appControlButtons.settingsMenu.pluginConfiguration') }}
        </el-button>

        <el-button type="text" class="settings-menu-item" @click="handleLocalStoragePanel">
          <el-icon><FolderOpened /></el-icon>
          {{ t('appControlButtons.settingsMenu.localStorageManagement') }}
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { FolderOpened, Grid, MagicStick, Menu, Operation, Setting } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

const { t } = useI18n()

// Get admin status from app state store
const appState = useAppStateStore()
const { isAdmin } = storeToRefs(appState)

// Reactive state for settings menu
const settingsMenuVisible = ref(false)

defineProps({
  mainAppVisible: Boolean,
  chatButtonVisible: Boolean,
  sidePanelVisible: Boolean,
  sitesGridVisible: Boolean,
  isDarkTheme: Boolean,
})

const emit = defineEmits([
  'toggleMainApp',
  'toggleChatButton',
  'toggleChromePanel',
  'openSitesGrid',
  'openPluginConfig',
])

// Methods to handle settings menu actions
const handlePluginConfig = () => {
  settingsMenuVisible.value = false
  emit('openPluginConfig')
}

const handleLocalStoragePanel = () => {
  settingsMenuVisible.value = false
  // Open localStorage management in a new tab
  window.open(chrome.runtime.getURL('local_storage.html'), '_blank')
}
</script>

<style scoped>
.app-control-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.control-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
  margin: 4px 6px;
  transition: all 0.3s ease;
}

.control-button {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: visible;
}

.control-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #6b7280;
  transform: rotate(2deg);
  box-shadow:
    0 4px 15px rgba(107, 114, 128, 0.3),
    0 0 20px rgba(107, 114, 128, 0.1);
  animation: pulse 1.5s infinite;
}

.control-button:hover::before {
  left: 100%;
}

.control-button:active {
  transform: scale(0.95) rotate(1deg);
  transition: transform 0.1s ease;
}

.control-button.active {
  background: #6b7280;
  border-color: #6b7280;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.control-button.active .button-icon {
  color: white;
}

.button-icon {
  font-size: 16px;
  color: #374151;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dark-theme .button-icon {
  color: #d1d5db;
}

.dark-theme .control-button {
  background: rgba(64, 64, 64, 0.8);
  border-color: rgba(115, 115, 115, 0.6);
}

.dark-theme .control-button:hover {
  background: rgba(75, 75, 75, 1);
  border-color: #9ca3af;
}

.dark-theme .control-button.active {
  background: #9ca3af;
  border-color: #9ca3af;
}

.dark-theme .control-button.active .button-icon {
  color: white;
}

.dark-theme .control-separator {
  background: linear-gradient(90deg, transparent, rgba(115, 115, 115, 0.6), transparent);
}

.button-icon svg {
  width: 16px;
  height: 16px;
  transition: inherit;
  max-width: 18px;
  max-height: 18px;
  min-width: 14px;
  min-height: 14px;
}

.control-button:has(.button-icon svg path[d*='M3']) .button-icon,
.control-button .button-icon:has(svg path[d*='M3']) {
  padding: 2px;
}

.control-button .button-icon svg[viewBox*='0 0 1024 1024'] {
  width: 18px;
  height: 18px;
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(107, 114, 128, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(107, 114, 128, 0);
  }
}

/* Settings Menu Styles */
.settings-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
}

.settings-menu-item {
  width: 100%;
  justify-content: flex-start;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: var(--el-text-color-primary);
  background: transparent;
  border: none;
}

.settings-menu-item:hover {
  background: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.settings-menu-item .el-icon {
  margin-right: 8px;
  font-size: 14px;
}

/* Dark theme support for settings menu */
.dark-theme .settings-menu-item {
  color: var(--el-text-color-primary);
}

.dark-theme .settings-menu-item:hover {
  background: var(--el-fill-color-darker);
  color: var(--el-color-primary-light-3);
}
</style>
