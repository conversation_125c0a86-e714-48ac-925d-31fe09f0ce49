<script setup>
import {
  Connection,
  Document,
  Expand,
  Folder,
  FolderAdd,
  Link,
  Loading,
  Plus,
  VideoPlay,
} from '@element-plus/icons-vue'
import { ElInput } from 'element-plus'
import { storeToRefs } from 'pinia'
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import Analytics from '../../Analytics'
import { useAppStateStore } from '../../stores/appState'
import { editBar } from '../firebase'
import { updateMyPromptsBarKeyInLocalStorage } from '../localStorage'
import iconAddBookmark from './icons/iconAddBookmark.vue'
import iconBookmark from './icons/iconBookmark.vue'
import Tooltip from './Tooltip.vue'

const props = defineProps(['bar', 'type', 'isAdmin'])
const emit = defineEmits(['openEditTextarea', 'openEditFolder', 'onPromptNameChange'])

const appState = useAppStateStore()
const {
  hasSubscription,
  isPromptBookmark,
  textToCreatePrompt,
  nameForGeneratedPrompt,
  isGeneratingPromptName,
} = storeToRefs(appState)

const isAdmin = props.isAdmin

const treeRef = ref(null)

// console.log('props.type', props.type)
let editInputShow = reactive([])
// console.log(JSON.stringify(props.bar, null, 2))
let addedNodeId = null
let recentlyAddedPromptId = null // Track the most recently added prompt for AI name updates
const activeAccordionInEdit = ref('1')

const barData = ref(props.bar)
// console.log('barData: ', barData.value)
const treeDataByType = computed(() => props.bar[props.type + 'Menu'] || [])
// console.log('treeDataByType: ', 'type ' + props.type, ': ', treeDataByType.value)
const currentNodeKey = ref('')

const isBookmark = ref(isPromptBookmark.value || false)

const bookmarkLink = inject('bookmarkLink', ref(''))

const { t } = useI18n()

function openSubscriptionDialog(type = '') {
  Analytics.fireEvent('subscription_dialog', { type })
  appState.openSubscriptionDialog()
}

const generateID = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

const defaultExpandedPromptsFolders = ref([])

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      if (props.type === 'prompt' && (textToCreatePrompt.value || isPromptBookmark.value)) {
        addPromptRoot()
      }
    }, 150)
  })

  if (props.type !== 'prompt') return // dont get expanded for links
  // console.log('find expanded: ', findExpandable(treeDataByType.value))
  defaultExpandedPromptsFolders.value = findExpanded(treeDataByType.value)
  // console.log('defaultExpandedFolders at load: ', defaultExpandedPromptsFolders.value)
})

// Watch for AI-generated name updates and apply them to the recently added prompt
watch(nameForGeneratedPrompt, (newName, oldName) => {
  if (
    recentlyAddedPromptId &&
    newName &&
    newName !== oldName &&
    newName !== t('messages.newPrompt')
  ) {
    // Find and update the recently added prompt's name
    const prompt = findNode(props.bar[props.type + 'Menu'], recentlyAddedPromptId)
    if (prompt && prompt.label === t('messages.newPrompt')) {
      prompt.label = newName
      saveToServer()
      // Clear the tracking ID since we've updated the name
      recentlyAddedPromptId = null
    }
  }
})

function findNode(tree, id) {
  for (let node of tree) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      let found = findNode(node.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

const findExpanded = (tree) => {
  let expanded = []
  function findExpanded(tree) {
    for (let node of tree) {
      if (node.expanded === true) {
        expanded.push(node.id)
      }
      if (node.children) {
        let found = findExpanded(node.children)
        if (found) {
          expanded.push(found.id)
        }
      }
    }
  }
  findExpanded(tree)
  // console.log('expanded: ', expanded)
  return expanded
}

const handleNodeClick = (nodeObj, node, tree, e) => {
  // console.log('handleNodeClick-nodeObj', nodeObj, node, tree, e)
  // if (props.type === 'prompt') {
  //   emit('updateEditPromptText', nodeObj, props.type)
  // }
  // if (props.type === 'link') {
  //   emit('updateEditLinkText', nodeObj, props.type)
  // }
  let setType = props.type ? props.type : 'prompt'
  if (nodeObj.children) {
    // console.log('openFolderEdit & closeEditTextarea')
    emit('openEditFolder', node)
  } else {
    // console.log('openEditTextarea & closeFolderEdit')
    emit('openEditTextarea', node, setType)
  }
}

const allowDrop = (draggingNode, dropNode, dropType) => {
  // return dropNode.data.prompt === undefined
  // console.log('draggingNode: ', draggingNode, '\n dropNode: ', dropNode, '\n dropType: ', dropType)
  if (dropNode.data.prompt !== undefined) return dropType !== 'inner'
  if (dropNode.data.link !== undefined) return dropType !== 'inner'
  return true
}

const handleDrop = (draggingNode, dropNode, dropType, ev) => {
  // console.log('dropNode.data.prompt', dropNode.data.prompt)
  // console.log('nadle drop: \n draggingNode: ', draggingNode, '\n dropNode: ', dropNode, '\n dropType: ', dropType, '\n ev: ', ev)
  // if (dropNode.data.prompt !== undefined) {
  //   console.log('dziala')
  //   ev.preventDefault()
  //   return
  // }
  // console.log('treeDataByType on drop: ', treeDataByType.value)
  saveToServer()

  nextTick(() => {
    setTimeout(() => {
      currentNodeKey.value = draggingNode.data.id // set currentNodeKey to expand the parent node
    }, 200)
    let index = defaultExpandedPromptsFolders.value.indexOf(dropNode.data.id)
    if (index > -1) return
    defaultExpandedPromptsFolders.value.push(dropNode.data.id) // add parent node to defaultExpanded
  })
}

const addFolder = (node, data) => {
  // console.log('addFolder: ', node, data, data.id)
  const newFolder = {
    label: t('messages.newFolder'),
    id: generateID(),
    expanded: true,
    children: [],
  }

  defaultExpandedPromptsFolders.value.push(data.id) // add parent node to defaultExpanded

  Analytics.fireEvent('folder_add', {
    prompt_bar_id: props.bar.id,
    folder_id: newFolder.id,
  })

  data.children.push(newFolder)
  saveToServer()

  nextTick(() => {
    currentNodeKey.value = newFolder.id // set currentNodeKey to expand the parent node

    setTimeout(() => {
      editNameBar(node, newFolder)
      nextTickFunction(newFolder)
    }, 100)
  })
}

const addFolderRoot = () => {
  if (!hasSubscription.value) {
    return
  }
  // treeDataByType.value = props.bar[props.type + 'Menu']
  const newFolder = {
    label: t('messages.newFolder'),
    id: generateID(),
    expanded: true,
    children: [],
  }

  props.bar[props.type + 'Menu'].push(newFolder)

  Analytics.fireEvent('folder_add', {
    prompt_bar_id: props.bar.id,
    folder_id: newFolder.id,
  })

  saveToServer()
  editNameBar(0, newFolder)
  nextTickFunction(newFolder)
}

const addPrompt = (node, data) => {
  // console.log('addPrompt to: ', node, node.key, data, data.id, treeDataByType.value)
  let newPromptName = t('messages.newPrompt')
  let currentURL = ''
  if (isBookmark.value) {
    // console.log('isBookmark: ', isBookmark)
    currentURL = window.location.href
    newPromptName = document.title || 'New Bookmark'
  }
  const newPrompt = {
    label: newPromptName,
    id: generateID(),
    prompt: '',
    premium: false,
    isBookmark: isBookmark.value,
    destination: currentURL,
  }

  data.children.push(newPrompt)
  saveToServer()

  nextTick(() => {
    currentNodeKey.value = newPrompt.id // set currentNodeKey to expand the parent node

    setTimeout(() => {
      editNameBar(node, newPrompt)
      nextTickFunction(newPrompt)
    }, 100)
  })

  addedNodeId = newPrompt.id

  Analytics.fireEvent('prompt_add', {
    prompt_bar_id: props.bar.id,
    prompt_id: newPrompt.id,
  })
  // console.log('added new Prompt: ', data, treeDataByType.value)
}
const addPromptRoot = () => {
  // treeDataByType.value = props.bar[props.type + 'Menu']
  // console.log('addPromptRoot', treeDataByType.value)
  // console.log('props.textToCreatePrompt: ', props.textToCreatePrompt)

  let currentURL = ''
  let newPromptName = isBookmark.value
    ? appState.nameForGeneratedPrompt || document.title || t('messages.newBookmark')
    : appState.nameForGeneratedPrompt || t('messages.newPrompt')

  if (isBookmark.value) {
    currentURL = bookmarkLink.value || window.location.href
    isPromptBookmark.value = false
  }
  const newPrompt = {
    label: newPromptName,
    id: generateID(),
    prompt: textToCreatePrompt.value,
    premium: false,
    isBookmark: isBookmark.value,
    destination: currentURL,
  }

  props.bar[props.type + 'Menu'].push(newPrompt)
  saveToServer()

  editNameBar(0, newPrompt)
  nextTickFunction(newPrompt)
  addedNodeId = newPrompt.id

  // Track this prompt for AI name updates (only if it has a temporary name)
  if (newPrompt.label === t('messages.newPrompt')) {
    recentlyAddedPromptId = newPrompt.id
  }

  Analytics.fireEvent('prompt_add', {
    prompt_bar_id: props.bar.id,
    prompt_id: newPrompt.id,
  })
  // console.log('addedNodeId:', addedNodeId)
}
const addLinkRoot = (type) => {
  // treeDataByType.value = props.bar[props.type + 'Menu']
  // console.log('addLinkRoot', treeDataByType.value, type)
  let label = 'New link'
  if (type === 'url') label = 'New external link'
  if (type === 'modal') label = 'New popup information'
  if (type === 'video') label = 'New popup video'

  const newLink = {
    label: label,
    id: generateID(),
    link: '',
    linkType: type,
  }

  props.bar[props.type + 'Menu'].push(newLink)
  saveToServer()

  editNameBar(0, newLink)
  nextTickFunction(newLink)

  addedNodeId = newLink.id
  // console.log('addedNodeId:', addedNodeId)
}

const nextTickFunction = (newPrompt) => {
  // console.log('before-treeRef.value.$el', treeRef.value.$el)
  nextTick(() => {
    // console.log('treeRef.value.$el', treeRef.value.$el)
    const newNodeElement = treeRef.value.$el.querySelector(`[data-key="${newPrompt.id}"]`)

    if (newNodeElement) {
      const inputElement = newNodeElement.querySelector('input')

      if (inputElement) {
        // console.log('Znaleziono inputElement')
        inputElement.select()
        inputElement.focus()
        // console.log('inputElement', inputElement)

        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        })
        inputElement.dispatchEvent(clickEvent)

        handleNodeClick(newPrompt)
      } else {
        // console.log('Nie znaleziono inputElement')
        // Handle the case when there's no inputElement
        handleNodeClick(newPrompt)
      }
    } else {
      // console.log('Nie znaleziono newNodeElement')
    }
  })
}

const editNameBar = (node, prompt) => {
  // Update all elements to false
  editInputShow.forEach((item) => (item.edit = false))

  const index = editInputShow.findIndex((item) => item.id === prompt.id)

  if (index === -1) {
    // Add a new object if it doesn't exist and set its edit property to true
    editInputShow.push({ id: prompt.id, edit: true })
  } else {
    editInputShow[index].edit = true
  }

  const newNodeElement = treeRef.value.$el.querySelector(`[data-key="$prompt.id}"]`)
  if (newNodeElement) {
    // console.log('newNodeElement-----', newNodeElement)
    const inputElement = newNodeElement.querySelector('input')
    // console.log('inputElement', inputElement)
    if (inputElement) {
      nextTick().then(() => {
        inputElement.focus()
      })
    }
  }

  // saveToServer()
}

const enterInput = (data) => {
  const isPrompt = data.prompt !== undefined
  const isFolder = data.children !== undefined
  // console.log('enterInput: ', data, { isPrompt, isFolder })
  // Update all elements to false
  editInputShow.forEach((item) => (item.edit = false))

  if (isPrompt) {
    Analytics.fireEvent('prompt_edit', {
      prompt_bar_id: props.bar.id,
      prompt_id: data.id,
      field: 'label',
    })
  } else if (isFolder) {
    Analytics.fireEvent('folder_edit', {
      prompt_bar_id: props.bar.id,
      folder_id: data.id,
      field: 'label',
    })
  }

  saveToServer()

  if (isPrompt) {
    emit('onPromptNameChange', data)
  }
}

const removeElementFromBar = (node, data) => {
  const isFolder = data.prompt === undefined
  // console.log('removeElementFromBar: ', node, data, data.id, isFolder)
  const parent = node.parent
  const children = parent.data.children || parent.data
  const index = children.findIndex((d) => d.id === data.id)

  if (isFolder) {
    Analytics.fireEvent('folder_remove', {
      prompt_bar_id: props.bar.id,
      folder_id: data.id,
    })
  } else {
    Analytics.fireEvent('prompt_remove', {
      prompt_bar_id: props.bar.id,
      prompt_id: data.id,
    })
  }

  children.splice(index, 1)
  saveToServer()
  if (isFolder) {
    emit('openEditFolder', '', true)
  } else {
    emit('openEditTextarea', '', '', true)
  }
}

const saveToServer = async () => {
  // treeDataByType.value = props.bar[props.type + 'Menu']
  // console.log('saveToServer data: ', treeDataByType.value, treeDataByType._rawValue)
  let rawData = JSON.stringify(treeDataByType.value)
  // console.log('saveToServer rawData: ', rawData)
  if (props.type === 'prompt') {
    // console.log('saveToServer prompt rawData: ', rawData)
    await editBar(props.bar, {
      prompt_menu: rawData,
    })
    updateMyPromptsBarKeyInLocalStorage('promptMenu', treeDataByType.value)
  }
  if (props.type === 'link') {
    // console.log('saveToServer link rawData: ', rawData)
    await editBar(props.bar, {
      links: rawData,
    })
    updateMyPromptsBarKeyInLocalStorage('linkMenu', treeDataByType.value)
  }
}

const filterPrompts = ref('')

watch(filterPrompts, (val) => {
  treeRef.value.filter(val)
})

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}

const onNodeExpand = (data) => {
  // console.log('onNodeExpand: ', data, treeDataByType.value, defaultExpandedPromptsFolders.value)
  let node = findNode(treeDataByType.value, data.id)
  if (node) {
    // console.log('node: ', node, treeDataByType.value)
    node.expanded = true
  }
  defaultExpandedPromptsFolders.value.push(data.id)
  saveToServer()
  // console.log('after expand: ', data, treeDataByType.value, defaultExpandedPromptsFolders.value)
}
const onNodeCollapse = (data) => {
  // console.log('onNodeCollapse: ', data, treeDataByType.value, defaultExpandedPromptsFolders.value)
  let node = findNode(treeDataByType.value, data.id)
  if (node) {
    // console.log('node: ', node, treeDataByType.value)
    node.expanded = false
  }

  defaultExpandedPromptsFolders.value.filter((item, index) => {
    // console.log('item: ', item, data.id, index)
    if (item === data.id) delete defaultExpandedPromptsFolders.value[index]
  })

  // delete defaultExpandedPromptsFolders.value[index]
  saveToServer()
  // console.log('after collapse: ', data, treeDataByType.value, defaultExpandedPromptsFolders.value)
}

// const foldersExpand = (setExpanded) => {
//   console.log('foldersExpand: ', setExpanded)
//   let expandable = []
//   let findExpandable = treeDataByType.value.filter((item) => item.expandable !== '')
//   if (!findExpandable || findExpandable.length === 0) return
//   console.log('findExpandable: ', findExpandable)
//   findExpandable.forEach((item) => {
//     expandable.push(item.id)
//   })
//   console.log('expandable: ', expandable)
//   if(setExpanded){
//     treeDataByType.value.forEach((item) => {
//       if (expandable.includes(item.id)) {
//         item.expanded = true
//       }
//     })
//     defaultExpandedPromptsFolders.value = expandable
//     console.log('open all')
//   }
//   else{
//     treeDataByType.value.forEach((item) => {
//       if (expandable.includes(item.id)) {
//         item.expanded = false
//       }
//     })
//     defaultExpandedPromptsFolders.value = []
//     console.log('close all')
//   }
//   console.log('treeDataByType: ', treeDataByType.value)
//   console.log('defaultExpandedPromptsFolders: ', defaultExpandedPromptsFolders.value, treeRef.value)
// }
</script>

<template>
  <div>
    <div class="absolute z-10 top-1.5 right-4 my-1 mx-4" v-show="activeAccordionInEdit === '1'">
      <template v-if="props.type === 'prompt'">
        <template v-if="!hasSubscription">
          <el-popover placement="bottom" :width="200" trigger="hover" :teleported="false">
            <template #reference>
              <el-button
                disabled
                type="success"
                size="small"
                plain
                :icon="Folder"
                class="text-base"
                @click="openSubscriptionDialog('add_prompt_bar_folder')"
              ></el-button>
            </template>
            <div
              class="flex flex-col items-center gap-4 text-center whitespace-break-spaces break-normal"
            >
              <p class="text-xs">{{ t('subscription.required') }}</p>
              <el-button
                type="info"
                plain
                @click="openSubscriptionDialog('add_prompt_bar_folder')"
                class="text-purple-400 focus:bg-purple-400 hover:!bg-purple-500 hover:!text-white"
                >{{ t('subscription.buySubscription') }}
              </el-button>
            </div>
          </el-popover>
          <el-popover placement="bottom" :width="200" trigger="hover" :teleported="false">
            <template #reference>
              <el-button
                type="success"
                size="small"
                plain
                :icon="Expand"
                @click="addPromptRoot"
                class="ml-2 text-base"
              >
              </el-button>
            </template>
            <div
              class="flex flex-col items-center gap-4 text-center whitespace-break-spaces break-normal"
            >
              <p class="text-xs">{{ t('messages.addPromptToSelectedPromptBar') }}</p>
            </div>
          </el-popover>
        </template>
        <template v-else>
          <Tooltip
            class="box-item"
            effect="light"
            :content="t('messages.addNewFolder')"
            placement="bottom"
          >
            <el-button
              type="success"
              size="small"
              plain
              :icon="Folder"
              class="text-base"
              @click="addFolderRoot"
              data-testid="add-prompt-bar-folder-button"
            ></el-button>
          </Tooltip>
          <Tooltip
            class="box-item"
            effect="light"
            :content="t('messages.addPromptToSelectedPromptBar')"
            placement="bottom"
          >
            <el-button
              type="success"
              size="small"
              plain
              :icon="Expand"
              class="text-base"
              @click="
                () => {
                  isBookmark = false
                  addPromptRoot()
                }
              "
              data-testid="add-prompt-bar-prompt-button"
            ></el-button>
          </Tooltip>
          <Tooltip
            class="box-item"
            effect="light"
            :content="t('messages.addBookmarkToSelectedPromptBar')"
            placement="bottom"
          >
            <el-button
              type="success"
              size="small"
              plain
              class="text-base"
              @click="
                () => {
                  isBookmark = true
                  addPromptRoot()
                }
              "
              data-testid="add-prompt-bar-bookmark-button"
            >
              <iconBookmark />
            </el-button>
          </Tooltip>
        </template>
      </template>
      <template v-if="props.type === 'link'">
        <Tooltip
          class="box-item"
          effect="light"
          :content="t('messages.addNewPopup')"
          placement="bottom"
        >
          <el-button
            type="success"
            size="small"
            plain
            :icon="Connection"
            @click="addLinkRoot('modal')"
            class="text-base"
            data-testid="bar-links-add-new-popup-button"
          ></el-button>
        </Tooltip>
        <Tooltip
          class="box-item"
          effect="light"
          :content="t('messages.addNewLink')"
          placement="bottom"
        >
          <el-button
            type="success"
            size="small"
            plain
            :icon="Link"
            @click="addLinkRoot('url')"
            class="text-base"
            data-testid="bar-links-add-new-link-button"
          ></el-button>
        </Tooltip>
        <Tooltip
          class="box-item"
          effect="light"
          :content="t('messages.addNewVideoLink')"
          placement="bottom"
        >
          <el-button
            type="success"
            size="small"
            plain
            :icon="VideoPlay"
            @click="addLinkRoot('video')"
            class="text-base"
            data-testid="bar-links-add-new-video-button"
          ></el-button>
        </Tooltip>
      </template>
    </div>
    <div>
      <!-- TODO: add placeholder -->
      <el-input v-model="filterPrompts" :placeholder="t('sort.filter')" class="mb-2" />

      <el-tree
        id="treeBar"
        ref="treeRef"
        :data="treeDataByType"
        draggable
        :expand-on-click-node="false"
        node-key="id"
        @node-click="handleNodeClick"
        @node-drop="handleDrop"
        :allow-drop="allowDrop"
        :empty-text="t('messages.empty')"
        :filter-node-method="filterNode"
        :highlight-current="true"
        :default-expanded-keys="defaultExpandedPromptsFolders"
        :current-node-key="currentNodeKey"
        :auto-expand-parent="false"
        @node-expand="onNodeExpand"
        @node-collapse="onNodeCollapse"
      >
        <template #default="{ node, data }">
          <span
            class="custom-tree-node w-full grid items-center gap-1 group"
            :class="{ 'text-purple-400': data.premium && isAdmin }"
          >
            <!-- {{ data }} -->
            <template v-if="props.type === 'prompt'">
              <el-icon v-if="data.prompt === undefined">
                <Folder />
              </el-icon>
              <template v-if="data.prompt !== undefined">
                <el-icon v-if="data.isBookmark">
                  <iconBookmark />
                </el-icon>
                <el-icon v-else>
                  <Expand />
                </el-icon>
              </template>
            </template>
            <template v-if="props.type === 'link'">
              <el-icon v-if="data.linkType === 'url'">
                <Link />
              </el-icon>
              <el-icon v-if="data.linkType === 'modal'">
                <Connection />
              </el-icon>
              <el-icon v-if="data.linkType === 'video'">
                <VideoPlay />
              </el-icon>
            </template>

            <el-input
              v-show="editInputShow.find((item) => item.id === data.id)?.edit"
              v-model="data.label"
              size="small"
              :ref="data.id"
              @keyup.enter="$event.target.blur()"
              @blur="enterInput(data)"
            >
            </el-input>
            <div v-show="!editInputShow.find((item) => item.id === data.id)?.edit" class="grid">
              <div class="flex items-center gap-2">
                <el-text
                  @dblclick="editNameBar(node, data)"
                  truncated
                  :class="{ 'text-purple-400': data.premium && isAdmin }"
                >
                  {{ node.label }}
                </el-text>
                <!-- Loading indicator for AI name generation in tree -->
                <el-icon
                  v-if="
                    isGeneratingPromptName &&
                    node.label === t('messages.newPrompt') &&
                    data.id === recentlyAddedPromptId
                  "
                  class="animate-spin text-blue-500"
                  :size="12"
                >
                  <Loading />
                </el-icon>
              </div>
            </div>
            <el-button-group class="custom-tree-node_options invisible group-hover:visible">
              <el-button
                size="small"
                type="primary"
                icon="Edit"
                plain
                @click="editNameBar(node, data)"
                class="border-none text-base"
              >
              </el-button>
              <el-button
                size="small"
                type="success"
                plain
                v-if="data.prompt === undefined && props.type === 'prompt'"
                :icon="Folder"
                @click="addFolder(node, data)"
                class="border-none text-base"
              >
              </el-button>
              <el-button
                size="small"
                type="success"
                plain
                v-if="data.prompt === undefined && props.type === 'prompt'"
                :icon="Expand"
                @click="
                  () => {
                    isBookmark = false
                    addPrompt(node, data)
                  }
                "
                class="border-none text-base"
              >
              </el-button>
              <el-button
                size="small"
                type="success"
                plain
                v-if="data.prompt === undefined && props.type === 'prompt'"
                @click="
                  () => {
                    isBookmark = true
                    addPrompt(node, data)
                  }
                "
                class="border-none text-base"
              >
                <iconBookmark />
              </el-button>
              <el-popconfirm
                width="220"
                confirm-button-text="OK"
                cancel-button-text="No, Thanks"
                @confirm="removeElementFromBar(node, data)"
                icon=""
                title="Are you sure to delete this?"
              >
                <template #reference>
                  <el-button
                    size="small"
                    type="danger"
                    icon="Delete"
                    plain
                    class="border-none text-base"
                  >
                  </el-button>
                </template>
              </el-popconfirm>
            </el-button-group>
          </span>
        </template>
      </el-tree>

      <!-- <div class="flex justify-center">
          <el-link :underline="false" @click="foldersExpand(true)"><span class="text-sm px-2">Expand all</span></el-link>
          <el-link :underline="false" @click="foldersExpand(false)"><span class="text-sm px-2">Collapse all</span></el-link>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.custom-tree-node {
  grid-template-columns: max-content auto max-content;
}
</style>
