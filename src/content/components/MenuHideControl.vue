<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { useDragAndDrop } from '../composables/useDragAndDrop.js'
import { useElementControls } from '../composables/useElementControls.js'
import { useMenuControlState } from '../composables/useMenuControlState.js'
import { useSidePanel } from '../composables/useSidePanel.js'
import { useThemeDetection } from '../composables/useThemeDetection.js'
import AppControlButtons from './AppControlButtons.vue'
import CollapseButton from './CollapseButton.vue'
import MenuButtons from './MenuButtons.vue'
import SitesGrid from './SitesGrid.vue'

// Załaduj stan zwinięcia przed pierwszym renderowaniem, <PERSON><PERSON>y uniknąć migania
const getInitialCollapseState = () => {
  const siteIdentifier = window.location.hostname.replace(/[^a-zA-Z0-9.-]/g, '_')
  const stateKey = `menuCollapseState_${siteIdentifier}`
  const savedState = localStorage.getItem(stateKey)

  if (savedState) {
    try {
      const state = JSON.parse(savedState)
      return state.isCollapsed ?? false
    } catch (e) {
      console.warn('Failed to load initial collapse state:', e)
      return false
    }
  }
  return false
}

const isCollapsed = ref(getInitialCollapseState())

const state = useMenuControlState()
const { mainAppVisible, chatButtonVisible, sidePanelVisible, menuHideLevel } = state

const elementControls = useElementControls(state)
const { toggleMainApp, toggleChatButton, changeMenuHideLevel, applyMenuHideLevel } = elementControls

const sidePanelControls = useSidePanel(state)
const { toggleChromePanel, checkSidePanelStatus, setupSidePanelListener, startStatusPolling } =
  sidePanelControls

const dragControls = useDragAndDrop()
const { isDragging, position, isOnLeftSide, startDrag, loadSavedPosition } = dragControls

const themeDetection = useThemeDetection()
const { isDarkTheme, detectTheme } = themeDetection

const { t } = useI18n()

// Funkcje do zapisywania i ładowania stanu rozwinięcia menu
const saveCollapseState = () => {
  const siteIdentifier = window.location.hostname.replace(/[^a-zA-Z0-9.-]/g, '_')
  const stateKey = `menuCollapseState_${siteIdentifier}`
  localStorage.setItem(
    stateKey,
    JSON.stringify({
      isCollapsed: isCollapsed.value,
      timestamp: Date.now(),
    }),
  )
}

// Funkcja do wysyłania zmiany motywu do background script
const sendThemeChange = (theme) => {
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime
      .sendMessage({
        action: 'themeChanged',
        isDarkTheme: theme,
      })
      .catch(() => {
        // Ignore errors
      })
  }
}

// Śledzenie poprzedniego stanu motywu
let previousTheme = isDarkTheme.value

const toggleCollapse = () => {
  if (isCollapsed.value) {
    const controlElement = document.querySelector('.menu-hide-control')
    if (controlElement) {
      const rect = controlElement.getBoundingClientRect()
      const expandedHeight = 240
      const windowHeight = window.innerHeight
      const currentTop = rect.top
      const bottomAfterExpansion = currentTop + expandedHeight

      if (bottomAfterExpansion > windowHeight - 30) {
        const excessHeight = bottomAfterExpansion - (windowHeight - 30)
        const currentTopPercent = (currentTop / windowHeight) * 100
        const adjustmentPercent = (excessHeight / windowHeight) * 100
        const newTopPercent = Math.max(5, currentTopPercent - adjustmentPercent)

        if (currentTopPercent - newTopPercent > 5) {
          position.value.top = newTopPercent
          localStorage.setItem(
            'menuControlPosition',
            JSON.stringify({
              top: position.value.top,
              isOnLeftSide: isOnLeftSide.value,
            }),
          )
        }
      }
    }
  }
  isCollapsed.value = !isCollapsed.value

  // Zapisz stan rozwinięcia/zwinięcia
  saveCollapseState()
}

const handleButtonClick = () => {
  if (isDragging.value) return
  toggleCollapse()
}

const handleStartDrag = (event) => {
  startDrag(event, isCollapsed.value)
}

const openPluginConfig = () => {
  if (isDragging.value) return
  // Otwórz vue-debug.html w nowej karcie
  window.open(chrome.runtime.getURL('vue-debug.html'), '_blank')
}

// Sites Grid Modal functionality
const isSitesGridModalOpen = ref(false)

const openSitesGrid = () => {
  if (isDragging.value) return
  isSitesGridModalOpen.value = true
}

const closeSitesGridModal = () => {
  isSitesGridModalOpen.value = false
}

const checkElementsVisibility = () => {
  const appElement = document.getElementById('_appAIPM')
  const chatElement = document.getElementById('_chatButton')

  let stateChanged = false

  if (appElement) {
    const isVisible = appElement.style.display !== 'none'
    if (mainAppVisible.value !== isVisible) {
      mainAppVisible.value = isVisible
      stateChanged = true
    }
  }
  if (chatElement) {
    const isVisible = chatElement.style.display !== 'none'
    if (chatButtonVisible.value !== isVisible) {
      chatButtonVisible.value = isVisible
      stateChanged = true
    }
  }

  // Save state if it changed (but not on initial load)
  if (stateChanged && state.currentSite.value) {
    state.saveVisibilityState()
  }
}

let intervals = {
  visibility: null,
  duplicateCheck: null,
  themeCheck: null,
  menuHideLevel: null,
  popupCheck: null,
  sidePanelCheck: null,
}
let sidePanelPollingCleanup = null
let mutationObserver

const removeDuplicateControls = () => {
  // Find all menu-hide-control elements
  const allControls = document.querySelectorAll('.menu-hide-control')

  if (allControls.length > 1) {
    // Find the one that is in #_controlPanel (our main one)
    const controlPanelElement = document.querySelector('#_controlPanel .menu-hide-control')

    // Remove all others
    allControls.forEach((control) => {
      if (control !== controlPanelElement) {
        control.remove()
      }
    })
  }
}

const cleanupProcessedMarkers = () => {
  // Usuń znaczniki z elementów, które już nie istnieją w DOM
  const processedElements = document.querySelectorAll('[data-processed]')
  processedElements.forEach((element) => {
    if (!document.body.contains(element)) {
      element.removeAttribute('data-processed')
    }
  })
}

const forcePopupsToBody = () => {
  // Znajdź aktywne popupy
  const activePopups = document.querySelectorAll(
    `
    .el-popper:not(.el-popper--hidden):not([data-processed]),
    .el-select-dropdown:not([data-processed]),
    .el-dropdown-menu:not([data-processed]),
    .el-dialog__wrapper:not([data-processed]),
    .ai-prompt-lab-popup:not([data-processed])
  `.trim(),
  )

  // Jeśli są aktywne popupy, zatrzymaj animacje na _appAIPM
  const appElement = document.getElementById('_appAIPM')
  if (activePopups.length > 0 && appElement) {
    // Zatrzymaj animacje poprzez ustawienie transition na none
    appElement.style.transition = 'none'
    // Usuń transformacje które mogą psuć pozycjonowanie popupów
    appElement.style.transform = 'none'
  } else if (appElement && appElement.style.transition === 'none') {
    // Przywróć animacje gdy nie ma aktywnych popupów
    appElement.style.transition = ''
    appElement.style.transform = ''
  }

  activePopups.forEach((popup) => {
    popup.dataset.processed = 'true'

    // Ustaw z-index dla popupów
    if (popup.style.zIndex < '9999999') {
      popup.style.zIndex = '9999999'
    }
  })
}

onMounted(() => {
  state.initializeState()
  removeDuplicateControls()
  detectTheme()
  applyMenuHideLevel()
  loadSavedPosition()

  intervals.duplicateCheck = setInterval(removeDuplicateControls, 2000)
  intervals.visibility = setInterval(checkElementsVisibility, 1000)
  intervals.themeCheck = setInterval(() => {
    detectTheme()
    // Wyślij zmianę motywu jeśli się zmienił
    if (isDarkTheme.value !== previousTheme) {
      previousTheme = isDarkTheme.value
      sendThemeChange(isDarkTheme.value)
    }
  }, 500)
  intervals.menuHideLevel = setInterval(() => {
    const currentMenuHideLevel = localStorage.getItem('menuHideLevel')
    const newLevel = parseInt(currentMenuHideLevel) || 0
    if (newLevel !== menuHideLevel.value) {
      menuHideLevel.value = newLevel
      applyMenuHideLevel()
    }
  }, 500)
  // Set up side panel status polling
  sidePanelPollingCleanup = startStatusPolling()
  intervals.popupCheck = setInterval(() => {
    forcePopupsToBody()
    if (Math.random() < 0.1) {
      cleanupProcessedMarkers()
    }
  }, 3000)

  forcePopupsToBody()

  mutationObserver = new MutationObserver((mutations) => {
    let shouldProcessPopups = false

    mutations.forEach((mutation) => {
      // Sprawdź czy dodano/usunięto popup elementy
      if (mutation.type === 'childList') {
        const addedNodes = Array.from(mutation.addedNodes).filter((node) => node.nodeType === 1)
        const removedNodes = Array.from(mutation.removedNodes).filter((node) => node.nodeType === 1)

        const isPopupChange = [...addedNodes, ...removedNodes].some(
          (node) =>
            node.classList?.contains('el-popper') ||
            node.classList?.contains('el-select-dropdown') ||
            node.classList?.contains('el-dropdown-menu') ||
            node.classList?.contains('el-dialog__wrapper') ||
            node.classList?.contains('ai-prompt-lab-popup') ||
            node.querySelector?.(
              '.el-popper, .el-select-dropdown, .el-dropdown-menu, .el-dialog__wrapper, .ai-prompt-lab-popup',
            ),
        )

        if (isPopupChange) {
          shouldProcessPopups = true
        }
      }

      // Sprawdź zmiany klas (np. dodanie/usunięcie el-popper--hidden)
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const target = mutation.target
        if (
          target.classList?.contains('el-popper') ||
          target.classList?.contains('el-select-dropdown') ||
          target.classList?.contains('el-dropdown-menu')
        ) {
          shouldProcessPopups = true
        }
      }
    })

    if (shouldProcessPopups) {
      forcePopupsToBody()
    }
  })

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class'],
  })

  setTimeout(checkSidePanelStatus, 500)
  setupSidePanelListener()

  // Nasłuchuj żądań aktualnego motywu
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      if (message.action === 'getCurrentTheme') {
        sendResponse({ isDarkTheme: isDarkTheme.value })
        return true
      }
      return false
    })
  }

  // Wyślij początkowy stan motywu
  sendThemeChange(isDarkTheme.value)
})

onUnmounted(() => {
  Object.values(intervals).forEach((interval) => {
    if (interval) clearInterval(interval)
  })
  if (mutationObserver) {
    mutationObserver.disconnect()
  }
  if (sidePanelPollingCleanup) {
    sidePanelPollingCleanup()
  }
})
</script>

<template>
  <div
    class="menu-hide-control"
    :class="{
      'on-left': isOnLeftSide,
      dragging: isDragging,
      'collapsed-mode': isCollapsed,
      'dark-theme': isDarkTheme,
      'light-theme': !isDarkTheme,
    }"
    :style="{
      top: position.top + '%',
      right: isOnLeftSide ? 'auto' : (position.right || 10) + 'px',
      left: isOnLeftSide ? '10px' : 'auto',
    }"
  >
    <div class="control-buttons" :class="{ collapsed: isCollapsed }">
      <MenuButtons
        :main-app-visible="mainAppVisible"
        :menu-hide-level="menuHideLevel"
        :is-dark-theme="isDarkTheme"
        @change-menu-hide-level="changeMenuHideLevel"
      />

      <AppControlButtons
        :main-app-visible="mainAppVisible"
        :chat-button-visible="chatButtonVisible"
        :side-panel-visible="sidePanelVisible"
        :sites-grid-visible="isSitesGridModalOpen"
        :is-dark-theme="isDarkTheme"
        @toggle-main-app="toggleMainApp"
        @toggle-chat-button="toggleChatButton"
        @toggle-chrome-panel="toggleChromePanel"
        @open-sites-grid="openSitesGrid"
        @open-plugin-config="openPluginConfig"
      />
    </div>

    <CollapseButton
      :is-collapsed="isCollapsed"
      :is-dark-theme="isDarkTheme"
      @click="handleButtonClick"
      @start-drag="handleStartDrag"
    />
  </div>

  <!-- Sites Grid Modal -->
  <el-dialog
    v-model="isSitesGridModalOpen"
    :title="t('menuHideControl.dialog.title')"
    width="clamp(600px, 95vw, 1400px)"
    height="clamp(600px, 85vh, 900px)"
    top="5vh"
    @close="closeSitesGridModal"
    destroy-on-close
    class="sites-grid-modal"
  >
    <div class="sites-grid-modal-content">
      <SitesGrid :isDarkTheme="isDarkTheme" />
    </div>
  </el-dialog>
</template>

<style scoped>
.menu-hide-control {
  position: fixed;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 999999;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 12px 8px;
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  user-select: none;
}

.menu-hide-control.dragging {
  transition: none !important;
  cursor: grabbing;
  z-index: 9999999;
}

.menu-hide-control.collapsed-mode {
  padding: 4px;
}

.menu-hide-control.on-left {
  right: auto;
  left: 10px;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  max-height: 290px;
  margin-bottom: 8px;
}

.control-buttons.collapsed {
  max-height: 0;
  gap: 0;
  margin-bottom: -8px;
}

/* Dark theme styles */
.menu-hide-control.dark-theme {
  background: rgba(38, 38, 38, 0.95);
  border-color: rgba(64, 64, 64, 0.6);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
}

/* Light theme styles */
.menu-hide-control.light-theme {
  background: rgba(229, 231, 235, 0.95);
  border-color: rgba(209, 213, 219, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
}

/* Sites Grid Modal Styles */
:deep(.sites-grid-modal) {
  --el-dialog-margin-top: 5vh;
}

:deep(.sites-grid-modal .el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh;
  height: clamp(600px, 85vh, 900px);
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

:deep(.sites-grid-modal .el-dialog__header) {
  flex-shrink: 0;
  padding: 16px 20px 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.sites-grid-modal .el-dialog__body) {
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sites-grid-modal-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 20px 20px;
}

/* Responsive adjustments for smaller screens */
@media (max-height: 700px) {
  :deep(.sites-grid-modal .el-dialog) {
    height: clamp(500px, 90vh, 800px);
    max-height: 90vh;
    margin-top: 2vh;
    margin-bottom: 2vh;
  }

  :deep(.sites-grid-modal) {
    --el-dialog-margin-top: 2vh;
  }
}

@media (max-height: 500px) {
  :deep(.sites-grid-modal .el-dialog) {
    height: 95vh;
    max-height: 95vh;
    margin-top: 1vh;
    margin-bottom: 1vh;
  }

  :deep(.sites-grid-modal) {
    --el-dialog-margin-top: 1vh;
  }
}
</style>
