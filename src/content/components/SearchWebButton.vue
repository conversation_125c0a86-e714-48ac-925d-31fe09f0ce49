<template>
  <el-button
    size="small"
    type="default"
    text
    bg
    class="chat-prompt-button"
    round
    @click="searchWeb"
    :disabled="!selectedBarId"
    :class="{ 'opacity-50': !selectedBarId }"
  >
    <el-icon><Search /></el-icon>
    {{ $t('messages.searchTheWeb') }}
  </el-button>
</template>

<script>
import { useI18n } from 'vue-i18n'

export default {
  name: 'SearchWebButton',
  props: {
    selectedBarId: {
      type: String,
      required: true,
    },
  },
  setup() {
    const { t } = useI18n()
    return { t }
  },
  methods: {
    searchWeb() {
      const promptText = ' - ' + this.t('messages.searchTheWeb')
      const outsideTextarea = document.querySelector('textarea')
      if (outsideTextarea) {
        let currentText = outsideTextarea.value || outsideTextarea.innerHTML
        const newText = `${currentText}${promptText}`
        if (outsideTextarea.value !== undefined) {
          outsideTextarea.value = newText
        } else {
          outsideTextarea.innerHTML = newText
        }

        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          which: 13,
          keyCode: 13,
          bubbles: true,
        })
        outsideTextarea.dispatchEvent(enterEvent)

        const checkButtonInterval = setInterval(() => {
          const searchResultButton = document.querySelector(
            'button.group.absolute.left-0.top-0.mr-1\\.5.h-\\[26px\\].overflow-hidden.juice\\:h-8',
          )
          if (searchResultButton) {
            searchResultButton.click()
            clearInterval(checkButtonInterval)
          }
        }, 500)
      }
    },
  },
}
</script>
