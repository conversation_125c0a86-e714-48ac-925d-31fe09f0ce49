<script setup>
import { storeToRefs } from 'pinia'
import { nextTick, ref, watch } from 'vue'

import Analytics from '../../Analytics'
import { useAppStateStore } from '../../stores/appState'
import { removeFromPluginStorageLocal, setToPluginStorageLocal } from '../localStorage'
import { chatSite } from '../sites_index.js'
import { usePromptStore } from '../stores/promptStore'
import { generatePromptRedirectLink } from '../utils'
import RecursiveMenu from './RecursiveMenu.vue'

const props = defineProps(['selectedBar', 'destination'])
// console.log('menu props: ', props)

const loadContent = ref(true)

const destinationDefault = ref(props.destination)

const appState = useAppStateStore()
const { hasSubscription, redirectedPrompt, activeTab, isAdmin } = storeToRefs(appState)

// Use store value instead of local computed property
// const hasSubscription = computed(() => appState.hasSubscription)

// Use store value instead of inject
// const redirectedPrompt = inject('redirectedPrompt')
// console.log('promptMenu redirectedPrompt: ', redirectedPrompt.value)

// Use store value instead of inject
// const activeTabName = inject('activeTabName')
// console.log('activeTabName: ', activeTabName.value)
const wasAutoInserted = ref(false)

const promptStore = usePromptStore()

const getOutsideSelectorFromChatSite = (sel) => {
  // Add null checks to prevent errors when chatSite is not properly initialized
  if (!chatSite || !chatSite.selector || !chatSite.selector[sel]) {
    return null
  }

  let outsideSelector = document.querySelector(chatSite.selector[sel])
  // console.log('outsideSelector: ', outsideSelector)
  // console.log('outsideSelector: ', chatSite.selectorAlters)
  // console.log('outsideSelector: ', chatSite.selectorShadows)
  if (chatSite.selectorAlters && chatSite.selectorAlters[sel]) {
    for (let i = 0; i < chatSite.selectorAlters[sel].length; i++) {
      outsideSelector = outsideSelector || document.querySelector(chatSite.selectorAlters[sel][i])
    }
  }
  if (chatSite.selectorShadows && chatSite.selectorShadows[sel]) {
    for (let i = 0; i < chatSite.selectorShadows[sel].length; i++) {
      outsideSelector = outsideSelector.shadowRoot.querySelector(chatSite.selectorShadows[sel][i])
    }
  }
  // console.log('whole outsideSelector: ', outsideSelector)
  return outsideSelector
}

const insertPrompt = (item, destination, type, target) => {
  // console.log('insertPrompt item', item, type, hasSubscription.value)
  if (!item) return

  Analytics.fireEvent('prompt_insert', {
    tab_name: activeTab.value,
    prompt_bar_id: props.selectedBar.id,
    prompt_id: item.id,
    type,
  })

  if (item.premium && !hasSubscription.value) return
  // console.log('insertPrompt: ', item)

  if (item.prompt != undefined) {
    if (ifPromptDestination(item, destination, target)) return

    // let promptTextarea =
    //   document.getElementById('prompt-textarea') ||
    //   document.getElementById('mat-input-0') ||
    //   document.querySelector('.ql-editor.textarea') ||
    //   document.querySelector('.text-textMain > .text-textMain') ||
    //   document
    //     .querySelector('.cib-serp-main')
    //     .shadowRoot.querySelector('#cib-action-bar-main')
    //     .shadowRoot.querySelector('.input-row cib-text-input')
    //     .shadowRoot.querySelector('textarea.text-area')

    let promptTextarea = getOutsideSelectorFromChatSite('textarea')

    // if (window.location.href.indexOf('bard.google.com') >= 0) {
    //   promptTextarea = document.getElementById('mat-input-0');
    // }

    if (promptTextarea.value !== undefined) {
      let elementType = promptTextarea.tagName.toLowerCase()

      if (elementType === 'textarea') {
        const nativeTextAreaValueSetter = Object.getOwnPropertyDescriptor(
          window.HTMLTextAreaElement.prototype,
          'value',
        ).set
        nativeTextAreaValueSetter.call(promptTextarea, promptTextarea.value + ' ' + item.prompt)
      } else if (elementType === 'input') {
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
          window.HTMLInputElement.prototype,
          'value',
        ).set
        nativeInputValueSetter.call(promptTextarea, promptTextarea.value + ' ' + item.prompt)
      } else {
        // Start of Selection
        console.error(`Unsupported element type: ${elementType}`)
      }

      // Trigger both input and change events to ensure Vue reactivity
      const inputEvent = new Event('input', { bubbles: true })
      promptTextarea.dispatchEvent(inputEvent)

      const changeEvent = new Event('change', { bubbles: true })
      promptTextarea.dispatchEvent(changeEvent)
    } else {
      promptTextarea.innerHTML = '<p>' + item.prompt + '</p>'
    }

    setTimeout(() => {
      promptTextarea.focus()
      // console.log('textFromTextarea', textFromTextarea.length)
      // promptTextarea.SelectionStart = item.prompt.length

      // var range = document.createRange()
      // var sel = window.getSelection()
      // range.setStart(promptTextarea.lastChild.childNodes[0], item.prompt.length)
      // range.collapse(true)
      // sel.removeAllRanges()
      // sel.addRange(range)
    }, 300)
  }
}

function ifPromptDestination(item, destination, target = '_self') {
  if (!destination) return false
  let itemDestination = item.destination || destinationDefault.value
  // console.log('itemDestination: ', itemDestination )
  let location = window.location.origin + window.location.pathname
  // console.log('location: ', location)

  if ((itemDestination && itemDestination !== location) || target === '_blank') {
    setToPluginStorageLocal('redirectedPrompt', {
      barId: props.selectedBar.id,
      promptId: item.id,
      activeTabName: activeTab.value,
    })
    // console.log('generatePromptRedirectLink: ', props.selectedBar, item, itemDestination)
    let link = generatePromptRedirectLink(itemDestination)
    // console.log('link: ', link)
    window.open(link, target)
    return true
  }
  return false
}

function insertPromptAuto() {
  let prompt_id = promptStore.sharedPromptId || redirectedPrompt?.value?.promptId
  if (!prompt_id) return

  nextTick(() => {
    let checkButtonInterval = setInterval(() => {
      let findBtn = document.querySelector('button[id="' + prompt_id + '"]')
      let promptTextarea = getOutsideSelectorFromChatSite('textarea')
      if (findBtn && promptTextarea) {
        clearInterval(checkButtonInterval)
        findBtn.click()

        redirectedPrompt.value = ''
        removeFromPluginStorageLocal('redirectedPrompt')
        if (promptStore.sharedPromptId) {
          promptStore.closePromptPopup()
        }
      }
    }, 500)

    setTimeout(() => {
      clearInterval(checkButtonInterval)
    }, 5000)
  })
}

const onMenuOpen = () => {
  // console.log('onMenuOpen')
  loadContent.value = true
}

const onMenuClose = () => {
  // console.log('onMenuClose')
}

const promptClicked = ({ item, destination, type = 'default', target = '_self' }) => {
  if (destination) destinationDefault.value = destination
  insertPrompt(item, destination, type, target)
}

const onPromptClickUrl = ({ item, destination, type, target }) => {
  if (destination) {
    let link = generatePromptRedirectLink(destination)
    window.open(link, target)
  }
}

if (redirectedPrompt.value) {
  // console.log('menu hasSubscription: ', hasSubscription.value)
  if (hasSubscription.value !== undefined) {
    // fetchDataAndInsertPrompt()
    // console.log('set prompt from stroge if any')
    wasAutoInserted.value = true
    nextTick(() => insertPromptAuto())
  }
  watch(
    () => hasSubscription.value,
    (newVal, oldVal) => {
      // console.log('watch hasSubscription: ', newVal, oldVal)
      if (newVal !== undefined && !wasAutoInserted.value) {
        // console.info('Subscription checked! Set prompt from stroge if any')
        // fetchDataAndInsertPrompt()
        nextTick(() => insertPromptAuto())
      }
    },
  )
}
</script>

<template>
  <el-menu
    class="el-menu-recursive justify-center flex-1 gap-2 flex-wrap max-h-screen"
    mode="horizontal"
    :unique-opened="true"
    :collapse-transition="false"
    @open="onMenuOpen()"
    @close="onMenuClose()"
  >
    <RecursiveMenu
      :data="props.selectedBar.promptMenu"
      :isAdmin="isAdmin"
      :loadContent="loadContent"
      :destination="props.destination"
      @onPromptClick="promptClicked"
      @onPromptClickUrl="onPromptClickUrl"
    />
  </el-menu>

  <!-- <el-menu
    class="el-menu-demo"
    mode="horizontal"
  >
    <el-menu-item index="1">Processing Center</el-menu-item>
    <el-sub-menu index="2">
      <template #title>Workspace</template>
      <el-menu-item index="2-1">item one</el-menu-item>
      <el-menu-item index="2-2">item two</el-menu-item>
      <el-menu-item index="2-3">item three</el-menu-item>
      <el-sub-menu index="2-4">
        <template #title>item four</template>
        <el-menu-item index="2-4-1">item one</el-menu-item>
        <el-menu-item index="2-4-2">item two</el-menu-item>
        <el-menu-item index="2-4-3">item three</el-menu-item>
      </el-sub-menu>
    </el-sub-menu>
    <el-menu-item index="3" disabled>Info</el-menu-item>
    <el-menu-item index="4">Orders</el-menu-item>
  </el-menu> -->
</template>

<style scoped>
.el-menu--horizontal {
  --el-menu-horizontal-height: auto;
}
.el-menu-recursive :deep(.el-popper) {
  max-height: 95vh;
  max-height: 95svh;
  z-index: 9999999 !important;
  /* overflow-y: auto; */
}
.el-menu-recursive :deep(.prompt-recursiveMenu) {
  /* overflow: unset; */
}
:deep(.el-button .el-icon) {
  font-size: inherit;
}

/* Style dla menu rozwijanych - tylko z-index, bez przenoszenia */
.el-menu-recursive :deep(.el-menu--popup) {
  z-index: 9999999 !important;
}
.el-menu-recursive :deep(.el-sub-menu__popup) {
  z-index: 9999999 !important;
}
.el-menu-recursive :deep(.el-popover) {
  z-index: 9999999 !important;
}
</style>
