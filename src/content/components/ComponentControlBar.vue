<template>
  <div class="control-panel-wrapper">
    <div class="control-toggle" :class="controlBarThemeClasses">
      <div class="toggle-controls">
        <ThemeSwitcher />
        <LanguageSelector />
        <ProfileSwitcher
          :selectedProfileId="selectedProfileId"
          :savedProfiles="savedProfiles"
          :isLoading="isPersistenceLoading"
          @profile-changed="handleProfileChanged"
        />
        <el-tooltip
          :content="isPanelVisible ? t('componentControlBar.hide') : t('componentControlBar.show')"
          placement="top"
        >
          <el-button
            @click="togglePanelVisibility"
            :type="isPanelVisible ? 'primary' : 'default'"
            size="small"
            class="panel-toggle-btn"
          >
            <el-icon class="toggle-icon">
              <Setting />
            </el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- Loading and error indicators -->
      <div v-if="isPersistenceLoading || hasPersistenceError" class="status-indicators">
        <div v-if="isPersistenceLoading" class="loading-indicator">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span class="loading-text">{{ t('componentControlBar.loading') }}</span>
        </div>
        <div v-if="hasPersistenceError" class="error-indicator">
          <el-tooltip :content="persistenceErrorMessage" placement="top">
            <el-icon class="error-icon">
              <Warning />
            </el-icon>
            <span class="error-text">{{ t('componentControlBar.error') }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>

    <Transition name="panel-slide">
      <div v-if="isPanelVisible" class="control-bar" :class="controlBarThemeClasses">
        <!-- Profile Management Section -->
        <div class="profile-management">
          <div class="profile-controls">
            <ProfileSelector
              v-model:selectedProfileId="selectedProfileId"
              :savedProfiles="savedProfiles"
              :isLoading="isPersistenceLoading"
              :isDarkTheme="isDarkTheme"
              @profile-change="loadProfile"
            />

            <div class="profile-actions">
              <el-button
                @click="showSaveDialog = true"
                type="success"
                size="small"
                :disabled="isPersistenceLoading"
                :icon="DocumentAdd"
              >
                {{ t('componentControlBar.profile.add') }}
              </el-button>

              <el-button
                v-if="
                  selectedProfileId !== 'default' &&
                  selectedProfileId !== 'prompt' &&
                  selectedProfileId !== 'search'
                "
                @click="overwriteProfile"
                type="warning"
                size="small"
                :disabled="isPersistenceLoading"
                :icon="Edit"
              >
                {{ t('componentControlBar.profile.overwrite') }}
              </el-button>

              <el-button
                v-if="
                  selectedProfileId !== 'default' &&
                  selectedProfileId !== 'prompt' &&
                  selectedProfileId !== 'search'
                "
                @click="deleteProfile"
                type="danger"
                size="small"
                :disabled="isPersistenceLoading"
                :icon="Delete"
              >
                {{ t('componentControlBar.profile.delete') }}
              </el-button>
            </div>
          </div>

          <!-- Save Profile Dialog -->
          <el-dialog
            v-model="showSaveDialog"
            :title="t('componentControlBar.dialog.saveProfile')"
            width="400px"
            :before-close="cancelSaveDialog"
            append-to-body
          >
            <el-form @submit.prevent="saveCurrentProfile">
              <el-form-item :label="t('componentControlBar.dialog.profileName')">
                <el-input
                  v-model="newProfileName"
                  :placeholder="t('componentControlBar.dialog.profileNamePlaceholder')"
                  @keyup.enter="saveCurrentProfile"
                  ref="profileNameInput"
                  clearable
                />
              </el-form-item>
            </el-form>

            <template #footer>
              <div class="dialog-footer">
                <el-button @click="cancelSaveDialog">{{
                  t('componentControlBar.profile.cancel')
                }}</el-button>
                <el-button
                  type="primary"
                  @click="saveCurrentProfile"
                  :disabled="!newProfileName.trim()"
                >
                  {{ t('componentControlBar.profile.save') }}
                </el-button>
              </div>
            </template>
          </el-dialog>
        </div>

        <div class="component-controls">
          <el-card
            v-for="component in components"
            :key="component.id"
            class="component-item"
            :draggable="true"
            @dragstart="onDragStart($event, component.id)"
            @dragover="onDragOver($event)"
            @drop="onDrop($event, component.id)"
            @dragenter="onDragEnter($event)"
            @dragleave="onDragLeave($event)"
            shadow="hover"
            :body-style="{ padding: '8px 12px' }"
          >
            <div class="component-item-content">
              <div class="drag-handle">
                <el-icon class="drag-icon">
                  <Grid />
                </el-icon>
              </div>
              <div class="component-info">
                <el-button
                  @click="toggleVisibility(component.id)"
                  :type="component.visible ? 'primary' : 'info'"
                  size="small"
                  circle
                  class="toggle-btn"
                >
                  <el-icon>
                    <View v-if="component.visible" />
                    <Hide v-else />
                  </el-icon>
                </el-button>

                <div class="reorder-controls">
                  <el-tooltip :content="t('componentControlBar.moveUp')" placement="top">
                    <el-button
                      @click="moveComponentUp(component.id)"
                      :disabled="component.order === 0"
                      size="small"
                      text
                      class="reorder-btn reorder-btn-up"
                    >
                      <el-icon class="reorder-icon arrow-up">
                        <ArrowUpBold />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip :content="t('componentControlBar.moveDown')" placement="top">
                    <el-button
                      @click="moveComponentDown(component.id)"
                      :disabled="component.order === components.length - 1"
                      size="small"
                      text
                      class="reorder-btn reorder-btn-down"
                    >
                      <el-icon class="reorder-icon arrow-down">
                        <ArrowDownBold />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                </div>

                <div class="component-text-container">
                  <span class="component-label">{{ component.label }}</span>
                  <span class="component-name">{{ component.name }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import {
  ArrowDownBold,
  ArrowUpBold,
  Delete,
  DocumentAdd,
  Edit,
  Grid,
  Hide,
  Loading,
  Setting,
  View,
  Warning,
} from '@element-plus/icons-vue'
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import { useComponentStatePersistence } from '../composables/useComponentStatePersistence'
import { useProfileSync } from '../composables/useProfileSync'
import { settingsRef, usersRef } from '../firebase'
import LanguageSelector from './LanguageSelector.vue'
import ProfileSelector from './ProfileSelector.vue'
import ProfileSwitcher from './ProfileSwitcher.vue'
import ThemeSwitcher from './ThemeSwitcher.vue'

const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:components', 'update:theme', 'profile-data-updated'])

const { t } = useI18n()
const user = useCurrentUser()

// Profile synchronization management
const { selectedProfileId, savedProfiles, setProfile, updateSavedProfiles, initializeProfileSync } =
  useProfileSync()

// Default state for the component
const DEFAULT_STATE = {
  isPanelVisible: false,
  isDarkTheme: false,
  components: [
    { id: 'app', name: 'House', label: 'APP', visible: true, order: 0 },
    { id: 'aitools', name: 'Robot', label: 'AI Tools', visible: true, order: 1 },
    { id: 'textfield', name: 'Edit', label: 'Text Field', visible: true, order: 2 },
    { id: 'sitesgrid', name: 'Grid', label: 'Sites Grid', visible: true, order: 3 },
  ],
  textareaHeight: null, // Default textarea height (null means use default)
}

// Predefined application states
const PROMPT_STATE = {
  isPanelVisible: false,
  isDarkTheme: false,
  components: [
    { id: 'app', name: 'House', label: 'APP', visible: true, order: 0 },
    { id: 'aitools', name: 'Robot', label: 'AI Tools', visible: true, order: 1 },
    { id: 'textfield', name: 'Edit', label: 'Text Field', visible: true, order: 2 },
    { id: 'sitesgrid', name: 'Grid', label: 'Sites Grid', visible: false, order: 3 },
  ],
  textareaHeight: 300,
}

const SEARCH_STATE = {
  isPanelVisible: false,
  isDarkTheme: false,
  components: [
    { id: 'app', name: 'Operation', label: 'APP', visible: false, order: 0 },
    { id: 'aitools', name: 'Robot', label: 'AI Tools', visible: false, order: 1 },
    { id: 'textfield', name: 'Edit', label: 'Text Field', visible: false, order: 2 },
    { id: 'sitesgrid', name: 'Grid', label: 'Sites Grid', visible: true, order: 3 },
  ],
  textareaHeight: 300,
}

const isPanelVisible = ref(DEFAULT_STATE.isPanelVisible)
const components = ref([...DEFAULT_STATE.components])
const textareaHeight = ref(DEFAULT_STATE.textareaHeight) // Stores the current textarea height

// Profile management state
const showSaveDialog = ref(false)
const newProfileName = ref('')
const profileNameInput = ref(null)
const previousLegacyDataHash = ref('')

let draggedElement = null

const SETTINGS_DOC_ID = 'componentControlBar'
const PROFILES_DOC_ID = 'componentProfiles'

// Validation function for component data
const validateComponentState = (state) => {
  if (!state || typeof state !== 'object') return false

  // Validate panel visibility
  if (typeof state.isPanelVisible !== 'boolean') return false

  // Validate components array
  if (!Array.isArray(state.components)) return false

  const requiredFields = ['id', 'name', 'label', 'visible', 'order']
  return state.components.every(
    (component) =>
      typeof component === 'object' &&
      component !== null &&
      requiredFields.every((field) => component.hasOwnProperty(field)) &&
      typeof component.id === 'string' &&
      typeof component.name === 'string' &&
      typeof component.label === 'string' &&
      typeof component.visible === 'boolean' &&
      typeof component.order === 'number',
  )
}

// Firestore handler for the persistence composable
const firestoreHandler = {
  save: async (data) => {
    if (!user.value?.uid) return

    try {
      const userRef = doc(usersRef, user.value.uid)

      await setDoc(
        userRef,
        {
          componentControlBarSettings: {
            ...data,
            updatedAt: new Date().toISOString(),
          },
        },
        { merge: true },
      )
    } catch (error) {
      console.error('💾 Error saving component settings to Firebase users collection:', error)
      throw error
    }
  },

  load: async () => {
    if (!user.value?.uid) return null

    try {
      // First, try to load from the new users collection
      const userRef = doc(usersRef, user.value.uid)
      const userSnap = await getDoc(userRef)

      let dataLoaded = false
      let loadedData = null

      if (userSnap.exists()) {
        const userData = userSnap.data()
        if (userData.componentControlBarSettings) {
          loadedData = userData.componentControlBarSettings
          dataLoaded = true
        }
      }

      // If no data in users collection, check the old settings collection for migration
      if (!dataLoaded) {
        const oldSettingsRef = doc(settingsRef, `${SETTINGS_DOC_ID}_${user.value.uid}`)
        const oldSettingsSnap = await getDoc(oldSettingsRef)

        if (oldSettingsSnap.exists()) {
          const oldData = oldSettingsSnap.data()
          loadedData = oldData

          // Save to new location (users collection)
          await firestoreHandler.save(oldData)
          dataLoaded = true

          // Note: We're not removing the old data immediately to ensure safe migration
        }
      }

      return loadedData
    } catch (error) {
      console.error('❌ Error loading component settings from Firebase:', error)
      return null
    }
  },

  clear: async () => {
    if (!user.value?.uid) return

    try {
      const userRef = doc(usersRef, user.value.uid)

      await setDoc(
        userRef,
        {
          componentControlBarSettings: {},
        },
        { merge: true },
      )
    } catch (error) {
      console.error('🗑️ Error clearing component settings:', error)
      throw error
    }
  },
}

// Setup persistence composable
const {
  isLoading: isPersistenceLoading,
  hasError: hasPersistenceError,
  errorMessage: persistenceErrorMessage,
  lastSaved,
  storageType,
  initializePersistence,
  setupAutoPersistence,
  resetToDefaults,
  saveState,
} = useComponentStatePersistence('componentControlBar', DEFAULT_STATE, {
  debounceMs: 500,
  validateData: validateComponentState,
  enableFirestore: true,
  firestoreHandler,
})

const controlBarThemeClasses = computed(() => ({
  'control-bar--dark': props.isDarkTheme,
  'control-bar--light': !props.isDarkTheme,
}))

// Get current component state
const getCurrentState = () => ({
  isPanelVisible: isPanelVisible.value,
  isDarkTheme: props.isDarkTheme,
  components: components.value,
  textareaHeight: textareaHeight.value,
})

// Apply loaded state to component
const applyLoadedState = (state) => {
  if (state.isPanelVisible !== undefined) {
    isPanelVisible.value = state.isPanelVisible
  }
  if (state.components && Array.isArray(state.components)) {
    components.value = [...state.components]
  }
  if (state.isDarkTheme !== undefined) {
    // Emit theme change to parent component
    emit('update:theme', state.isDarkTheme)
  }
  if (state.textareaHeight !== undefined) {
    textareaHeight.value = state.textareaHeight
    // Apply textarea height to all TextFieldComponent instances
    applyTextareaHeight(state.textareaHeight)
  }
}

// Textarea Height Management Functions
const applyTextareaHeight = (height) => {
  if (!height) return

  // Apply height to all TextFieldComponent instances
  nextTick(() => {
    const textareas = document.querySelectorAll('.text-field-textarea .el-textarea__inner')
    textareas.forEach((textarea) => {
      if (textarea && height > 0) {
        textarea.style.height = `${height}px`
      }
    })
  })
}

const saveTextareaHeight = (height) => {
  if (height && height > 0) {
    textareaHeight.value = height
  }
}

const setupTextareaHeightObserver = () => {
  // Setup listeners for existing textareas
  const setupExistingTextareas = () => {
    const existingTextareas = document.querySelectorAll('.text-field-textarea .el-textarea__inner')
    existingTextareas.forEach((textarea) => {
      setupTextareaResizeListener(textarea)
      // Apply saved height if available
      if (textareaHeight.value && textareaHeight.value > 0) {
        textarea.style.height = `${textareaHeight.value}px`
      }
    })
  }

  // Setup existing textareas immediately
  setupExistingTextareas()

  // Also setup after a short delay to catch dynamically loaded components
  setTimeout(setupExistingTextareas, 100)
  setTimeout(setupExistingTextareas, 500)

  // Use MutationObserver to detect when TextFieldComponent instances are added
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const textareas = node.querySelectorAll
            ? node.querySelectorAll('.text-field-textarea .el-textarea__inner')
            : []
          textareas.forEach((textarea) => {
            setupTextareaResizeListener(textarea)
            // Apply saved height if available
            if (textareaHeight.value && textareaHeight.value > 0) {
              textarea.style.height = `${textareaHeight.value}px`
            }
          })
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  return observer
}

const setupTextareaResizeListener = (textarea) => {
  if (!textarea || textarea.dataset.heightListenerAdded) return

  // Mark as having listener to avoid duplicates
  textarea.dataset.heightListenerAdded = 'true'

  // Use ResizeObserver to detect height changes
  const resizeObserver = new ResizeObserver((entries) => {
    entries.forEach((entry) => {
      const height = entry.contentRect.height
      if (height > 0 && height !== textareaHeight.value) {
        saveTextareaHeight(height)
      }
    })
  })

  resizeObserver.observe(textarea)

  // Also listen for manual resize events
  textarea.addEventListener('mouseup', () => {
    const height = textarea.offsetHeight
    if (height > 0 && height !== textareaHeight.value) {
      saveTextareaHeight(height)
    }
  })
}

// Profile Management Functions
const generateProfileId = () => {
  return 'profile_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

const saveCurrentProfile = async () => {
  if (!newProfileName.value.trim()) return

  try {
    const profileId = generateProfileId()
    const profile = {
      id: profileId,
      name: newProfileName.value.trim(),
      state: getCurrentState(),
      createdAt: new Date().toISOString(),
      userId: user.value?.uid || 'anonymous',
    }

    // Add to saved profiles
    savedProfiles.value.push(profile)
    updateSavedProfiles(savedProfiles.value)

    // Save profiles to storage
    await saveProfilesToStorage()

    // Select the new profile
    setProfile(profileId)

    // Close dialog
    showSaveDialog.value = false
    newProfileName.value = ''
  } catch (error) {
    console.error('ComponentControlBar: błąd podczas zapisywania profilu:', error)
  }
}

const overwriteProfile = async () => {
  if (selectedProfileId.value === 'default') return

  try {
    // Find the existing profile
    const profileIndex = savedProfiles.value.findIndex((p) => p.id === selectedProfileId.value)
    if (profileIndex !== -1) {
      const existingProfile = savedProfiles.value[profileIndex]

      // Update the profile with current state
      const updatedProfile = {
        ...existingProfile,
        state: getCurrentState(),
        updatedAt: new Date().toISOString(),
        userId: user.value?.uid || 'anonymous',
      }

      // Replace the profile in the array
      savedProfiles.value[profileIndex] = updatedProfile

      // Save profiles to storage
      await saveProfilesToStorage()
    }
  } catch (error) {
    console.error('ComponentControlBar: błąd podczas nadpisywania profilu:', error)
  }
}

const loadProfile = async () => {
  try {
    console.log(`ComponentControlBar: Loading profile: ${selectedProfileId.value}`)

    // Preserve current panel visibility state
    const currentPanelVisibility = isPanelVisible.value

    if (selectedProfileId.value === 'default') {
      console.log('ComponentControlBar: Loading DEFAULT profile')
      // Load default state but preserve panel visibility
      const stateToApply = { ...DEFAULT_STATE, isPanelVisible: currentPanelVisibility }
      applyLoadedState(stateToApply)
    } else if (selectedProfileId.value === 'prompt') {
      console.log('ComponentControlBar: Loading PROMPT profile')
      // Load prompt state but preserve panel visibility
      const stateToApply = { ...PROMPT_STATE, isPanelVisible: currentPanelVisibility }
      applyLoadedState(stateToApply)
    } else if (selectedProfileId.value === 'search') {
      console.log('ComponentControlBar: Loading SEARCH profile')
      // Load search state but preserve panel visibility
      const stateToApply = { ...SEARCH_STATE, isPanelVisible: currentPanelVisibility }
      applyLoadedState(stateToApply)
    } else {
      // Find and load selected profile
      const profile = savedProfiles.value.find((p) => p.id === selectedProfileId.value)
      if (profile && profile.state) {
        console.log(`ComponentControlBar: Loading custom profile: ${profile.name}`)
        // Load profile state but preserve panel visibility
        const stateToApply = { ...profile.state, isPanelVisible: currentPanelVisibility }
        applyLoadedState(stateToApply)
      } else {
        console.warn(`ComponentControlBar: Custom profile not found: ${selectedProfileId.value}`)
      }
    }

    console.log('ComponentControlBar: Emitting updated components:', components.value)
    emit('update:components', [...components.value])
  } catch (error) {
    console.error('ComponentControlBar: błąd podczas ładowania profilu:', error)
  }
}

const deleteProfile = async () => {
  // Prevent deletion of predefined states
  if (
    selectedProfileId.value === 'default' ||
    selectedProfileId.value === 'prompt' ||
    selectedProfileId.value === 'search'
  )
    return

  try {
    const profileIndex = savedProfiles.value.findIndex((p) => p.id === selectedProfileId.value)
    if (profileIndex !== -1) {
      const profileName = savedProfiles.value[profileIndex].name
      savedProfiles.value.splice(profileIndex, 1)

      // Update composable
      updateSavedProfiles(savedProfiles.value)

      // Save updated profiles
      await saveProfilesToStorage()

      // Switch to default profile but preserve panel visibility
      const currentPanelVisibility = isPanelVisible.value
      setProfile('default')
      const stateToApply = { ...DEFAULT_STATE, isPanelVisible: currentPanelVisibility }
      applyLoadedState(stateToApply)
      emit('update:components', [...components.value])
    }
  } catch (error) {
    console.error('ComponentControlBar: błąd podczas usuwania profilu:', error)
  }
}

const cancelSaveDialog = () => {
  showSaveDialog.value = false
  newProfileName.value = ''
}

const saveProfilesToStorage = async () => {
  try {
    if (user.value?.uid) {
      // Save to Firestore users collection for authenticated users
      const userRef = doc(usersRef, user.value.uid)

      await setDoc(
        userRef,
        {
          componentProfiles: {
            profiles: savedProfiles.value,
            updatedAt: new Date().toISOString(),
          },
        },
        { merge: true },
      )
    }

    // Also save to local storage as backup
    localStorage.setItem('componentProfiles', JSON.stringify(savedProfiles.value))
  } catch (error) {
    console.error('ComponentControlBar: Error saving profiles to users collection:', error)
  }
}

const loadProfilesFromStorage = async () => {
  try {
    let loadedProfiles = []

    // Try to load from new users collection first
    if (user.value?.uid) {
      const userRef = doc(usersRef, user.value.uid)
      const userSnap = await getDoc(userRef)

      let dataLoaded = false

      if (userSnap.exists()) {
        const userData = userSnap.data()
        if (
          userData.componentProfiles &&
          userData.componentProfiles.profiles &&
          Array.isArray(userData.componentProfiles.profiles)
        ) {
          loadedProfiles = userData.componentProfiles.profiles
          dataLoaded = true
        }
      }

      // If no data in users collection, check the old settings collection for migration
      if (!dataLoaded) {
        const oldProfilesRef = doc(settingsRef, `${PROFILES_DOC_ID}_${user.value.uid}`)
        const oldProfilesSnap = await getDoc(oldProfilesRef)

        if (oldProfilesSnap.exists()) {
          const oldData = oldProfilesSnap.data()
          if (oldData.profiles && Array.isArray(oldData.profiles)) {
            loadedProfiles = oldData.profiles

            // Save to new location (users collection)
            savedProfiles.value = loadedProfiles
            await saveProfilesToStorage()
            dataLoaded = true
          }
        }
      }
    }

    // Fallback to localStorage if no Firestore data
    if (loadedProfiles.length === 0) {
      const localProfiles = localStorage.getItem('componentProfiles')
      if (localProfiles) {
        try {
          const parsed = JSON.parse(localProfiles)
          if (Array.isArray(parsed)) {
            loadedProfiles = parsed
          }
        } catch (e) {
          console.warn('ComponentControlBar: Error parsing local profiles:', e)
        }
      }
    }

    savedProfiles.value = loadedProfiles
    updateSavedProfiles(loadedProfiles)
  } catch (error) {
    console.error('ComponentControlBar: Error loading profiles:', error)
  }
}

const generateLegacyDataHash = () => {
  return JSON.stringify({
    components: components.value,
    panelVisible: isPanelVisible.value,
  })
}

// Legacy Firestore functions (updated to use users collection for consistency)
const saveToFirestore = async (forceUpdate = false) => {
  try {
    if (!user.value?.uid) {
      return
    }

    // Check if data actually changed
    const currentDataHash = generateLegacyDataHash()
    if (!forceUpdate && currentDataHash === previousLegacyDataHash.value) {
      return
    }

    const dataToSave = {
      components: components.value,
      panelVisible: isPanelVisible.value,
      updatedAt: new Date().toISOString(),
    }

    const userRef = doc(usersRef, user.value.uid)
    await setDoc(
      userRef,
      {
        componentControlBarLegacy: dataToSave,
      },
      { merge: true },
    )

    // Update the hash after successful save
    previousLegacyDataHash.value = currentDataHash
  } catch (error) {
    console.error('💾 Legacy save: Failed to save to users collection:', error)
  }
}

// Legacy validation function (kept for backward compatibility)
const validateComponentData = (data) => {
  if (!Array.isArray(data)) return false

  const requiredFields = ['id', 'name', 'label', 'visible', 'order']
  return data.every(
    (component) =>
      typeof component === 'object' &&
      component !== null &&
      requiredFields.every((field) => component.hasOwnProperty(field)) &&
      typeof component.id === 'string' &&
      typeof component.name === 'string' &&
      typeof component.label === 'string' &&
      typeof component.visible === 'boolean' &&
      typeof component.order === 'number',
  )
}

// Legacy Firestore load function (updated to use users collection)
const loadFromFirestore = async () => {
  try {
    if (!user.value?.uid) {
      return
    }

    const userRef = doc(usersRef, user.value.uid)
    const userSnap = await getDoc(userRef)

    if (userSnap.exists()) {
      const userData = userSnap.data()

      // Check for legacy data in users collection
      if (userData.componentControlBarLegacy) {
        const data = userData.componentControlBarLegacy

        if (data.components && validateComponentData(data.components)) {
          components.value = data.components
        }

        if (typeof data.panelVisible === 'boolean') {
          isPanelVisible.value = data.panelVisible
        }
      }
    }
  } catch (error) {
    console.error('❌ Legacy load: Failed to load from users collection:', error)
  }
}

const togglePanelVisibility = () => {
  isPanelVisible.value = !isPanelVisible.value
}

const handleProfileChanged = (profileId) => {
  setProfile(profileId)
  // loadProfile() will be called automatically by the watcher
}

// Emit profile data updates to parent components
const emitProfileDataUpdate = () => {
  emit('profile-data-updated', {
    selectedProfileId: selectedProfileId.value,
    savedProfiles: savedProfiles.value,
  })
}

const toggleVisibility = (componentId) => {
  const component = components.value.find((c) => c.id === componentId)
  if (component) {
    component.visible = !component.visible
    emit('update:components', [...components.value])
  }
}

const moveComponentUp = (componentId) => {
  const currentIndex = components.value.findIndex((c) => c.id === componentId)
  if (currentIndex > 0) {
    const newComponents = [...components.value]
    // Swap with the component above
    ;[newComponents[currentIndex - 1], newComponents[currentIndex]] = [
      newComponents[currentIndex],
      newComponents[currentIndex - 1],
    ]

    // Update order properties
    newComponents.forEach((component, index) => {
      component.order = index
    })

    components.value = newComponents
    emit('update:components', [...components.value])
  }
}

const moveComponentDown = (componentId) => {
  const currentIndex = components.value.findIndex((c) => c.id === componentId)
  if (currentIndex < components.value.length - 1) {
    const newComponents = [...components.value]
    // Swap with the component below
    ;[newComponents[currentIndex], newComponents[currentIndex + 1]] = [
      newComponents[currentIndex + 1],
      newComponents[currentIndex],
    ]

    // Update order properties
    newComponents.forEach((component, index) => {
      component.order = index
    })

    components.value = newComponents
    emit('update:components', [...components.value])
  }
}

const onDragStart = (event, componentId) => {
  draggedElement = componentId
  event.dataTransfer.effectAllowed = 'move'
  event.target.style.opacity = '0.5'
}

const onDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
}

const onDragEnter = (event) => {
  event.preventDefault()
  event.target.classList.add('drag-over')
}

const onDragLeave = (event) => {
  event.target.classList.remove('drag-over')
}

const onDrop = (event, targetComponentId) => {
  event.preventDefault()
  event.target.classList.remove('drag-over')

  if (draggedElement && draggedElement !== targetComponentId) {
    const draggedIndex = components.value.findIndex((c) => c.id === draggedElement)
    const targetIndex = components.value.findIndex((c) => c.id === targetComponentId)

    const draggedComponent = components.value[draggedIndex]
    components.value.splice(draggedIndex, 1)
    components.value.splice(targetIndex, 0, draggedComponent)

    components.value.forEach((component, index) => {
      component.order = index
    })

    emit('update:components', [...components.value])
  }

  document.querySelectorAll('.component-item').forEach((el) => {
    el.style.opacity = '1'
  })

  draggedElement = null
}

onMounted(async () => {
  // Initialize profile synchronization
  initializeProfileSync()

  // Load saved profiles first
  await loadProfilesFromStorage()

  // Initialize persistence and load saved state
  try {
    const loadedState = await initializePersistence()
    applyLoadedState(loadedState)

    // Force load the current profile state AFTER loading persistence
    // This ensures components match the selected profile
    console.log(
      `ComponentControlBar: Force loading profile after persistence: ${selectedProfileId.value}`,
    )
    await loadProfile()

    emit('update:components', [...components.value])
    // Initialize the hash for legacy data comparison
    previousLegacyDataHash.value = generateLegacyDataHash()
  } catch (error) {
    console.error('ComponentControlBar: failed to initialize persistence:', error)
  }

  // Setup automatic persistence for state changes
  setupAutoPersistence(() => getCurrentState())

  // Setup textarea height observer and apply saved height
  setupTextareaHeightObserver()

  // Apply saved textarea height to existing textareas
  if (textareaHeight.value && textareaHeight.value > 0) {
    applyTextareaHeight(textareaHeight.value)
  }

  // Check if current profile needs to be loaded immediately after mount
  nextTick(async () => {
    // If selectedProfileId is not 'default' but we're showing default state,
    // we need to force load the correct profile
    if (selectedProfileId.value !== 'default') {
      console.log(
        `ComponentControlBar: Auto-loading non-default profile: ${selectedProfileId.value}`,
      )
      await loadProfile()
      emit('update:components', [...components.value])
    }
  })

  // Watch for user login/logout and reinitialize if needed
  watch(
    () => user.value?.uid,
    async (newUserId, oldUserId) => {
      if (newUserId && newUserId !== oldUserId) {
        try {
          const loadedState = await initializePersistence()
          applyLoadedState(loadedState)
          emit('update:components', [...components.value])
        } catch (error) {
          console.error('ComponentControlBar: failed to reinitialize for new user:', error)
        }
      }
    },
    { immediate: false },
  )

  // Watch components changes and emit updates
  watch(
    () => components.value,
    () => {
      emit('update:components', [...components.value])
    },
    { deep: true },
  )

  // Legacy Firestore sync (for backward compatibility)
  watch(
    () => [components.value, isPanelVisible.value],
    () => {
      if (user.value?.uid) {
        saveToFirestore()
      }
    },
    { deep: true },
  )
})

// Watch for save dialog to focus input
watch(showSaveDialog, (newValue) => {
  if (newValue) {
    // Focus the input when dialog opens
    setTimeout(() => {
      if (profileNameInput.value) {
        profileNameInput.value.focus()
      }
    }, 100)
  }
})

// Watch for user changes to reload profiles
watch(
  () => user.value?.uid,
  async (newUserId, oldUserId) => {
    if (newUserId && newUserId !== oldUserId) {
      await loadProfilesFromStorage()
    }
  },
)

// Watch for profile data changes and emit updates
watch(
  [selectedProfileId, savedProfiles],
  () => {
    emitProfileDataUpdate()
  },
  { deep: true },
)

// Watch for external profile changes and auto-load profile
watch(
  selectedProfileId,
  (newProfileId, oldProfileId) => {
    if (newProfileId !== oldProfileId) {
      console.log(`ComponentControlBar: Profile changed externally to: ${newProfileId}`)
      loadProfile()
    }
  },
  { immediate: false },
)

// Emit initial profile data on mount
onMounted(() => {
  nextTick(() => {
    emitProfileDataUpdate()
  })
})

// Expose methods and state for parent components
defineExpose({
  saveState: () => saveState(getCurrentState()),
  getCurrentState,
  isPersistenceLoading,
  hasPersistenceError,
  persistenceErrorMessage,
  lastSaved,
  storageType,
  // Profile management methods and state
  handleProfileChanged,
  selectedProfileId,
  savedProfiles,
  // Textarea height management
  textareaHeight,
  saveTextareaHeight,
  applyTextareaHeight,
})
</script>

<style scoped>
.control-toggle {
  @apply flex justify-center;
}

.toggle-controls,
.profile-controls {
  @apply flex flex-row gap-1 p-2 justify-center;
}

.component-controls {
  @apply grid grid-cols-2 sm:grid-cols-4 gap-2 p-2 justify-center;
}

.component-item-content,
.drag-handle,
.component-info {
  @apply flex items-center gap-2;
}

.drag-handle {
  @apply cursor-grab;
}

.reorder-controls {
  @apply flex flex-col gap-1 items-center justify-center h-full m-0;
}

.reorder-btn {
  @apply w-4 h-4 p-0 !m-0 border-none bg-transparent transition-all duration-200 flex items-center justify-center box-border flex-shrink-0;
}
</style>
