<template>
  <div
    :class="['site-list-item', { 'edit-mode': showDragHandle, 'dark-theme': isDarkTheme }]"
    :draggable="showDragHandle"
    @dragstart="$emit('dragstart', $event)"
    @dragover="$emit('dragover', $event)"
    @drop="$emit('drop', $event)"
  >
    <div class="site-list-content">
      <!-- Config button (gear icon) - left side in edit mode -->
      <el-tooltip v-if="showConfigIcon" :content="t('siteCard.config.tooltip')" placement="top">
        <el-button
          @click.stop="handleConfigClick"
          class="config-button-list"
          size="small"
          :icon="Setting"
          circle
          type="info"
        />
      </el-tooltip>

      <div class="site-list-info">
        <el-text
          :type="isDarkTheme ? 'primary' : 'default'"
          size="large"
          class="site-list-name"
          truncated
        >
          {{ site.name }}
        </el-text>

        <el-text type="info" size="small" class="site-list-url" truncated>
          {{ displayUrl }}
        </el-text>
      </div>

      <div v-if="site.tags && site.tags.length > 0" class="site-list-tags">
        <el-tag
          v-for="tag in site.tags.slice(0, 5)"
          :key="tag"
          size="small"
          class="tag"
          :style="getTagCSSStyle(tag)"
          :data-selected="selectedTags.includes(tag)"
          @click.stop="handleTagClick(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-tag v-if="site.tags.length > 5" size="small" type="info" class="tag-more">
          +{{ site.tags.length - 5 }}
        </el-tag>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons-list">
        <el-tooltip :content="t('sitesGrid.actions.openInCurrentTab')" placement="top">
          <el-button
            @click.stop="openInCurrentTab"
            class="action-button-list action-button-list-primary"
            size="small"
            :icon="Link"
            type="default"
            plain
          >
            {{ t('sitesGrid.actions.open') }}
          </el-button>
        </el-tooltip>
        <el-tooltip :content="t('sitesGrid.actions.openInNewTab')" placement="top">
          <el-button
            @click.stop="openInNewTab"
            class="action-button-list action-button-list-success"
            size="small"
            :icon="TopRight"
            type="default"
            plain
          >
            {{ t('sitesGrid.actions.openNewTab') }}
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <div class="site-list-actions">
      <!-- Remove/Add button -->
      <el-tooltip
        v-if="shouldShowFavoriteButton"
        :content="showRemoveIcon ? 'Usuń z ulubionych' : 'Dodaj do ulubionych'"
        placement="top"
      >
        <el-button
          @click.stop="toggleFavorite"
          class="favorite-button-list"
          :class="favoriteButtonClass"
          size="small"
          :type="favoriteButtonType"
          :icon="showRemoveIcon ? Close : Plus"
          circle
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { Close, Link, Plus, Setting, TopRight } from '@element-plus/icons-vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useMyAISettings } from '../composables/useMyAISettings.js'
import { useTagColors } from '../composables/useTagColors.js'

const props = defineProps({
  site: {
    type: Object,
    required: true,
    validator: (site) => site && typeof site.url === 'string' && typeof site.name === 'string',
  },
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
  selectedTags: {
    type: Array,
    default: () => [],
  },
  showConfigIcon: {
    type: Boolean,
    default: false,
  },
  showRemoveIcon: {
    type: Boolean,
    default: false,
  },
  showDragHandle: {
    type: Boolean,
    default: false,
  },
  isSearchTab: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'click',
  'favorite-changed',
  'config-click',
  'tag-selected',
  'dragstart',
  'dragover',
  'drop',
])

const { t } = useI18n()
const { myAI, addToMyAI, removeFromMyAI, isMyAIItem } = useMyAISettings()

const displayUrl = computed(() => {
  return props.site.url.replace('https://', '').split('/')[0]
})

const isFavorite = computed(() => {
  return isMyAIItem(props.site.url)
})

// Determine when to show the favorite button
const shouldShowFavoriteButton = computed(() => {
  if (props.isSearchTab) {
    // Search AI tab: always show add button
    return true
  } else {
    // My AI tab: only show remove button in edit mode
    return props.showRemoveIcon
  }
})

// Determine button styling class
const favoriteButtonClass = computed(() => {
  if (props.isSearchTab) {
    return 'favorite-button-list-search'
  } else if (props.showRemoveIcon) {
    return 'favorite-button-list-remove'
  }
  return ''
})

// Determine button type for Element Plus styling
const favoriteButtonType = computed(() => {
  if (props.showRemoveIcon) {
    return 'danger'
  } else if (props.isSearchTab) {
    return 'default' // Start with neutral/gray type
  }
  return 'success'
})

const handleConfigClick = () => {
  emit('config-click', props.site)
}

const toggleFavorite = () => {
  let wasAdded = false

  if (props.isSearchTab) {
    // W Search AI zawsze dodaj (pozwól na duplikaty)
    addToMyAI(props.site)
    wasAdded = true
  } else {
    // W My AI trybie normalnym - dodaj/usuń
    if (props.showRemoveIcon) {
      // W trybie edycji My AI - usuń konkretny element po ID
      removeFromMyAI(props.site)
      wasAdded = false
    } else if (isFavorite.value) {
      removeFromMyAI(props.site.url)
      wasAdded = false
    } else {
      addToMyAI(props.site)
      wasAdded = true
    }
  }

  emit('favorite-changed', wasAdded)
}

const getTagCSSStyle = (tag) => {
  const { getTagStyle } = useTagColors(props.isDarkTheme)
  const isSelected = props.selectedTags.includes(tag)
  return getTagStyle(tag, isSelected)
}

// Tag click handler
const handleTagClick = (tag) => {
  emit('tag-selected', tag)
}

// Action button handlers
const openInCurrentTab = () => {
  // Ensure URL has proper protocol
  let url = props.site.url
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://' + url
  }

  try {
    // For Chrome extensions, we might need to use different methods
    if (typeof chrome !== 'undefined' && chrome.tabs) {
      // If we have access to Chrome tabs API, use it
      chrome.tabs.update({ url: url })
    } else {
      // Otherwise use standard navigation
      window.location.href = url
    }
  } catch (error) {
    console.error('Error opening URL in current tab:', error)
    try {
      // Fallback: try using window.location.assign
      window.location.assign(url)
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError)
      // Last resort: try window.open with _self target
      window.open(url, '_self')
    }
  }
}

const openInNewTab = () => {
  window.open(props.site.url, '_blank')
}
</script>

<style scoped>
.site-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.site-list-item:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.site-list-item.dark-theme:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.site-list-item.edit-mode {
  cursor: grab;
}

.site-list-item.edit-mode:active {
  cursor: grabbing;
}

.site-list-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex: 1;
  min-width: 0;
}

.site-list-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 200px;
  flex-shrink: 0;
}

.site-list-name {
  font-weight: 600;
}

.site-list-url {
  opacity: 0.8;
}

.site-list-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  flex: 1;
}

.action-buttons-list {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.action-button-list {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.2s ease;
  min-width: 80px;
  opacity: 0.7;
}

.action-button-list:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 1;
}

.site-list-item.dark-theme .action-button-list:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Primary action button hover */
.action-button-list-primary:hover {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
  color: white !important;
}

/* Success action button hover */
.action-button-list-success:hover {
  background-color: var(--el-color-success) !important;
  border-color: var(--el-color-success) !important;
  color: white !important;
}

.site-list-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  margin-left: 1rem;
}

.config-button-list,
.favorite-button-list {
  opacity: 0.7;
  transition: all 0.2s ease;
}

/* Search AI list view - Add to favorites button styling */
.favorite-button-list-search {
  background-color: #f5f5f5 !important;
  border-color: #bdbdbd !important;
  color: #424242 !important;
}

.favorite-button-list-search:hover {
  background-color: #43a047 !important;
  border-color: #43a047 !important;
  color: white !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(67, 160, 71, 0.3);
}

/* Dark theme for Search AI list button */
.site-list-item.dark-theme .favorite-button-list-search {
  background-color: #424242 !important;
  border-color: #616161 !important;
  color: #e0e0e0 !important;
}

.site-list-item.dark-theme .favorite-button-list-search:hover {
  background-color: #43a047 !important;
  border-color: #43a047 !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(67, 160, 71, 0.4);
}

/* My AI list view - Remove button styling (only visible in edit mode) */
.favorite-button-list-remove {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
  color: white !important;
}

.favorite-button-list-remove:hover {
  background-color: #d32f2f !important;
  border-color: #d32f2f !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.site-list-item:hover .config-button-list,
.site-list-item:hover .favorite-button-list {
  opacity: 1;
}

/* Tag styling consistent with SiteCard */
.tag {
  background-color: var(--tag-bg);
  color: var(--tag-text);
  border-color: var(--tag-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Hover state for unselected tags (gray) */
.tag:not([data-selected='true']):hover {
  background-color: #f5f5f5 !important;
  border-color: #bdbdbd !important;
  color: #424242 !important;
  box-shadow: 0 2px 4px rgba(189, 189, 189, 0.2);
}

/* Hover state for selected tags (darker shade) */
.tag[data-selected='true']:hover {
  opacity: 0.8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dark theme hover state for unselected tags */
.site-list-item.dark-theme .tag:not([data-selected='true']):hover {
  background-color: #424242 !important;
  border-color: #757575 !important;
  color: #e0e0e0 !important;
  box-shadow: 0 2px 4px rgba(117, 117, 117, 0.3);
}

/* Dark theme hover state for selected tags */
.site-list-item.dark-theme .tag[data-selected='true']:hover {
  opacity: 0.8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

/* Responsive design */
@media (max-width: 768px) {
  .site-list-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .site-list-info {
    min-width: auto;
    width: 100%;
  }

  .site-list-tags {
    width: 100%;
  }

  .action-buttons-list {
    width: 100%;
    justify-content: center;
  }

  .action-button-list {
    flex: 1;
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .site-list-item {
    padding: 0.75rem;
  }

  .site-list-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--el-bg-color);
    border-radius: 6px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .site-list-content {
    padding-right: 4rem;
  }

  .action-buttons-list {
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
  }

  .action-button-list {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    min-width: 80px;
    min-height: 36px;
  }
}
</style>
