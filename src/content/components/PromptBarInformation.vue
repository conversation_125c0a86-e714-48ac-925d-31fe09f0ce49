<script setup>
import { Connection, Link } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

const { t } = useI18n()

const props = defineProps(['title', 'description', 'links', 'tags'])

const emit = defineEmits(['openInformationDialog'])
const appState = useAppStateStore()

const isDisabled = () => {
  return (!props.description && !props.links) ||
    (props.description === '' && props.links?.length === 0)
    ? true
    : false
}

function handleOpenInformationDialog(linkItem) {
  emit('openInformationDialog', linkItem)
}
</script>
<template>
  <el-popover
    placement="bottom"
    width="clamp(300px,50dvw,960px)"
    trigger="hover"
    :teleported="false"
    :disabled="isDisabled()"
    v-model:visible="appState.promptBarPopoverVisible"
  >
    <template #reference>
      <el-button
        :disabled="isDisabled()"
        :class="{ 'opacity-50': isDisabled() }"
        class="!p-1 !bg-blue-400/30 hover:!bg-blue-400/10"
        link
        data-testid="prompt-bar-info-button"
      >
        <!-- <span class="hidden xl:block mr-2">Information</span> -->
        <el-icon class="" size="23"><InfoFilled /></el-icon>
      </el-button>
    </template>
    <template #default>
      <div class="p-2 flex flex-col gap-2 max-h-[80vh] overflow-auto">
        <h3 class="text-lg mb-1 font-bold">{{ props.title }}</h3>
        <div class="mb-2 text-base opacity-75 whitespace-pre-wrap">
          {{ props.description }}
        </div>
        <div v-if="props.tags && props.tags.length != 0" class="last-of-type:mb-2">
          <div class="flex gap-1 flex-wrap">
            <div class="py-1 mr-1 text-xs">{{ t('bar.tags') }}:</div>
            <template v-for="tag in props.tags" :key="tag">
              <span>
                <el-button type="primary" text bg size="small">
                  {{ tag }}
                </el-button>
              </span>
            </template>
          </div>
        </div>
        <div v-if="props.links && props.links.length != 0">
          <el-divider class="my-2"></el-divider>
          <div class="flex gap-2 flex-wrap">
            <div class="py-2.5 mr-1">{{ t('bar.links') }}:</div>
            <template v-for="linkItem in props.links" :key="linkItem.id">
              <a v-if="linkItem.linkType === 'url'" :href="linkItem.link" target="_blank">
                <el-button size="large">
                  <el-icon class="mr-2"><Link /></el-icon> {{ linkItem.label }}
                </el-button>
              </a>
              <span v-else>
                <el-button size="large" @click="handleOpenInformationDialog(linkItem)">
                  <el-icon v-if="linkItem.linkType === 'modal'" class="mr-2"
                    ><Connection
                  /></el-icon>
                  <el-icon v-else class="mr-2"><VideoPlay /></el-icon>
                  {{ linkItem.label }}
                </el-button>
              </span>
            </template>
          </div>
        </div>
      </div>
    </template>
  </el-popover>
</template>
<style scoped></style>
