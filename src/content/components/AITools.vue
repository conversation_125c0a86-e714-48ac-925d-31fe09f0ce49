<script setup>
import {
  Coin,
  Expand,
  Finished,
  Loading,
  MagicStick,
  Plus,
  QuestionFilled,
  Reading,
} from '@element-plus/icons-vue'
import { getAuth } from 'firebase/auth'
import { getFunctions, httpsCallable } from 'firebase/functions'
import { storeToRefs } from 'pinia'
import { computed, nextTick, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useExternalTextarea } from '@/content/composables/useExternalTextarea'
import { setUserCredits } from '@/content/firebase'
import {
  getFromLocalStorage,
  getFromPluginStorageLocal,
  setToPluginStorageLocal,
} from '@/content/localStorage'
import { chatSite } from '@/content/sites_index'
import { aiStorageName, clearAiStorage, TABS } from '@/content/utils'
import { useAppStateStore } from '@/stores/appState'

import IconBookmark from './icons/IconBookmark.vue'
import IconLogo from './icons/IconLogo.vue'
import Popup from './Popup.vue'
import Tooltip from './Tooltip.vue'

// Define props with proper validation
const props = defineProps({
  showLogo: {
    type: Boolean,
    default: true,
    validator: (value) => typeof value === 'boolean',
  },
})

const auth = getAuth()

const appState = useAppStateStore()
const {
  selectedBarId,
  creditsOwnedByUser,
  selectRef,
  selectedBar,
  isPromptBookmark,
  outsideTextareaPrompt,
  nameForGeneratedPrompt,
  isEditPromptBarDialogOpen,
  textToCreatePrompt,
  selectedLangInPopularDropdown,
  selectedLangInLibraryDropdown,
  isLoggedIn,
  hasSubscription,
  activeBarIdsByTab,
  hasActiveMyPromptsBar,
  creditsMax,
  isUserLoaded,
  isLoggingIn,
  isSigningUp,
  isAddPromptBarDialogOpen,
  automaticNewBar,
  isGeneratingPromptName,
} = storeToRefs(appState)

const { locale, t } = useI18n()

const popoverPlacement = chatSite.name === 'Canva' ? 'bottom-start' : 'top-start'

// Function to detect if extension is running in side panel mode
const isSidePanelMode = () => {
  return (
    window.location.href.includes('side_panel.html') ||
    window.location.pathname.includes('side_panel.html')
  )
}

// Function to detect if we're in development mode (local file)
const isDevelopmentMode = () => {
  return (
    window.location.protocol === 'file:' ||
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1'
  )
}

// Function to get current tab info when in side panel mode
const getCurrentTabInfo = async () => {
  if (!isSidePanelMode()) {
    console.log('Not in side panel mode, skipping tab info retrieval')
    return null
  }

  try {
    console.log('Requesting current tab info from background script...')
    console.log('Development mode:', isDevelopmentMode())
    console.log('Current location:', window.location.href)

    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage(
        {
          action: 'getCurrentTabInfo',
          isDevelopmentMode: isDevelopmentMode(),
          currentLocation: window.location.href,
        },
        (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error getting current tab info:', chrome.runtime.lastError)
            resolve(null)
          } else {
            console.log('Received tab info response:', response)
            resolve(response)
          }
        },
      )
    })

    if (response?.success && response.data) {
      console.log('Successfully retrieved tab info:', {
        url: response.data.url,
        title: response.data.title,
        id: response.data.id,
      })
      return response.data
    } else {
      console.warn('Failed to get tab info:', response?.error || 'Unknown error')
      return null
    }
  } catch (error) {
    console.error('Error requesting current tab info:', error)
    return null
  }
}

// Watch for dialog close to abort AI generation
watch(isEditPromptBarDialogOpen, (isOpen) => {
  if (!isOpen && abortAiGeneratingController) {
    // Dialog was closed, abort any ongoing AI generation
    abortAiGeneratingController.abort()
    abortAiGeneratingController = null
    appState.setIsGeneratingPromptName(false)
  }
})

// **** AI functions ****
const defaultOptionTabIndex = 0

const aiTopic = {
  Better: 'betterPrompt',
  BetterAdvanced: 'betterPromptAdvanced',
  Tip: 'tipPrompt',
  Library: 'library',
  Test: 'test',
  Rtf: 'RTF',
  Tag: 'TAG',
  Bab: 'BAB',
  Care: 'CARE',
  Rise: 'RISE',
  Coast: 'COAST',
  Take: 'TAKE',
  Pain: 'PAIN',
  Create: 'CREATE',
}
const aiSearchEngine = {
  Google: 'google',
  Anthropic: 'anthropic',
  GPT: 'gpt',
}
// ai Buttons - if button have cost, it's main button (center section), else it's minor button (right section)
// NOTE: This defines aiButtons locally, overriding the possibility of passing it as a prop.
const aiButtons = {
  'Better Prompt': {
    name: computed(() => t('aiTools.betterPrompt')),
    optionTabs: computed(() => [
      {
        name: t('aiTools.default'),
        topic: aiTopic.Better,
        engine: aiSearchEngine.GPT,
        compare: true,
      },
      {
        name: t('aiTools.advanced'),
        topic: aiTopic.BetterAdvanced,
        engine: aiSearchEngine.GPT,
        secure: true,
        compare: true,
      },
      {
        name: t('aiTools.tips'),
        topic: aiTopic.Tip,
        engine: aiSearchEngine.GPT,
      },
    ]),
    tooltip: computed(() => t('aiTools.improvePrompt')),
    creditsCost: 1,
    icon: MagicStick,
    size: 'small',
  },
  Frameworks: {
    name: computed(() => t('aiTools.frameworks')),
    optionTabs: computed(() => [
      {
        name: 'RTF ',
        topic: aiTopic.Rtf,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.rtfTooltip')),
        compare: true,
      },
      {
        name: 'TAG',
        topic: aiTopic.Tag,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.tagTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'BAB',
        topic: aiTopic.Bab,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.babTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'CARE',
        topic: aiTopic.Care,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.careTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'RISE',
        topic: aiTopic.Rise,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.riseTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'COAST',
        topic: aiTopic.Coast,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.coastTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'TAKE',
        topic: aiTopic.Take,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.takeTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'PAIN',
        topic: aiTopic.Pain,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.painTooltip')),
        secure: true,
        compare: true,
      },
      {
        name: 'CREATE',
        topic: aiTopic.Create,
        engine: aiSearchEngine.GPT,
        tooltip: computed(() => t('aiTools.createTooltip')),
        secure: true,
        compare: true,
      },
    ]),
    tooltip: computed(() => t('aiTools.applyFrameworks')),
    creditsCost: 1,
    icon: Finished,
    size: 'small',
  },
  Finder: {
    name: computed(() => t('aiTools.finder')),
    optionTabs: computed(() => [
      {
        name: t('aiTools.defaultGoogle'),
        topic: aiTopic.Library,
        engine: aiSearchEngine.GPT,
      },
    ]),
    tooltip: computed(() => t('aiTools.findPrompts')),
    creditsCost: 1,
    icon: Reading,
    size: 'small',
  },
}

const { getOutsideSelectorFromChatSite } = useExternalTextarea()

const activeAiButton = ref('')
const activeAiOptionTabIndex = ref(defaultOptionTabIndex)

const aiTopicCurrent = ref('')
const aiSearchEngineCurrent = ref('')

const isGeneratingAiResponse = ref(false)
const isAiOpen = ref(false)
const AIFunctionResult = ref('')
const hasAiResultError = ref(false)

const doComparison = ref(false)
const isGeneratingComparison = ref(false)
const comparisonResult = ref(null)
const comparisonResultPercent = ref(null)

const aiTooltip = computed(() => {
  return isLoggedIn.value ? t('prompt.enterText') : t('messages.loginRequired')
})
const noCreditsTooltip = computed(() => {
  return hasSubscription.value ? t('tooltip.noCredits') : t('subscription.buySubscription')
})

const isCreditsLocked = computed(() => {
  return creditsOwnedByUser.value <= 0
})

const freeRerolls = ref(0)
const freeRerollsMax = ref(0)

watch(
  isUserLoaded,
  () => {
    if (isUserLoaded.value) {
      freeRerollsMax.value = 5
    }
  },
  { immediate: true },
)

const isRerollsLocked = computed(() => {
  return freeRerolls.value >= freeRerollsMax.value
})
let abortAiGeneratingController = null

const isLibraryButton = ref(false)

const creditsUsedAnimation = ref(false)

const callAIFunction = async (aiType, functionName, regenerateQuestion = null) => {
  if (abortAiGeneratingController) {
    abortAiGeneratingController.abort()
  }
  abortAiGeneratingController = new AbortController()

  isGeneratingAiResponse.value = true
  hasAiResultError.value = false
  AIFunctionResult.value = ''
  const question = regenerateQuestion ?? outsideTextareaPrompt.value

  const buttonConfig = Object.values(aiButtons).find((btn) =>
    btn.optionTabs.value.some((tab) => tab.topic === functionName),
  )
  const costsCredits = buttonConfig?.creditsCost >= 0

  if (costsCredits && isCreditsLocked.value) {
    console.error('No credits available to make AI call.')
    hasAiResultError.value = true
    AIFunctionResult.value = t('aiTools.notEnoughCredits')
    isGeneratingAiResponse.value = false
    abortAiGeneratingController = null
    return
  }

  try {
    const functions = getFunctions()
    let response

    let callableFunction
    if (aiType === aiSearchEngine.Google) {
      callableFunction = httpsCallable(functions, 'askAIGoogle')
    } else if (aiType === aiSearchEngine.Anthropic) {
      callableFunction = httpsCallable(functions, 'askAnthropic')
    } else if (aiType === aiSearchEngine.GPT) {
      callableFunction = httpsCallable(functions, 'askAIGPT')
    } else {
      throw new Error('Unsupported AI type')
    }

    if (navigator.webdriver && functionName !== aiTopic.Library) {
      response = {
        data: {
          result: `functionName: ${functionName}\nlocale: ${locale.value}\nprompt: ${question}`,
        },
      }
    } else {
      response = await callableFunction(
        { prompt: question, functionName: functionName, locale: locale.value },
        { signal: abortAiGeneratingController.signal },
      )
    }

    if (abortAiGeneratingController.signal.aborted) {
      resetAiState()
      return
    }

    if (response.data && response.data.result) {
      AIFunctionResult.value = response.data.result

      if (costsCredits) {
        const user = auth.currentUser
        if (user) {
          try {
            const currentCredits = creditsOwnedByUser.value
            const newCredits = Math.max(0, currentCredits - buttonConfig.creditsCost)

            appState.setCreditsOwnedByUser(newCredits)

            try {
              await setUserCredits(user.uid, newCredits)
            } catch (error) {
              console.error('Error updating user credits in database:', error)
            }

            creditsUsedAnimation.value = true
            setTimeout(() => {
              creditsUsedAnimation.value = false
            }, 1000)
          } catch (error) {
            console.error('Error updating user credits:', error)
          }
        }
      }
    } else if (response?.data?.result === '') {
      AIFunctionResult.value = t('aiTools.noResults')
      hasAiResultError.value = true
    } else {
      AIFunctionResult.value = t('aiTools.invalidResponse')
      hasAiResultError.value = true
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      resetAiState()
    } else {
      console.error(`Error calling "${aiType}" "${functionName}":`, error)
      AIFunctionResult.value = t('aiTools.errorOccurred')
      hasAiResultError.value = true
    }
  } finally {
    isGeneratingAiResponse.value = false
    abortAiGeneratingController = null

    await saveAiCurrentsToPluginStorage(aiTopicCurrent.value, question)

    if (!hasAiResultError.value) {
      freeRerolls.value += 1
      isAiOpen.value = true
    }

    if (doComparison.value && !hasAiResultError.value) {
      setComparisonResult()
    }

    adjustResponseBox()
  }
}

const callAiCompare = async (generatedPrompt, originalPrompt) => {
  isGeneratingComparison.value = true
  if (abortAiGeneratingController) {
    abortAiGeneratingController.abort()
  }
  abortAiGeneratingController = new AbortController()

  const functions = getFunctions()
  let result = null
  try {
    const askAIGPTAgain = httpsCallable(functions, 'askAIGPT')

    const response = await askAIGPTAgain(
      {
        prompt: generatedPrompt,
        promptSource: originalPrompt,
        functionName: 'betterPromptInfo',
      },
      { signal: abortAiGeneratingController.signal },
    )
    if (abortAiGeneratingController.signal.aborted) {
      return
    }
    result = response.data.result
  } catch (error) {
    if (error.name === 'AbortError') {
      // Operation was aborted
    } else {
      console.error(`Error calling "comparePrompt": ${error}`)
      comparisonResult.value = 'Error during comparison.'
    }
  } finally {
    isGeneratingComparison.value = false
    abortAiGeneratingController = null

    if (result) {
      let clearResult = result.replace(/```/g, '')
      if (clearResult.startsWith('html')) {
        clearResult = clearResult.slice(4).trim()
      }

      if (clearResult && clearResult.trim() !== '') {
        comparisonResult.value = clearResult
        let clearResultPercent = clearResult
          .replace(/<[^>]*>/g, '')
          .replace('.', '')
          .trim()
          .slice(-20)

        if (clearResultPercent.includes('%')) {
          comparisonResultPercent.value = clearResultPercent.match(/.{0,3}%/)?.[0] || 'unknown'
        } else {
          comparisonResultPercent.value = 'unknown'
        }

        saveComparisonResultForCurrentQuestion(
          aiTopicCurrent.value,
          comparisonResult.value,
          comparisonResultPercent.value,
        )
      }
    } else if (!comparisonResult.value) {
      comparisonResult.value = comparisonResult.value || t('aiTools.comparisonFailed')
    }
  }
}

async function saveAiCurrentsToPluginStorage(aiTopic, questionToAi = outsideTextareaPrompt.value) {
  if (!aiTopic || !AIFunctionResult.value) return

  try {
    const existingStorage = (await getFromPluginStorageLocal(aiStorageName).catch(() => ({}))) || {}

    existingStorage[aiTopic] = existingStorage[aiTopic] || {}
    existingStorage[aiTopic].lastResult = existingStorage[aiTopic].lastResult || {}
    existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value] =
      existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value] || {}

    const saveEngineData = {
      result: AIFunctionResult.value,
      comparisonResult:
        existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value].comparisonResult || '',
      comparisonResultPercent:
        existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value].comparisonResultPercent ||
        '',
    }

    const saveTopicData = {
      lastResult: {
        ...existingStorage[aiTopic].lastResult,
        [aiSearchEngineCurrent.value]: saveEngineData,
        question: questionToAi,
      },
      lastTab: activeAiOptionTabIndex.value,
    }

    const updatedStorage = {
      ...existingStorage,
      [aiTopic]: saveTopicData,
    }

    await setToPluginStorageLocal(aiStorageName, updatedStorage)
  } catch (error) {
    console.error('Error while saving AI result to storage:', error)
  }
}

async function saveComparisonResultForCurrentQuestion(aiTopic, compResult, compResultPercent) {
  if (!aiTopic || !aiSearchEngineCurrent.value) return

  try {
    const existingStorage = (await getFromPluginStorageLocal(aiStorageName).catch(() => ({}))) || {}

    existingStorage[aiTopic] = existingStorage[aiTopic] || {}
    existingStorage[aiTopic].lastResult = existingStorage[aiTopic].lastResult || {}
    existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value] =
      existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value] || {}

    existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value].comparisonResult = compResult
    existingStorage[aiTopic].lastResult[aiSearchEngineCurrent.value].comparisonResultPercent =
      compResultPercent

    await setToPluginStorageLocal(aiStorageName, existingStorage)
  } catch (error) {
    console.error('Error while saving comparison result to storage:', error)
  }
}

async function clearLastResultFromStorage(aiTopic) {
  if (!aiTopic) return
  try {
    const existingStorage = await getFromPluginStorageLocal(aiStorageName).catch(() => ({}))
    if (existingStorage && existingStorage[aiTopic]) {
      existingStorage[aiTopic].lastResult = {}

      await setToPluginStorageLocal(aiStorageName, existingStorage)
    }
  } catch (error) {
    console.error(`Error clearing lastResult for ${aiTopic}:`, error)
  }
}

async function setComparisonResult() {
  try {
    const existingStorage = (await getFromPluginStorageLocal(aiStorageName)) || {}
    const lastResultData =
      existingStorage[aiTopicCurrent.value]?.lastResult?.[aiSearchEngineCurrent.value]

    if (lastResultData?.comparisonResult && lastResultData?.comparisonResultPercent) {
      comparisonResult.value = lastResultData.comparisonResult
      comparisonResultPercent.value = lastResultData.comparisonResultPercent
    } else {
      const originalQuestion =
        existingStorage[aiTopicCurrent.value]?.lastResult?.question || outsideTextareaPrompt.value
      if (AIFunctionResult.value && originalQuestion) {
        await callAiCompare(AIFunctionResult.value, originalQuestion)
      } else {
        console.warn('Cannot run comparison: Missing generated result or original question.')
        comparisonResult.value = t('aiTools.comparisonUnavailable')
        comparisonResultPercent.value = ''
      }
    }
  } catch (error) {
    console.error('Error setting comparison result:', error)
    comparisonResult.value = t('aiTools.errorLoadingComparison')
    comparisonResultPercent.value = ''
  }
}

async function resetAiState() {
  AIFunctionResult.value = ''
  aiSearchEngineCurrent.value = ''
  isAiOpen.value = false
  freeRerolls.value = 0
  activeAiButton.value = ''
  activeAiOptionTabIndex.value = defaultOptionTabIndex
  hasAiResultError.value = false
  isGeneratingComparison.value = false
  comparisonResult.value = ''
  comparisonResultPercent.value = ''
  isLibraryButton.value = false

  if (aiTopicCurrent.value) {
    await clearLastResultFromStorage(aiTopicCurrent.value)
  }
  aiTopicCurrent.value = ''
}

async function insertAiResult() {
  let promptTextarea = getOutsideSelectorFromChatSite('textarea')
  if (!promptTextarea) {
    console.error('Prompt textarea not found')
    return
  }

  promptTextarea.focus()

  // console.log('promptTextarea', promptTextarea)
  // console.log('promptTextarea details:', {
  //   tagName: promptTextarea.tagName,
  //   id: promptTextarea.id,
  //   className: promptTextarea.className,
  //   contentEditable: promptTextarea.contentEditable,
  //   isContentEditable: promptTextarea.isContentEditable,
  //   dataset: promptTextarea.dataset,
  //   attributes: Array.from(promptTextarea.attributes).map((attr) => ({
  //     name: attr.name,
  //     value: attr.value,
  //   })),
  //   placeholder: promptTextarea.placeholder || promptTextarea.getAttribute('placeholder'),
  //   type: promptTextarea.type,
  //   value: promptTextarea.value,
  //   textContent: promptTextarea.textContent,
  //   innerText: promptTextarea.innerText,
  //   innerHTML: promptTextarea.innerHTML,
  // })

  // Check if this is an internal AI component (like TextFieldComponent)
  if (promptTextarea.classList && promptTextarea.classList.contains('el-textarea__inner')) {
    // This is an Element Plus textarea (TextFieldComponent)
    promptTextarea.value = AIFunctionResult.value

    // Trigger input event to ensure Vue reactivity
    const inputEvent = new Event('input', { bubbles: true })
    promptTextarea.dispatchEvent(inputEvent)

    // Also trigger change event
    const changeEvent = new Event('change', { bubbles: true })
    promptTextarea.dispatchEvent(changeEvent)
  } else if (chatSite.name === 'Vespa') {
    promptTextarea.value = AIFunctionResult.value
  } else if (chatSite.name === 'Bard') {
    promptTextarea.textContent = AIFunctionResult.value
  } else if (chatSite.name === 'Claude') {
    promptTextarea.textContent = AIFunctionResult.value
  } else if (promptTextarea.tagName === 'TEXTAREA' || promptTextarea.tagName === 'INPUT') {
    promptTextarea.value = AIFunctionResult.value
  } else if (promptTextarea.tagName === 'DIV') {
    promptTextarea.innerText = AIFunctionResult.value
  } else if (promptTextarea.isContentEditable) {
    promptTextarea.innerText = AIFunctionResult.value
  } else {
    promptTextarea.textContent = AIFunctionResult.value
  }

  promptTextarea.dispatchEvent(new Event('input', { bubbles: true }))
  promptTextarea.dispatchEvent(new Event('change', { bubbles: true }))

  await resetAiState()
  clearAiStorage()
}

async function closeAiResult() {
  if (isGeneratingAiResponse.value && abortAiGeneratingController) {
    abortAiGeneratingController.abort()
  }
  isGeneratingAiResponse.value = false
  await resetAiState()
}

async function cancelAiResult() {
  if (abortAiGeneratingController) {
    abortAiGeneratingController.abort()
  }
  await resetAiState()
}

function adjustResponseBox() {
  nextTick(() => {
    const buttonBox = document.querySelector('#_chatButton .bottom-buttons')
    const responseBox = document.querySelector('#_chatButton .ai-response')
    if (buttonBox && responseBox) {
      const responseBoxPosY = buttonBox.getBoundingClientRect().y
      const screenH = window.innerHeight
      const gap = 10
      const height = buttonBox.offsetHeight
      if (responseBoxPosY + height + responseBox.offsetHeight + gap < screenH) {
        responseBox.style.top = `${gap + height}px`
        responseBox.style.bottom = 'auto'
      } else {
        responseBox.style.bottom = `${gap + height}px`
        responseBox.style.top = 'auto'
      }
    }
  })
}

const activateAIButton = (buttonName, button) => {
  if (!isLoggedIn.value) {
    appState.setLoginPopoverVisible(true)
    return
  }

  activeAiButton.value = buttonName
  const currentTabIndex =
    activeAiOptionTabIndex.value >= button.optionTabs.value.length
      ? defaultOptionTabIndex
      : activeAiOptionTabIndex.value
  activeAiOptionTabIndex.value = currentTabIndex

  const activeTab = button.optionTabs.value[currentTabIndex]
  doComparison.value = activeTab.compare ?? false

  setAIFunction(activeTab.topic, activeTab.engine)
}

async function setAIFunction(topicKey, engine = null) {
  if (!isLoggedIn.value || isCreditsLocked.value) {
    console.warn('Login or credits required.')
    activeAiButton.value = ''
    return
  }

  aiTopicCurrent.value = topicKey
  isLibraryButton.value = topicKey === aiTopic.Library
  aiSearchEngineCurrent.value = engine || aiSearchEngine.Google

  doComparison.value = !isLibraryButton.value && doComparison.value
  comparisonResult.value = ''
  comparisonResultPercent.value = ''
  isGeneratingComparison.value = false

  let storage
  let lastResultData = null
  let storedQuestion = ''

  try {
    storage = (await getFromPluginStorageLocal(aiStorageName).catch(() => ({}))) || {}
    lastResultData = storage[topicKey]?.lastResult?.[aiSearchEngineCurrent.value]
    storedQuestion = storage[topicKey]?.lastResult?.question

    if (lastResultData?.result) {
      AIFunctionResult.value = lastResultData.result
      isAiOpen.value = true
      freeRerolls.value = 0

      if (lastResultData.comparisonResult && lastResultData.comparisonResultPercent) {
        comparisonResult.value = lastResultData.comparisonResult
        comparisonResultPercent.value = lastResultData.comparisonResultPercent
        doComparison.value = true
      } else if (
        aiButtons[activeAiButton.value]?.optionTabs.value[activeAiOptionTabIndex.value]?.compare
      ) {
        doComparison.value = true
        setComparisonResult()
      }
    } else {
      isAiOpen.value = false
      callAIFunction(
        aiSearchEngineCurrent.value,
        aiTopicCurrent.value,
        storedQuestion || outsideTextareaPrompt.value,
      )
    }
  } catch (error) {
    console.error('Error setting AI function or loading from storage:', error)
    hasAiResultError.value = true
    AIFunctionResult.value = 'Error loading AI function state.'
    isAiOpen.value = true
  } finally {
    adjustResponseBox()
  }
}

const callAiRegenerate = async () => {
  if (!aiTopicCurrent.value || !aiSearchEngineCurrent.value || isRerollsLocked.value) {
    console.warn('Cannot regenerate: Missing topic/engine or rerolls locked.')
    return
  }

  let regenerateQuestion = ''
  try {
    const storage = await getFromPluginStorageLocal(aiStorageName).catch(() => ({}))
    regenerateQuestion =
      storage?.[aiTopicCurrent.value]?.lastResult?.question || outsideTextareaPrompt.value
  } catch (error) {
    console.error('Error fetching question for regeneration:', error)
    regenerateQuestion = outsideTextareaPrompt.value
  } finally {
    AIFunctionResult.value = ''
    comparisonResult.value = ''
    comparisonResultPercent.value = ''
    isAiOpen.value = false

    const activeTab =
      aiButtons[activeAiButton.value]?.optionTabs.value[activeAiOptionTabIndex.value]
    doComparison.value = activeTab?.compare ?? false

    callAIFunction(aiSearchEngineCurrent.value, aiTopicCurrent.value, regenerateQuestion)
  }
}

const addPromptFromAiGeneratorToMyPrompts = () => {
  if (activeTabName.value !== TABS.MyPrompts) activeTabName.value = TABS.MyPrompts

  nextTick(() => {
    let isBarSelectedAndExists = selectedBar.value && selectedBarId.value
    if (!isBarSelectedAndExists) {
      automaticNewBar.value = true
      isAddPromptBarDialogOpen.value = true
      return
    }

    // Set prompt content immediately
    textToCreatePrompt.value = AIFunctionResult.value

    // Set temporary name immediately
    nameForGeneratedPrompt.value = t('messages.newPrompt')

    // Open dialog immediately
    appState.openEditPromptBarDialog()

    // Start AI name generation in background
    generatePromptNameInBackground()
  })
}

const generatePromptNameInBackground = () => {
  if (!abortAiGeneratingController) {
    abortAiGeneratingController = new AbortController()
  }

  // Set loading state
  appState.setIsGeneratingPromptName(true)

  callAIFunctionForTopic(textToCreatePrompt.value)
    .then((generatedTitle) => {
      // Only update if we got a valid title and it's different from the temporary name
      if (generatedTitle && generatedTitle.trim() && generatedTitle !== t('messages.newPrompt')) {
        nameForGeneratedPrompt.value = generatedTitle
      }
    })
    .catch((error) => {
      console.error('Error generating title:', error)

      // Handle different types of errors
      if (error.name === 'AbortError') {
        console.log('AI name generation was aborted')
      } else if (error.message?.includes('credits')) {
        console.warn('Insufficient credits for AI name generation')
      } else {
        console.warn('AI name generation failed, keeping temporary name')
      }

      // Keep the temporary name if AI fails - no need to change nameForGeneratedPrompt
      // The temporary name 'New Prompt' will remain in place
    })
    .finally(() => {
      // Clear loading state
      appState.setIsGeneratingPromptName(false)
    })
}

const callAIFunctionForTopic = async (aiResult) => {
  if (!aiResult) throw new Error('Cannot generate topic name from empty result.')

  if (abortAiGeneratingController && abortAiGeneratingController.signal.aborted) {
    throw new Error('Operation aborted before generating topic name.')
  }
  if (!abortAiGeneratingController) {
    abortAiGeneratingController = new AbortController()
  }

  try {
    const functions = getFunctions()
    const askAIGPT = httpsCallable(functions, 'askAIGPT')
    const response = await askAIGPT(
      { prompt: aiResult, functionName: 'generateTopicName' },
      { signal: abortAiGeneratingController.signal },
    )

    if (abortAiGeneratingController.signal.aborted) {
      throw new Error('Operation aborted while generating topic name.')
    }

    if (response.data && response.data.result) {
      return response.data.result
    } else {
      throw new Error('No result from AI function for topic name')
    }
  } catch (error) {
    console.error('Error calling askAIGPT for topic name:', error)
    if (error.name === 'AbortError') {
      throw error // Re-throw abort errors to be handled properly
    }
    throw error // Re-throw other errors for proper handling
  }
}

let isShareButtonObserved = false

const hasActiveBar = computed(() => hasActiveMyPromptsBar.value)

const hasOutsideTextareaPrompt = computed(() => outsideTextareaPrompt.value !== '')

const addOpenEditPromptBarDialog = () => {
  if (!isLoggedIn.value) {
    appState.setLoginPopoverVisible(true)
    return
  }

  if (activeTabName.value !== TABS.MyPrompts) activeTabName.value = TABS.MyPrompts
  nextTick(() => {
    let length = 0
    function findPrompts(tree) {
      for (let node of tree) {
        if (node.children) {
          findPrompts(node.children)
        } else {
          length++
        }
      }
    }
    findPrompts(selectedBar.value.promptMenu)

    if (isPromptBookmark.value) {
      textToCreatePrompt.value = ''
      nameForGeneratedPrompt.value = document.title
      isEditPromptBarDialogOpen.value = true
      return
    }

    // Set prompt content immediately
    textToCreatePrompt.value = outsideTextareaPrompt.value

    // Set temporary name immediately
    nameForGeneratedPrompt.value = t('messages.newPrompt')

    // Open dialog immediately
    isEditPromptBarDialogOpen.value = true

    // Start AI name generation in background
    generatePromptNameInBackground()
  })
}

function observeShareButton() {
  if (typeof document === 'undefined') return

  const observer = new MutationObserver((mutations) => {
    if (isShareButtonObserved) {
      observer.disconnect()
      return
    }
    for (let mutation of mutations) {
      if (mutation.type === 'childList') {
        const shareButton = document.querySelector('button[data-testid="share-chat-button"]')
        if (shareButton) {
          isShareButtonObserved = true
          observer.disconnect()
          shareButton.addEventListener('click', () => {
            setTimeout(() => {
              waitForDialogAndAddButton()
            }, 500)
          })
          break
        }
      }
    }
  })
  observer.observe(document.body, { childList: true, subtree: true })
}

function waitForDialogAndAddButton() {
  if (typeof document === 'undefined') return
  const observer = new MutationObserver((mutationsList, observerInstance) => {
    const dialogElement = document.querySelector('[role="dialog"][id*="radix-"]')
    if (dialogElement) {
      observerInstance.disconnect()
      waitForInputLink(dialogElement)
    }
  })
  observer.observe(document.body, { childList: true, subtree: true })
}

function waitForInputLink(dialogElement) {
  if (typeof document === 'undefined') return
  let inputLink = dialogElement.querySelector('input[readonly]')
  let attempts = 0
  const maxAttempts = 20

  const checkInput = () => {
    inputLink = dialogElement.querySelector('input[readonly]')
    if (inputLink) {
      if (inputLink.value && !inputLink.value.includes('share/...')) {
        addSaveTheLinkButtonToChatGPT(dialogElement)
      } else {
        const valueObserver = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
              const newValue = inputLink.value
              if (newValue && !newValue.includes('share/...')) {
                valueObserver.disconnect()
                addSaveTheLinkButtonToChatGPT(dialogElement)
              }
            }
          })
        })
        valueObserver.observe(inputLink, { attributes: true, attributeFilter: ['value'] })
        setTimeout(() => valueObserver.disconnect(), 5000)
      }
    } else if (attempts < maxAttempts) {
      attempts++
      setTimeout(checkInput, 100)
    } else {
      console.warn('Could not find input link in share dialog after multiple attempts.')
    }
  }
  checkInput()
}

function addSaveTheLinkButtonToChatGPT(dialogElement) {
  if (typeof document === 'undefined') return
  let inputLink = dialogElement.querySelector('input[readonly]')
  if (!inputLink) return

  let addToElement = inputLink.parentElement?.parentElement
  let closeButton = dialogElement.querySelector('button[data-testid="close-button"]')
  if (!addToElement || !closeButton) {
    console.error("Could not find elements to attach 'Save The Link' button.")
    return
  }

  let saveButton = dialogElement.querySelector('.aip-save-the-link-button')
  if (!saveButton) {
    const originalButton = document.querySelector('.chat-prompt-button.add-bookmark')
    if (!originalButton) {
      console.error("Could not find the template 'add-bookmark' button in AI Tools UI.")
      return
    }

    let saveButtonDiv = document.createElement('div')
    saveButtonDiv.className =
      'aip-save-the-link flex w-full items-center justify-center gap-1.5 p-2 mt-2 border-t border-gray-300 dark:border-gray-700'
    saveButtonDiv.innerHTML = `<span class="text-sm text-gray-600 dark:text-gray-400">Add to selected bar:</span>`

    saveButton = originalButton.cloneNode(true)
    saveButton.classList.remove('el-button--small')
    saveButton.classList.add('aip-save-the-link-button')
    saveButton.style.width = 'auto'
    saveButton.disabled = !selectedBarId.value
    const textSpan = document.createElement('span')
    textSpan.className = 'ml-2'
    textSpan.textContent = 'Save as bookmark'
    saveButton.appendChild(textSpan)

    saveButton.addEventListener('click', (event) => {
      event.stopPropagation()
      if (inputLink && inputLink.value) {
        saveBookmarkToBar(inputLink.value)
        closeButton.click()
      } else {
        console.error('Link input not found or empty when trying to save.')
      }
    })

    saveButtonDiv.appendChild(saveButton)
    addToElement.insertAdjacentElement('afterend', saveButtonDiv)
  } else {
    saveButton.disabled = !selectedBarId.value
  }
}

const bookmarkLink = ref('')

function saveBookmarkToBar(shareLink) {
  if (!isLoggedIn.value) {
    appState.setLoginPopoverVisible(true)
    return
  }

  if (!selectedBarId.value) {
    console.warn('No prompt bar selected to save the bookmark.')
    return
  }
  bookmarkLink.value = shareLink

  appState.openEditPromptBarDialog(true)
}

// Computed property for bookmark tooltip content
const bookmarkTooltipContent = computed(() => {
  if (!isLoggedIn.value) {
    return t('messages.loginRequired')
  }

  if (!hasActiveBar.value) {
    return t('messages.selectBarInMyPromptsToActivate')
  }

  // Different message for side panel mode
  if (isSidePanelMode()) {
    return t('messages.bookmarkCurrentPageToSelectedBar')
  }

  return t('messages.addBookmarkToSelectedPromptBar')
})

// Handle bookmark button click with side panel mode detection
const handleBookmarkClick = async () => {
  if (!checkLoginAndShowPopover()) return

  appState.activeTab = TABS.MyPrompts

  // Check if we're in side panel mode
  if (isSidePanelMode()) {
    console.log('Frontend: Side panel mode detected, attempting to get current tab info...')
    console.log('Frontend: Current window location:', window.location.href)

    try {
      // Get current tab info
      const tabInfo = await getCurrentTabInfo()

      console.log('Frontend: Received tab info response:', tabInfo)

      if (tabInfo && tabInfo.url && tabInfo.title) {
        console.log('Frontend: Successfully captured tab info for bookmark:', {
          url: tabInfo.url,
          title: tabInfo.title,
          id: tabInfo.id,
        })

        // Validate that we're not capturing the extension's own URL
        if (tabInfo.url.startsWith('chrome-extension://')) {
          console.warn(
            'Frontend: Detected extension URL, falling back to normal bookmark behavior:',
            tabInfo.url,
          )
        } else if (tabInfo.url.includes('side_panel.html')) {
          console.warn(
            'Frontend: Detected side panel URL, falling back to normal bookmark behavior:',
            tabInfo.url,
          )
        } else {
          console.log('Frontend: Valid content URL detected, proceeding with auto-bookmark')

          // Set the bookmark data from current tab
          bookmarkLink.value = tabInfo.url
          appState.setNameForGeneratedPrompt(tabInfo.title)
          appState.setTextToCreatePrompt('') // Clear text content for bookmarks

          console.log('Frontend: Set bookmark data:', {
            bookmarkLink: bookmarkLink.value,
            promptName: tabInfo.title,
          })

          console.log('Frontend: Opening bookmark dialog with captured tab info')
          // Open the dialog with the current tab's info
          appState.openEditPromptBarDialog(true)
          return
        }
      } else {
        console.warn(
          'Frontend: Could not get current tab info in side panel mode, response was:',
          tabInfo,
        )
      }
    } catch (error) {
      console.error('Frontend: Error getting current tab info:', error)
    }
  } else {
    console.log('Frontend: Not in side panel mode, using normal bookmark behavior')
  }

  // Fallback to normal behavior (for non-side panel mode or if tab info fails)
  console.log('Frontend: Opening normal bookmark dialog (fallback)')
  appState.openEditPromptBarDialog(true)
}

let activeTabName = ref(getFromLocalStorage('activeTab') || TABS.Popular)

const insertAiResultToSearch = () => {
  if (!isLoggedIn.value) {
    appState.setLoginPopoverVisible(true)
    return
  }

  appState.setActiveTab(TABS.Library) // Change to the Library tab
  if (activeTabName.value === TABS.Popular) {
    selectedLangInPopularDropdown.value = locale.value
  } else if (activeTabName.value === TABS.Library) {
    selectedLangInLibraryDropdown.value = locale.value
  }
  nextTick(() => {
    const result = AIFunctionResult.value || '' // Ensure AIFunctionResult.value is a string
    const words = result.split(/\s+/) // Split the AIFunctionResult into individual words using regex to handle multiple spaces
    appState.openPromptBarDropdown()
    nextTick(() => {
      if (selectRef.value && selectRef.value.$el) {
        const input = selectRef.value.$el.querySelector('input')
        if (input) {
          input.focus()
          // Simulate typing each word followed by a space
          let currentIndex = 0
          const typeWord = () => {
            if (currentIndex < words.length) {
              input.value += words[currentIndex] + ' ' // Append each word followed by a space
              input.dispatchEvent(new Event('input')) // Dispatch input event to update the value
              const spaceEvent = new KeyboardEvent('keydown', {
                key: ' ',
                code: 'Space',
                keyCode: 32,
                charCode: 32,
                bubbles: true,
              })
              input.dispatchEvent(spaceEvent)
              currentIndex++
              setTimeout(typeWord, 100) // Add a larger delay between each word
            }
          }
          typeWord()
        }
      }
    })
  })
}

// Helper function to check if user is logged in and show login popover if not
const checkLoginAndShowPopover = () => {
  if (!isLoggedIn.value) {
    appState.setLoginPopoverVisible(true)
    return false
  }
  return true
}
</script>

<template>
  <div
    v-show="appState.showApp"
    class="prompt-manager"
    style="position: relative"
    :class="{ 'h-0 hideApp': !appState.showApp }"
  >
    <div
      v-if="isGeneratingAiResponse || AIFunctionResult || hasAiResultError"
      class="ai-response absolute w-full bg-neutral-200 dark:bg-neutral-800 border border-neutral-400 dark:border-neutral-700/75 rounded-lg z-[100]"
    >
      <div class="p-2.5 flex flex-col gap-2.5 dark:bg-neutral-900/70 rounded-lg">
        <div class="flex justify-between items-center">
          <div class="flex mx-2">
            <el-text class="font-bold">{{ activeAiButton }}:</el-text>
          </div>
          <div class="flex items-center gap-6">
            <div v-if="aiButtons[activeAiButton]?.creditsCost">
              <Tooltip
                :content="`${t(
                  'aiTools.yourCurrentCreditBalance',
                )}: ${creditsOwnedByUser} / ${creditsMax}`"
                placement="top"
                effect="light"
              >
                <el-text class="text-lg">
                  <el-icon size="1em" class="mr-1"><Coin /></el-icon>
                  <span class="relative">
                    {{ creditsOwnedByUser }}
                    <span
                      class="absolute flex items-center justify-end top-0 -right-5 text-red-500 opacity-0 font-bold text-right w-5"
                      :class="{ 'fade-out-up': creditsUsedAnimation }"
                      >-1</span
                    >
                  </span>
                </el-text>
              </Tooltip>
            </div>
            <el-button
              size="small"
              type="default"
              bg
              circle
              @click="closeAiResult"
              icon="Close"
              :disabled="isGeneratingAiResponse"
              data-testid="close-ai-result-button"
              :class="{ 'opacity-50': isGeneratingAiResponse }"
            >
            </el-button>
          </div>
        </div>

        <div>
          <div
            class="flex items-center"
            v-if="
              aiButtons[activeAiButton] && aiButtons[activeAiButton]?.optionTabs.value.length > 1
            "
            :class="{ 'opacity-50 pointer-events-none': isGeneratingAiResponse }"
          >
            <template
              v-for="(optionTab, optionTabIndex) in aiButtons[activeAiButton]?.optionTabs.value"
              :key="optionTabIndex"
            >
              <Tooltip
                v-if="optionTab.tooltip"
                :content="optionTab.tooltip"
                placement="top"
                effect="light"
              >
                <el-button
                  :disabled="!appState.hasSubscription && optionTab.secure"
                  size=""
                  :type="activeAiOptionTabIndex === optionTabIndex ? 'primary' : 'default'"
                  @click="
                    () => {
                      if (!checkLoginAndShowPopover()) return
                      ;(doComparison = optionTab.compare ?? false),
                        (activeAiOptionTabIndex = optionTabIndex),
                        setAIFunction(optionTab.topic, optionTab.engine)
                    }
                  "
                  plane
                  text
                  :data-testid="'ai-button-' + optionTab.name"
                  class="!rounded-none"
                  :class="
                    activeAiOptionTabIndex === optionTabIndex
                      ? '!bg-neutral-50 dark:!bg-neutral-900/90'
                      : ''
                  "
                >
                  <span>{{ optionTab.name }}</span>
                </el-button>
              </Tooltip>
              <template v-else>
                <el-button
                  :disabled="!appState.hasSubscription && optionTab.secure"
                  size=""
                  :type="activeAiOptionTabIndex === optionTabIndex ? 'primary' : 'default'"
                  @click="
                    () => {
                      if (!checkLoginAndShowPopover()) return
                      ;(doComparison = optionTab.compare ?? false),
                        (activeAiOptionTabIndex = optionTabIndex),
                        setAIFunction(optionTab.topic, optionTab.engine)
                    }
                  "
                  plane
                  text
                  :data-testid="'ai-button-' + optionTab.name"
                  class="!rounded-none"
                  :class="
                    activeAiOptionTabIndex === optionTabIndex
                      ? '!bg-neutral-50 dark:!bg-neutral-900/90'
                      : ''
                  "
                >
                  <span>{{ optionTab.name }}</span>
                </el-button>
              </template>
            </template>
          </div>
          <div
            class="p-2.5 bg-neutral-50 dark:bg-neutral-900/90 min-h-[12dvh] max-h-[200px] overflow-y-auto"
            style="text-align: left"
          >
            <template v-if="isGeneratingAiResponse">
              <el-skeleton :rows="3" animated> </el-skeleton>
            </template>
            <template v-else-if="AIFunctionResult">
              <div
                id="better-prompt-result"
                class="whitespace-pre-wrap text-sm"
                data-testid="better-prompt-result"
              >
                {{ AIFunctionResult }}
              </div>
            </template>
            <template v-else-if="hasAiResultError">
              <el-text class="text-red-500">
                An error occurred while generating the AI result. Please try again.
              </el-text>
            </template>
          </div>
          <div v-show="AIFunctionResult && !isGeneratingAiResponse && doComparison">
            <Tooltip
              :content="comparisonResult"
              raw-content
              placement="top"
              effect="light"
              :enterable="false"
              :disabled="isGeneratingComparison"
            >
              <el-button
                class="better-prompt-button-info w-full !bg-blue-500/5"
                type="primary"
                text
                size="large"
                :class="{ '!cursor-default': isGeneratingComparison }"
              >
                <div
                  class="w-full flex items-center justify-center gap-1 italic flex-wrap"
                  :class="{ 'opacity-75': isGeneratingComparison }"
                >
                  <template v-if="isGeneratingComparison">
                    <el-icon class="animate-spin text-xl"><Loading /></el-icon>
                    {{ t('aiTools.comparing') }}
                  </template>
                  <template v-else>
                    <el-text class="font-bold">{{
                      t('messages.thisPromptIsBetterThanOriginalBy')
                    }}</el-text>
                    <span class="font-bold text-lg">{{ comparisonResultPercent }}</span>
                    <span class="flex items-center text-xs"
                      >({{ t('messages.howIsItCalculated') }}
                      <el-icon class="ml-1"> <QuestionFilled /> </el-icon>)
                    </span>
                  </template>
                </div>
              </el-button>
            </Tooltip>
          </div>
        </div>

        <div class="better-prompt-button-group flex justify-center items-center gap-1 flex-wrap">
          <template v-if="isGeneratingAiResponse">
            <el-text class="mx-2 animate-pulse">{{ t('messages.generationInProgress') }}</el-text>
            <el-button
              size=""
              type="default"
              class="ml-auto"
              @click="cancelAiResult"
              icon="Close"
              text
            >
              {{ t('aiTools.cancelAndClose') }}
            </el-button>
          </template>
          <template v-else>
            <el-button
              v-if="!isLibraryButton && aiTopicCurrent !== aiTopic.Tip && !hasAiResultError"
              size=""
              type="default"
              class=""
              icon="Download"
              text
              @click="
                () => {
                  if (!checkLoginAndShowPopover()) return
                  insertAiResult()
                }
              "
            >
              {{ t('aiTools.insert') }}
            </el-button>
            <el-button
              v-if="!isLibraryButton && aiTopicCurrent !== aiTopic.Tip && !hasAiResultError"
              size=""
              type="default"
              class=""
              @click="
                () => {
                  if (!checkLoginAndShowPopover()) return
                  addPromptFromAiGeneratorToMyPrompts()
                }
              "
              icon="Plus"
              text
            >
              {{ t('aiTools.addToMyPrompts') }}
            </el-button>
            <el-button
              v-if="isLibraryButton"
              size=""
              type="default"
              class=""
              icon="Upload"
              text
              @click="
                () => {
                  if (!checkLoginAndShowPopover()) return
                  appState.setSelectedLanguageInLibraryDropdown(locale)
                  insertAiResultToSearch()
                }
              "
              :disabled="hasAiResultError"
            >
              {{ t('aiTools.findInLibrary') }}
            </el-button>
            <Tooltip
              :content="isRerollsLocked ? t('messages.regenerationLimitReached') : ''"
              placement="top"
              effect="light"
              :disabled="!isRerollsLocked"
            >
              <el-button
                size=""
                type="default"
                class=""
                @click="
                  () => {
                    if (!checkLoginAndShowPopover()) return
                    callAiRegenerate()
                  }
                "
                icon="Refresh"
                text
                :disabled="isRerollsLocked"
              >
                {{ t('btn.regenerate') }}
              </el-button>
            </Tooltip>
            <el-button size="" type="default" class="" @click="closeAiResult" icon="Close" text>
              {{ t('aiTools.close') }}
            </el-button>
          </template>
        </div>
      </div>
    </div>

    <div
      class="bottom-buttons relative my-2 bg-neutral-200 dark:bg-neutral-800 border border-neutral-400 dark:border-neutral-700/75 rounded-lg"
    >
      <div class="p-2.5 flex items-center gap-2 dark:bg-neutral-900/70 rounded-lg">
        <div v-if="props.showLogo" class="flex items-center">
          <el-popover
            trigger="click"
            :placement="popoverPlacement"
            effect="light"
            popper-class="ai-prompt-lab-popup"
            :teleported="false"
            width="100%"
          >
            <template #reference>
              <el-button
                type="default"
                circle
                text
                class="chat-prompt-button !bg-transparent !px-0"
              >
                <IconLogo
                  width="3em"
                  height="3em"
                  class="-mb-1"
                  title="AI Prompt Lab - click for more"
                />
              </el-button>
            </template>
            <Popup wide roundedBorder />
          </el-popover>
        </div>

        <div
          v-if="props.showLogo"
          class="h-full w-px bg-gray-300 dark:bg-gray-700 min-h-[1.5em]"
        ></div>
        <div
          class="flex items-center gap-1 flex-wrap mr-auto"
          :class="{
            'opacity-50 pointer-events-none': isGeneratingAiResponse || AIFunctionResult,
          }"
        >
          <template v-for="(button, buttonName) in aiButtons" :key="buttonName">
            <Tooltip
              :content="
                appState.outsideTextareaPrompt === '' || !isLoggedIn
                  ? aiTooltip
                  : isCreditsLocked
                  ? noCreditsTooltip
                  : `<center>${button.tooltip.value}${
                      button.creditsCost >= 0
                        ? `<br/><i>${t('aiTools.costWithCredits', button.creditsCost)}</i>`
                        : ''
                    }</center>`
              "
              placement="top"
              effect="light"
              raw-content
            >
              <el-button
                :type="activeAiButton === buttonName ? 'primary' : 'default'"
                text
                bg
                :size="button.size ? button.size : 'default'"
                @click="
                  () => {
                    if (!checkLoginAndShowPopover()) return
                    activateAIButton(buttonName, button)
                  }
                "
                :disabled="
                  ((appState.outsideTextareaPrompt === '' || isCreditsLocked) && isLoggedIn) ||
                  isLoggingIn ||
                  isSigningUp
                "
                :class="{
                  '!bg-blue-400': activeAiButton === buttonName,
                  '!text-blue-500': !isLoggedIn,
                  'opacity-80': isLoggedIn && appState.outsideTextareaPrompt === '',
                  'px-1': true,
                }"
              >
                <span class="relative">
                  <el-icon
                    v-if="buttonName === 'Better Prompt'"
                    size="1.2em"
                    class="absolute left-0"
                    :class="{
                      'animate-ping !text-blue-500':
                        appState.outsideTextareaPrompt &&
                        !(isGeneratingAiResponse || AIFunctionResult || isCreditsLocked),
                    }"
                  >
                    <MagicStick />
                  </el-icon>
                  <el-icon
                    v-if="button.icon && typeof button.icon !== 'string'"
                    size="1.2em"
                    class="mr-2"
                  >
                    <component :is="button.icon" />
                  </el-icon>
                  <el-icon v-else-if="button.icon" size="1.2em" class="mr-2">
                    {{ button.icon }}
                  </el-icon>
                </span>
                <span class="relative hidden sm:block">
                  <span
                    v-if="buttonName === 'Better Prompt'"
                    class="absolute left-0"
                    data-testid="better-prompt-button"
                    :class="{
                      'animate-pulse !text-blue-200':
                        appState.outsideTextareaPrompt &&
                        !(isGeneratingAiResponse || AIFunctionResult || isCreditsLocked),
                    }"
                  >
                    {{ button.name.value }}
                  </span>
                  <span class="z-10 relative">{{ button.name.value }}</span>
                </span>
                <span class="m-0 sm:ml-2 flex items-center gap-1">
                  <el-icon size="1em"><Coin /></el-icon> {{ button.creditsCost }}
                </span>
              </el-button>
            </Tooltip>
          </template>
        </div>

        <div class="h-full w-px bg-gray-300 dark:bg-gray-700 min-h-[1.5em]"></div>
        <div class="flex items-center gap-1">
          <!-- 1. button to add bookmark to selected prompt bar -->
          <Tooltip :content="bookmarkTooltipContent" placement="top" effect="light">
            <el-button
              type="default"
              bg
              class="chat-prompt-button sub-button add-bookmark"
              :data-testid="'select-bar-in-my-prompts-to-activate-ai-button'"
              size="small"
              @click="handleBookmarkClick"
              :disabled="(!hasActiveBar && isLoggedIn) || isLoggingIn || isSigningUp"
              :class="{ 'opacity-80': !hasActiveBar, '!text-blue-500': !isLoggedIn }"
            >
              <el-icon>
                <IconBookmark />
              </el-icon>
            </el-button>
          </Tooltip>
          <!-- 2. button to add a new prompt bar -->
          <Tooltip
            :content="isLoggedIn ? t('messages.addANewPromptBar') : t('messages.loginRequired')"
            placement="top"
            effect="light"
          >
            <el-button
              type="default"
              bg
              circle
              class="chat-prompt-button add-bar"
              :data-testid="'add-ai-prompt-bar-button'"
              @click="
                () => {
                  if (!checkLoginAndShowPopover()) return

                  appState.activeTab = TABS.MyPrompts
                  appState.openAddPromptBarDialog(true, true)
                }
              "
              :class="{
                '!text-blue-500': !isLoggedIn,
              }"
            >
              <el-icon size="1.3em">
                <Plus />
              </el-icon>
            </el-button>
          </Tooltip>
          <!-- 3. button to add a prompt to selected prompt bar -->
          <Tooltip
            :content="
              isLoggedIn
                ? hasOutsideTextareaPrompt
                  ? t('messages.addPromptToSelectedPromptBar')
                  : t('messages.enterPromptTextAndSelectBarInMyPromptsToActivate')
                : t('messages.loginRequired')
            "
            placement="top"
            effect="light"
          >
            <el-button
              type="default"
              bg
              class="chat-prompt-button sub-button add-prompt"
              size="small"
              :data-testid="'add-ai-prompt-button-default'"
              @click="
                () => {
                  if (!checkLoginAndShowPopover()) return
                  isPromptBookmark = false
                  appState.activeTab = TABS.MyPrompts
                  addOpenEditPromptBarDialog()
                }
              "
              :disabled="
                ((!hasOutsideTextareaPrompt || !hasActiveBar) && isLoggedIn) ||
                isLoggingIn ||
                isSigningUp
              "
              :class="{
                'opacity-80': !hasOutsideTextareaPrompt || !hasActiveBar,
                '!text-blue-500': !isLoggedIn,
              }"
            >
              <el-icon>
                <Expand />
              </el-icon>
            </el-button>
          </Tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.ai-prompt-lab-popup.el-popper {
  z-index: 9999 !important;
  padding: 0 !important;
  border: none !important;
  background: unset !important;
  box-shadow: none !important;
  --el-popper-border-radius: 0px !important;
  --el-border-color: transparent !important;
  --el-box-shadow: none !important;
  --el-box-shadow-light: none !important;
  --el-box-shadow-lighter: none !important;
  --el-box-shadow-dark: none !important;
}

.ai-prompt-lab-popup.el-popper.is-light {
  border: none !important;
  background: unset !important;
  box-shadow: none !important;
}

.ai-prompt-lab-popup .el-popper__body,
.ai-prompt-lab-popup .el-popper__content {
  padding: 0 !important;
  border: none !important;
  background: unset !important;
  box-shadow: none !important;
}
</style>
