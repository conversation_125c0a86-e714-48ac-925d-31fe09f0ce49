<script setup>
import { ElMessage } from 'element-plus'
import { doc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { computed, inject, nextTick, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useDocument } from 'vuefire'

import Analytics from '@/Analytics'
import { useAppStateStore } from '@/stores/appState'

import {
  barsRef,
  editBar,
  getUserBars,
  libraryBarsRef,
  popularBarsRef,
  publicBarsRef,
} from '../firebase'
import { updateMyPromptsBarKeyInLocalStorage } from '../localStorage'
import { cc, convertIdToString, TABS } from '../utils'

const props = defineProps(['bar', 'user'])

const barIdToCopyFrom = inject('barIdToCopyFrom', undefined)
// console.log('barIdToCopyFrom: ', barIdToCopyFrom.value)
const barsTabName = inject('barsTabName', undefined)
// console.log('barsTabName: ', barsTabName?.value)

const appState = useAppStateStore()

const { hasSubscription } = storeToRefs(appState)

const isSubscriptionDialogOpen = inject('isSubscriptionDialogOpen', ref(false))

const { t } = useI18n()

const copyOptions = {
  All: t('copyTab.copyAll'),
  Prompts: t('copyTab.copyOnlyPrompts'),
  Folders: t('copyTab.copyFolders'),
}

const promptMenuRef = ref(props.bar['promptMenu'])
const promptMenuData = computed(() =>
  props.bar['promptMenu'] ? JSON.parse(JSON.stringify(props.bar['promptMenu'])) : [],
)
const copyTo_data = ref(
  promptMenuData.value ? JSON.parse(JSON.stringify(promptMenuData.value)) : [],
)
const copyTo_treeRef = ref(null)
const copyTo_activeBar = ref(props.bar.name)
const copyTo_activeBarId = ref(props.bar.id)
const copyTo_barsList = ref([{ id: props.bar.id, name: props.bar.name }])
const copyTo_barsLoaded = ref(false)
const copyTo_current = ref(null)
const copyTo_changed = ref(false)
const copyTo_targetData = ref(copyTo_data.value)

const copyFrom_data = ref([])
const copyFrom_treeRef = ref(null)
const copyFrom_activeBar = ref('')
const copyFrom_barsList = ref([])
const copyFrom_barsLoaded = ref(false)

const copyFrom_activeCategory = ref(barsTabName?.value || TABS.MyPrompts)

const treeEmptyText = ref(t('copyTab.selectBarToCopyFrom'))

const message = (setContent, setType = 'info') => {
  ElMessage({
    message: setContent,
    type: setType,
  })
}

function openSubscriptionDialog(type = '') {
  Analytics.fireEvent('subscription_dialog', { type })
  isSubscriptionDialogOpen.value = true
}

const generateRandomId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

function flattenToGetPrompts(trees) {
  const flatArray = []
  function addLeafNodes(node) {
    // Check if the node is a leaf node
    if (!node.children) {
      const leafNode = {}
      for (const key in node) {
        if (key !== 'children') {
          leafNode[key] = node[key]
        }
      }
      flatArray.push(leafNode)
    } else {
      node.children.forEach(addLeafNodes)
    }
  }
  trees.forEach(addLeafNodes)
  return flatArray
}
function flattenToGetFolders(trees) {
  const flatArray = []
  function flattenTree(node) {
    if (node.hasOwnProperty('children')) {
      const treeNode = {}
      for (const key in node) {
        if (key !== 'children') {
          treeNode[key] = node[key]
        }
      }
      flatArray.push(treeNode)
    }
    if (node.children) {
      node.children.forEach(flattenTree)
    }
  }
  trees.forEach(flattenTree)

  return flatArray
}

const allowDrop = (draggingNode, dropNode, dropType) => {
  // console.log('draggingNode: ', draggingNode, '\n dropNode: ', dropNode, '\n dropType: ', dropType)
  if (dropNode.data.prompt !== undefined) return dropType !== 'inner'
  return true
}
const nodeDrop = (draggingNode, dropNode, dropType, ev) => {
  // console.log('draggingNode: ', draggingNode, '\n dropNode: ', dropNode, '\n dropType: ', dropType, '\n ev: ', ev)
  copyTo_changed.value = true
}

const getAncestorsIds = (nodes, id, ancestors = []) => {
  for (const node of nodes) {
    if (node.id === id) return [...ancestors, node.id]
    if (node.children) {
      const foundAncestors = getAncestorsIds(node.children, id, [...ancestors, node.id])
      if (foundAncestors) return foundAncestors
    }
  }
  return null
}

const filterTreeByAncestors = (nodes, ancestors, selectedIds) => {
  return nodes
    .filter(
      (node) =>
        selectedIds.includes(node.id) ||
        (node.children && filterTreeByAncestors(node.children, ancestors, selectedIds)),
    )
    .map((node) => ({
      ...node,
      children: node.children
        ? filterTreeByAncestors(node.children, ancestors, selectedIds)
        : undefined,
    }))
    .filter((node) => selectedIds.includes(node.id) || (node.children && node.children.length > 0))
}

const copyFromChecked = computed(() => {
  const checkedNodes = copyFrom_treeRef?.value?.getCheckedNodes(false, true)
  if (checkedNodes && checkedNodes.length > 0) {
    const selectedIds = checkedNodes.map((node) => node.id)
    const result = new Set()
    checkedNodes.forEach((checkedNode) => {
      const targetId = checkedNode.id
      const ancestorsIds = getAncestorsIds(copyFrom_data.value, targetId)
      if (ancestorsIds) {
        const filteredTree = filterTreeByAncestors(copyFrom_data.value, ancestorsIds, selectedIds)
        result.add(JSON.stringify(filteredTree)) // Dodajemy zserializowane drzewo do Set
      }
    })
    return Array.from(result).map((tree) => JSON.parse(tree))
  }
  return []
})

const findNodeById = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const foundNode = findNodeById(node.children, id)
      if (foundNode) {
        return foundNode
      }
    }
  }
  return null
}

const copyToChecked = computed(() => {
  return findNodeById(copyTo_data.value, copyTo_treeRef?.value?.getCurrentKey())
})

const onBarSelectorFocus = async (myBarsForCopyTo) => {
  // console.log('onBarSelectorFocus: ', copyFrom_activeCategory.value)

  if (copyFrom_barsLoaded.value) return

  if (myBarsForCopyTo) {
    copyTo_barsList.value = await getUserBars(props.user?.uid)
    copyTo_barsLoaded.value = true
    return
  }

  if (copyFrom_activeCategory.value === TABS.MyPrompts) {
    copyFrom_barsList.value = await getUserBars(props.user?.uid)
  }

  if (copyFrom_activeCategory.value === TABS.Public) {
    const doc = useDocument(publicBarsRef)
    const bars = await doc.promise.value
    copyFrom_barsList.value = Object.entries(bars).map(([barId, bar]) => ({ ...bar, id: barId }))
  }

  if (copyFrom_activeCategory.value === TABS.Popular) {
    const doc = useDocument(popularBarsRef)
    const bars = await doc.promise.value
    copyFrom_barsList.value = Object.entries(bars).map(([barId, bar]) => ({ ...bar, id: barId }))
  }

  if (copyFrom_activeCategory.value === TABS.Library) {
    const doc = useDocument(libraryBarsRef)
    const bars = await doc.promise.value
    copyFrom_barsList.value = Object.entries(bars).map(([barId, bar]) => ({ ...bar, id: barId }))
  }

  // console.log('copyFrom_barsList: ', copyFrom_barsList.value)
  copyFrom_barsLoaded.value = true
}

const getBarById = async (barId) => {
  // console.log('getBarById: ', barId)
  const barDoc = useDocument(doc(barsRef, barId))
  // console.log('getBarById doc: ', barDoc)
  const bar = await barDoc.promise.value
  // console.log(new Date().toUTCString() + ' Read-> barsRef: getBarById')
  // console.log('bar: ', bar)
  bar.promptMenu = JSON.parse(bar.prompt_menu, convertIdToString)
  bar.linkMenu = bar.links ? JSON.parse(bar.links, convertIdToString) : []

  return bar
}
const copyFrom_onBarChange = async (barId) => {
  // console.log('copyFrom_onBarChange: ', barId)
  const bar = await getBarById(barId)

  copyFrom_data.value = bar.promptMenu

  if (barIdToCopyFrom?.value) {
    // console.log('loaded bar from click: ', bar)
    copyFrom_activeBar.value = bar.name
  }
}

if (barIdToCopyFrom?.value) {
  // console.log('if barIdToCopyFrom: ', barIdToCopyFrom)
  nextTick(() => {
    copyFrom_onBarChange(barIdToCopyFrom.value)
  })
}

const copyTo_onBarChange = async (barId) => {
  // console.log('copyTo_onBarChange: ', barId)
  const bar = await getBarById(barId)

  copyTo_data.value = bar.promptMenu
  copyTo_targetData.value = copyTo_data.value

  copyTo_activeBar.value = bar.name

  copyTo_activeBarId.value = bar.id
}

const copyToOnClick = (node, data, e, f) => {
  // console.log('copyToOnClick: ', node, data, e, f)
  if (copyTo_current.value?.id !== copyTo_treeRef.value.getCurrentKey()) {
    copyTo_treeRef.value.setCurrentKey(node?.id)
    copyTo_current.value = node
    copyTo_targetData.value = data
    // promptMenuData.value = [data]
    return
  }
  copyTo_treeRef.value.setCurrentKey(null)
  copyTo_current.value = null
  copyTo_targetData.value = copyTo_data.value
}

const copyFromChangeChecked = () => {
  if (copyFromChecked.value.length === 0) {
    copyFrom_treeRef.value.setCheckedNodes(copyFrom_data.value, false)
  } else {
    copyFrom_treeRef.value.setCheckedNodes([], false)
  }
}

const copySelected = (option) => {
  let sourceData = JSON.parse(JSON.stringify(copyFromChecked.value[0]))

  if (!sourceData || !copyTo_targetData) {
    message(t('copyTab.errors.somethingWentWrong'), 'error')
    return
  }

  if (option === copyOptions.Prompts) {
    sourceData = flattenToGetPrompts(sourceData)
    if (sourceData.length === 0) {
      message(t('copyTab.errors.noPromptsToCopy'), 'warning')
      return
    }
  }
  if (option === copyOptions.Folders) sourceData = flattenToGetFolders(sourceData)

  let promptsCount = 0
  let foldersCount = 0
  const changeIds = (node) => {
    if (!node) return
    node.id = generateRandomId()
    if (node.children) {
      node.children.forEach((child) => changeIds(child))
      foldersCount++
    } else {
      promptsCount++
    }
  }

  const changeIdsInTree = (tree) => {
    if (Array.isArray(tree)) {
      tree.forEach((node) => changeIds(node))
    } else {
      changeIds(tree)
    }
  }

  changeIdsInTree(sourceData)
  // console.log('copy source: new ids: ', sourceData)

  if (copyToChecked?.value?.children) {
    // console.log('f updated copyTo_targetData: ', copyTo_targetData.value)
    copyTo_targetData.value = copyTo_targetData.value.data.children
  }
  //  console.log('copy copyTo_targetData: ', copyTo_targetData)
  copyTo_targetData.value.push(...sourceData)
  copyTo_changed.value = true

  message(
    `${promptsCount > 0 ? t('copyTab.copied.prompts', { count: promptsCount }) : ''} 
    ${
      promptsCount > 0 && foldersCount > 0
        ? ' ' + t('copyTab.copied.folders', { count: foldersCount })
        : ''
    } 
    ${
      foldersCount > 0 && promptsCount === 0
        ? t('copyTab.copied.folders', { count: foldersCount })
        : ''
    } 
    ${t('copyTab.copied.successfully')}`,
  )
}

const remove = () => {
  // console.log(
  //   'remove: ',
  //   copyTo_current.value,
  //   copyTo_data.value,
  //   copyTo_targetData.value,
  //   copyTo_targetData.value.parent,
  // )
  let index = ''
  if (copyTo_targetData.value.parent.data.children) {
    index = copyTo_targetData.value.parent.data.children.findIndex(
      (item) => item.id === copyTo_current.value.id,
    )
    copyTo_targetData.value.parent.data.children.splice(index, 1)
  } else {
    index = copyTo_targetData.value.parent.data.findIndex(
      (item) => item.id === copyTo_current.value.id,
    )
    copyTo_targetData.value.parent.data.splice(index, 1)
  }

  copyTo_targetData.value = copyTo_data.value
  copyTo_treeRef.value.setCurrentKey(null)
  copyTo_current.value = null
  copyTo_changed.value = true
}

const cancel = () => {
  // console.log('cancel: ', promptMenuData.value, copyTo_data.value)

  // copyTo_data.value = JSON.parse(JSON.stringify(promptMenuData.value))
  // copyTo_targetData.value = copyTo_data.value

  copyTo_onBarChange(copyTo_activeBarId.value)

  copyTo_treeRef.value.setCurrentKey(null)
  copyTo_current.value = null
  copyTo_changed.value = false
}

const saveToServer = async () => {
  let newData = JSON.parse(JSON.stringify(copyTo_data.value))
  // console.log('saveToServer: newData: ', newData, copyTo_activeBar.value)

  await editBar(props.bar, {
    prompt_menu: JSON.stringify(newData),
  })

  if (copyTo_activeBarId.value === props.bar.id) {
    props.bar.promptMenu = newData
    props.bar.prompt_menu = JSON.stringify(newData)
    updateMyPromptsBarKeyInLocalStorage('promptMenu', newData)
  }

  copyTo_changed.value = false
  message(`Bar ${copyTo_activeBar.value} saved successfully`, 'success')
}

watch(
  () => promptMenuData.value,
  (newValue) => {
    // console.log('watch promptMenuData.value: ', newValue)
    copyTo_data.value = JSON.parse(JSON.stringify(newValue))
    copyTo_targetData.value = copyTo_data.value
  },
)

watch(
  () => copyFrom_activeCategory.value,
  (newVal, oldVal) => {
    // console.log('watch copyFrom_activeCategory: ', newVal, oldVal)
    copyFrom_barsLoaded.value = false
    copyFrom_activeBar.value = ''
    copyFrom_data.value = []
  },
)
</script>

<template>
  <div class="flex flex-col gap-4">
    <el-row :gutter="20">
      <el-col :span="10"> {{ t('copyTab.copyTo') + ':' }} </el-col>
      <el-col :span="4"> </el-col>
      <el-col :span="10">
        {{ t('copyTab.copyFrom') + ':' }}

        <el-button-group v-if="hasSubscription" class="ml-4">
          <el-button
            type="primary"
            @click="copyFrom_activeCategory = TABS.MyPrompts"
            text
            :disabled="copyFrom_activeCategory === TABS.MyPrompts"
            size="small"
          >
            {{ t('tabs.' + cc('my prompts')) }}
          </el-button>
          <el-button
            type="primary"
            @click="copyFrom_activeCategory = TABS.Popular"
            text
            :disabled="copyFrom_activeCategory === TABS.Popular"
            size="small"
          >
            {{ t('tabs.popular') }}
          </el-button>
          <el-button
            type="primary"
            @click="copyFrom_activeCategory = TABS.Public"
            text
            :disabled="copyFrom_activeCategory === TABS.Public"
            size="small"
          >
            {{ t('tabs.public') }}
          </el-button>
          <el-button
            type="primary"
            @click="copyFrom_activeCategory = TABS.Library"
            text
            :disabled="copyFrom_activeCategory === TABS.Library"
            size="small"
          >
            {{ t('tabs.library') }}
          </el-button>
        </el-button-group>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-select
          class="text-lg font-medium w-full"
          v-model="copyTo_activeBarId"
          filterable
          :placeholder="t('copyTab.selectBar')"
          :teleported="false"
          @change="copyTo_onBarChange"
          @focus="onBarSelectorFocus(true)"
        >
          <el-option
            v-for="(bar, index) in copyTo_barsList"
            :key="bar.id"
            :label="bar.name"
            :value="bar.id"
            class="border-b border-opacity-25 border-gray-300 last-of-type:border-b-0 w-full max-w-[99dvw]"
          >
          </el-option>
        </el-select>
        <el-input v-if="false" class="" v-model="props.bar.name" readonly disabled> </el-input>
      </el-col>
      <el-col :span="4"> </el-col>
      <el-col :span="10">
        <el-select
          class="text-lg font-medium w-full"
          v-model="copyFrom_activeBar"
          filterable
          placeholder="Select Bar"
          data-testid="copy-prompt-select"
          :teleported="false"
          @change="copyFrom_onBarChange"
          @focus="onBarSelectorFocus()"
        >
          <el-option
            v-for="(bar, index) in copyFrom_barsList"
            :key="bar.id"
            :label="bar.name"
            :value="bar.id"
            data-testid="copy-prompt-select-option"
            class="border-b border-opacity-25 border-gray-300 last-of-type:border-b-0 w-full max-w-[99dvw]"
          >
            <div class="selectMenu leading-snug py-2">
              <div class="flex gap-2 items-center">
                {{ bar.name }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-tree
          id="copyTo_data"
          ref="copyTo_treeRef"
          :data="copyTo_data"
          draggable
          highlight-current
          :expand-on-click-node="false"
          node-key="id"
          :empty-text="t('copyTab.empty')"
          :auto-expand-parent="false"
          :allow-drop="allowDrop"
          class="bg-blue-500/5 border border-blue-500/25 rounded-lg p-1 h-full min-h-[30vh] max-h-[50vh] overflow-y-auto"
          @node-click="copyToOnClick"
          @node-drop="nodeDrop"
        >
          <template #default="{ node, data }">
            <span class="w-full flex justify-between items-center">
              <span class="custom-tree-node w-full grid items-center gap-1 group">
                <el-icon v-if="data.prompt === undefined">
                  <Folder />
                </el-icon>
                <el-icon v-if="data.prompt !== undefined">
                  <Document />
                </el-icon>

                <el-text truncated class="">
                  {{ node.label }}
                </el-text>
              </span>

              <span
                v-if="
                  copyTo_treeRef && !node.children && node.key === copyTo_treeRef.getCurrentKey()
                "
                class="mx-2 flex gap-1"
              >
                <el-icon v-if="copyTo_targetData && copyTo_targetData?.data?.children"
                  ><Aim
                /></el-icon>
              </span>
            </span>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="4" class="!p-0">
        <div v-if="hasSubscription" class="h-full flex flex-col gap-4 p-2 justify-between">
          <div class="flex flex-col gap-4 bg-blue-500/5 -mr-5 pr-5 -ml-2 p-2 rounded-l-lg">
            <el-text class="opacity-75">{{ t('copyTab.copyFromRight') }}:</el-text>
            <div>
              <el-button
                type="primary"
                plain
                @click="copySelected(copyOptions.All)"
                class="w-full min-w-[120px]"
                :disabled="copyFromChecked?.length === 0"
                icon="CopyDocument"
              >
                <div class="w-full text-xs whitespace-normal">
                  {{ t('copyTab.copyAllSelected') }}
                </div>
              </el-button>
            </div>
            <div>
              <el-button
                type="primary"
                plain
                @click="copySelected(copyOptions.Prompts)"
                class="w-full min-w-[120px]"
                :disabled="copyFromChecked?.length === 0"
                icon="CopyDocument"
              >
                <div class="w-full text-xs whitespace-normal">
                  {{ t('copyTab.onlyPrompts') }}
                </div>
              </el-button>
            </div>
            <div v-if="0 == 1">
              <el-button
                type="primary"
                plain
                @click="copySelected(copyOptions.Folders)"
                class="w-full min-w-[120px]"
                :disabled="copyFromChecked?.length === 0"
                icon="CopyDocument"
              >
                <div class="w-full whitespace-normal">{{ t('copyTab.copyFolders') }}</div>
              </el-button>
            </div>
          </div>
          <div class="flex flex-col gap-4 bg-blue-500/5 -ml-5 pl-5 -mr-2 p-2 rounded-r-lg">
            <el-text class="opacity-75">{{ t('copyTab.actionOnLeft') }}:</el-text>
            <div>
              <el-button
                type="danger"
                plain
                @click="remove"
                class="w-full min-w-[120px]"
                :disabled="!copyTo_current"
                icon="Delete"
              >
                <div class="w-full whitespace-normal">{{ t('btn.remove') }}</div>
              </el-button>
            </div>
            <div>
              <el-button
                plain
                @click="cancel"
                class="w-full min-w-[120px]"
                :disabled="!copyTo_changed"
                icon="Close"
              >
                <div class="w-full whitespace-normal">{{ t('btn.cancel') }}</div>
              </el-button>
            </div>
            <div>
              <el-button
                type="success"
                plain
                @click="saveToServer"
                class="w-full min-w-[120px]"
                :disabled="!copyTo_changed"
                icon="Select"
              >
                <div class="w-full whitespace-normal">{{ t('btn.save') }}</div>
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="10">
        <el-tree
          id="copyFromTree"
          ref="copyFrom_treeRef"
          :data="copyFrom_data"
          :expand-on-click-node="false"
          node-key="id"
          :auto-expand-parent="false"
          show-checkbox
          data-testid="copy-prompt-tree"
          :empty-text="treeEmptyText + ' ' + copyFrom_activeCategory"
          :check-on-click-node="true"
          class="bg-blue-500/5 border border-blue-500/25 rounded-lg p-1 h-full min-h-[30vh] max-h-[50vh] overflow-y-auto"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node w-full grid items-center gap-1 group">
              <el-icon v-if="data.prompt === undefined">
                <Folder />
              </el-icon>
              <el-icon v-if="data.prompt !== undefined">
                <Document />
              </el-icon>

              <el-text truncated class="flex-auto">
                {{ node.label }}
              </el-text>

              <el-popover
                v-if="data.prompt !== undefined"
                placement="right"
                :width="300"
                trigger="hover"
                :teleported="false"
                :offset="0"
              >
                <template #reference>
                  <el-icon class="mx-1"><InfoFilled /></el-icon>
                </template>
                <template #default>
                  <el-text
                    v-if="node.data.description"
                    class="whitespace-break-spaces break-normal text-start font-medium line-clamp-[10]"
                    >{{ node.data.description }}</el-text
                  >
                  <br v-if="node.data.description" />
                  <el-text
                    v-if="node.data.prompt"
                    class="whitespace-break-spaces break-normal text-start line-clamp-[10]"
                    >{{ node.data.prompt }}</el-text
                  >
                </template>
              </el-popover>
            </span>
          </template>
        </el-tree>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="10"></el-col>
      <el-col :span="4"></el-col>
      <el-col :span="10">
        <el-button
          type="primary"
          plain
          text
          @click="copyFromChangeChecked"
          class="w-full mt-2"
          :disabled="copyFrom_data?.length === 0"
        >
          <div class="w-full flex justify-center">{{ t('copyTab.uncheckAll') }}</div>
        </el-button>
      </el-col>
    </el-row>
  </div>
  <div
    v-if="!hasSubscription"
    class="absolute inset-0 w-full bg-neutral-200/75 dark:bg-neutral-800/75 flex justify-center items-center text-center"
  >
    <div class="flex flex-col gap-4 justify-center items-center">
      <el-icon size="40"><Lock /></el-icon>
      <el-text>{{ t('copyTab.featureOnlyForSubscription') }}</el-text>
      <el-button
        type="info"
        plain
        @click="openSubscriptionDialog('copy_prompts')"
        class="text-purple-400 focus:bg-purple-400 hover:!bg-purple-500 hover:!text-white"
        >{{ t('subscription.buySubscription') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.custom-tree-node {
  grid-template-columns: max-content auto max-content;
}

button.w-full :deep(> span) {
  width: 100%;
}
</style>
