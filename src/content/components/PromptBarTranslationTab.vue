<script setup>
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

import { usePromptBarActions } from '../composables/usePromptBarActions'
import { getCopyToUserList } from '../firebase'

const appState = useAppStateStore()
const { isAdmin, selectedBarId, selectedBar, promptBarLanguage, supportedLanguages } =
  storeToRefs(appState)

const { t } = useI18n()

const selectedUser = ref([])
const usersList = ref([])
const isCopyingToUser = ref(false)
const isCopyingAndTranslating = ref(false)
const selectedLanguage = ref(promptBarLanguage.value)

const handleLanguageChange = (value) => {
  selectedLanguage.value = value
}

onMounted(async () => {
  const users = await getCopyToUserList()
  usersList.value = users.map((u) => (typeof u === 'string' ? { id: u, name: u } : u))
})

const { copyMyPromptBar, copyToUser, copyToUserAndTranslate } = usePromptBarActions({
  selectedLanguage,
})

const handleCopyMyPromptBar = () => copyMyPromptBar({ selectedBar })
const handleCopyToUser = async () => {
  isCopyingToUser.value = true
  try {
    await copyToUser({ selectedUser, selectedBar })
  } finally {
    isCopyingToUser.value = false
  }
}
const handleCopyAndTranslate = async () => {
  isCopyingAndTranslating.value = true
  try {
    await copyToUserAndTranslate({ selectedUser, selectedBar })
  } finally {
    isCopyingAndTranslating.value = false
  }
}
</script>

<template>
  <div class="translation-section">
    <template v-if="!isAdmin">
      <el-button
        type="primary"
        size="small"
        class="mb-4"
        @click="handleCopyMyPromptBar"
        block
        data-testid="copy-my-prompt-bar-button"
      >
        {{ t('copyTab.copy') }}
      </el-button>
    </template>

    <template v-if="isAdmin">
      <div class="mb-2 font-semibold">{{ t('copyTab.copyToUser') }}</div>
      <el-select
        v-model="selectedUser"
        class="my-select mb-2"
        :placeholder="t('translationTab.selectUserPlaceholder')"
        data-testid="user-select-for-copy"
        multiple
        style="width: 100%"
      >
        <el-option v-for="user in usersList" :key="user.id" :label="user.name" :value="user.id" />
      </el-select>
      <el-button
        type="primary"
        size="small"
        @click="handleCopyToUser"
        :disabled="!selectedUser || selectedUser.length === 0 || isCopyingToUser"
        :loading="isCopyingToUser"
        block
        data-testid="copy-prompt-bar-button"
      >
        {{ t('copyTab.copyTo') }}
      </el-button>
      <el-divider class="my-4" />
    </template>

    <div v-if="isAdmin" class="mb-2 font-semibold">
      {{ t('editPromptBar.tabs.translationTab') }}
    </div>
    <el-select
      v-if="isAdmin"
      v-model="selectedLanguage"
      @change="handleLanguageChange"
      class="language-select mb-2"
      size="small"
      style="max-width: 200px"
      popper-class="language-select-dropdown"
      data-testid="language-select-dropdown-translation-tab"
    >
      <el-option
        v-for="lang in supportedLanguages"
        :key="lang.value"
        :label="lang.label"
        :value="lang.value"
        data-testid="language-select-option-in-edit-mode-translation-tab"
      />
    </el-select>
    <el-button
      v-if="isAdmin"
      type="primary"
      size="small"
      @click="handleCopyAndTranslate"
      :disabled="!selectedUser || selectedUser.length === 0 || isCopyingAndTranslating"
      block
      :loading="isCopyingAndTranslating"
    >
      {{ t('copyTab.copy') }} & {{ t('editPromptBar.tabs.translationTab') }}
    </el-button>
  </div>
</template>

<style scoped>
.translation-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
