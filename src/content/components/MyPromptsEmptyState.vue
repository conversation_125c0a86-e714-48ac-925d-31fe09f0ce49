<script setup>
import { useI18n } from 'vue-i18n'

defineProps({
  hasSubscription: <PERSON><PERSON><PERSON>,
  canAddMoreFreePromptBars: <PERSON><PERSON><PERSON>,
  selectedBarId: String,
  selectedBar: Object,
  isTabEmpty: <PERSON>ole<PERSON>,
})

const emit = defineEmits([
  'open-subscription-dialog',
  'edit-prompt-bar',
  'open-edit-prompt-bar-dialog',
  'open-add-prompt-bar-dialog',
])

const { t } = useI18n()

const handleOpenSubscriptionDialog = () => {
  emit('open-subscription-dialog', 'tab')
}

const handleEditPromptBar = () => {
  emit('edit-prompt-bar')
}

const handleOpenEditPromptBarDialog = () => {
  emit('open-edit-prompt-bar-dialog', null, 'copyTab')
}

const handleOpenAddPromptBarDialog = () => {
  emit('open-add-prompt-bar-dialog', false, true)
}
</script>

<template>
  <template v-if="!hasSubscription && !canAddMoreFreePromptBars && !selectedBarId">
    {{ t('messages.canNotAddMoreFreeBars') }}
    <el-link class="text-base ml-1" type="primary" @click="handleOpenSubscriptionDialog">{{
      t('subscription.buySubscription')
    }}</el-link
    >.
  </template>
  <template v-else-if="selectedBar?.promptMenu?.length === 0">
    {{ t('bar.empty') + '. ' + t('bar.click') }}
    <el-link class="text-base mx-1" type="primary" @click="handleEditPromptBar">{{
      t('btn.edit')
    }}</el-link>
    {{ t('messages.addSomePrompts') }}
    <el-link class="text-base mx-1" type="primary" @click="handleOpenEditPromptBarDialog">{{
      t('btn.copy')
    }}</el-link>
    {{ t('messages.fromAnotherBar') }}
  </template>

  <template v-else>
    {{ t('bar.noBarSelected') }}
    <el-link class="text-base ml-1" type="primary" @click="handleOpenAddPromptBarDialog">{{
      t('btn.add')
    }}</el-link
    >.
  </template>
</template>
