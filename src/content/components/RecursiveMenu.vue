<script setup>
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { computed, inject, nextTick, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import Analytics from '../../Analytics'
import { useAppStateStore } from '../../stores/appState'
import { usePromptStore } from '../stores/promptStore'
import { getPromptWithProcessedParts, processPromptFields, TABS } from '../utils'
import iconBookmark from './icons/iconBookmark.vue'
import Tooltip from './Tooltip.vue'

const maxStringLength = 1000

// console.log('RecursiveMenu')

const props = defineProps(['data', 'isAdmin', 'loadContent', 'destination'])
const emits = defineEmits(['onPromptClick', 'onPromptClickUrl'])

// console.log('recursive menu props: ', props)
const appState = useAppStateStore()
const {
  hasSubscription,
  isSubscriptionDialogOpen,
  isShareDialogPromptBarOpen,
  sharePrompt,
  activeTab,
} = storeToRefs(appState)

// Remove local computed property
// const hasSubscription = computed(() => appState.hasSubscription)
// Use store value instead of inject
// const isSubscriptionDialogOpen = inject('isSubscriptionDialogOpen', ref(false))
// const isShareDialogPromptBarOpen = inject('isShareDialogPromptBarOpen', ref(false))

// Use store value instead of local ref and inject
// const sharePrompt = ref(inject('sharePrompt'))

// Use store value instead of inject
// const activeTabName = inject('activeTabName')
const isShareVisible = computed(() => {
  if (activeTab.value === TABS.MyPrompts || activeTab.value === TABS.Public) {
    return true
  } else {
    return false
  }
})

const buySubscription = (type = '') => {
  // window.open('https://ai-promptlab.com/', '_blank')
  Analytics.fireEvent('subscription_dialog', { type })
  isSubscriptionDialogOpen.value = true
}

const openSharePrompt = (item) => {
  // console.log('openSharePrompt: ', item)
  appState.setSharePrompt({ prompt: item, destination: item.destination || props.destination })
  nextTick(() => {
    appState.openSharePromptBarDialog()
  })
}

const onPromptClick = ({ item, destination, type = 'default', target = '' }) => {
  // console.log('onPromptClick: ', item, destination, type, target)
  // console.log('hasSubscription: ', hasSubscription)
  // console.log('item.premium: ', item.premium)

  if (item.premium && !hasSubscription.value) {
    buySubscription('premium_prompt')
    return
  }

  emits('onPromptClick', { item, destination, type, target })
}

const onPromptClickUrl = ({ item, destination, type = 'default', target = '' }) => {
  // console.log('onPromptClickUrl: ', item, destination, type, target)
  // console.log('hasSubscription: ', hasSubscription)
  // console.log('item.premium: ', item.premium)
  if (item.premium && !hasSubscription.value) {
    buySubscription('premium_prompt')
    return
  }

  emits('onPromptClickUrl', { item, destination, type, target })
}

const promptStore = usePromptStore()

const isPromptModalOpen = computed(
  () => !!(promptStore.isPromptPopupOpen && promptStore.currentPrompt),
)

const getDescription = (item) => {
  // Your logic to get the description
  return item.description || 'No description available'
}

const openPromptPopup = async (item, event) => {
  if (event) {
    event.stopPropagation()
  }

  const finalPrompt = processPromptFields(item)

  await promptStore.openPromptPopup(
    {
      ...item,
      processedPrompt: finalPrompt,
    },
    props.destination,
  )
}

onMounted(async () => {
  if (promptStore.sharedPromptId) {
    const item = props.data.find((item) => item.id === promptStore.sharedPromptId)
    if (item && !item.isBookmark && !promptStore.isPromptPopupOpen) {
      await nextTick()
      await new Promise((resolve) => setTimeout(resolve, 100))
      openPromptPopup(item)
    }
  }
})

const closeDialog = () => {
  promptStore.closePromptPopup()
}

const handleSelectChange = (value) => {}

const insertPrompt = () => {
  const finalPrompt = getPromptWithProcessedParts(promptStore.currentPrompt.processedPrompt)

  emits('onPromptClick', {
    item: { ...promptStore.currentPrompt, prompt: finalPrompt },
    type: 'config',
  })
  closeDialog()
}

const copyPromptToClipboard = async () => {
  const finalPrompt = getPromptWithProcessedParts(promptStore.currentPrompt.processedPrompt)
  try {
    await navigator.clipboard.writeText(finalPrompt)
    ElMessage.success(t('promptConfig.copied'))
  } catch (e) {
    ElMessage.error(t('promptConfig.copyFailed'))
  }
}

const hasSameUrlAsActual = (item) => {
  let actualUrl = window.location.href
  if (item.destination === actualUrl || props.destination === actualUrl) {
    return true
  }
  return false
}

const { t } = useI18n()
</script>

<template>
  <div v-for="item in data" class="prompt-recursiveMenu">
    <el-sub-menu
      v-if="item.children"
      :index="item.id"
      :id="item.id"
      class="recursive-submenu rounded-md"
      :teleported="false"
      :popper-offset="0"
    >
      <template #title
        ><span>{{ item.label }}</span></template
      >

      <template v-if="item.children?.length === 0">
        <el-menu-item disabled>{{ t('menu.folderEmpty') }}</el-menu-item>
      </template>
      <RecursiveMenu
        v-if="loadContent"
        :data="item.children"
        :isAdmin="isAdmin"
        :loadContent="'true'"
        :destination="item.destination || props.destination"
        @onPromptClick="onPromptClick"
        @onPromptClickUrl="onPromptClickUrl($event)"
      />
      <el-skeleton v-else style="width: 100%" animated class="p-2"> </el-skeleton>
    </el-sub-menu>
    <template v-else>
      <template v-if="item.premium && !hasSubscription && false">
        <div class="flex justify-between items-center">
          <el-menu-item
            @click=""
            :index="item.id"
            :id="item.id"
            :prompt="item"
            class="recursive-link rounded-md flex justify-between gap-2"
            disabled
          >
            {{ item.label }}
          </el-menu-item>
          <span class="px-2 text-xs text-purple-400">Premium</span>
        </div>
      </template>

      <el-menu-item
        :index="item.id"
        :id="item.id"
        :prompt="item"
        class="recursive-link rounded-md flex cursor-auto !px-0"
      >
        <div class="flex-1 flex items-center justify-between prompt-item">
          <template v-if="(!item.premium || hasSubscription) && !item.isBookmark">
            <Tooltip
              :content="t('menu.openPromptConfig')"
              placement="top"
              effect="light"
              :hide-after="50"
              class="pointer-events-none"
              :enterable="false"
            >
              <el-button
                class="!bg-transparent hover:!bg-white/10 px-1 !ml-0 prompt-config-button"
                @click.stop="(event) => openPromptPopup(item, event)"
                icon="Expand"
                text
              >
              </el-button>
            </Tooltip>
          </template>
          <template v-if="(!item.premium || hasSubscription) && item.isBookmark">
            <el-popover
              placement="top"
              :width="300"
              trigger="hover"
              :teleported="false"
              :offset="0"
              :show-after="300"
            >
              <template #default>
                <div v-if="item.destination || props.destination" class="text-xs text-center">
                  <p class="mb-2">{{ t('menu.goToDestinationUrl') }}</p>
                  <p class="mb-2 text-center whitespace-break-spaces break-normal italic">
                    {{ item.destination || props.destination }}
                  </p>
                  <div>
                    <el-button
                      @click.stop="
                        onPromptClickUrl({
                          item,
                          destination: item.destination || props.destination,
                          type: 'redirect',
                          target: '_self',
                        })
                      "
                      size="small"
                      >{{ t('menu.openUrl') }}</el-button
                    >
                    <el-button
                      @click.stop="
                        onPromptClickUrl({
                          item,
                          destination: item.destination || props.destination,
                          type: 'redirect',
                          target: '_blank',
                        })
                      "
                      size="small"
                      >{{ t('menu.openInNewTab') }}</el-button
                    >
                  </div>
                </div>
                <div v-else class="text-center">{{ t('menu.bookmarkWithoutDestination') }}</div>
              </template>
              <template #reference>
                <el-button
                  class="!bg-transparent hover:!bg-white/10 px-1 !ml-0"
                  text
                  @click.stop
                  :disabled="!item.destination && !props.destination"
                >
                  <iconBookmark class="mx-1" />
                </el-button>
              </template>
            </el-popover>
          </template>

          <Tooltip
            :content="
              item.isBookmark
                ? hasSameUrlAsActual(item)
                  ? t('menu.youAreHere')
                  : item.destination || props.destination
                  ? t('menu.goToBookmark') + (item.destination || props.destination)
                  : t('menu.bookmarkWithoutDestination')
                : t('menu.insertPrompt') + item.label
            "
            placement="top-start"
            effect="light"
            :hide-after="50"
            class="pointer-events-none"
            :enterable="false"
          >
            <el-button
              :id="item.id"
              text
              :icon="item.premium && !hasSubscription ? 'Lock' : ''"
              class="mr-auto !bg-transparent hover:!bg-white/10"
              :class="{
                '!text-purple-400': item.premium && !hasSubscription,
                'hover:underline': item.isBookmark,
              }"
              @click.stop="
                item.isBookmark
                  ? onPromptClickUrl({
                      item,
                      destination: item.destination || props.destination,
                      type: 'redirect',
                      target: '_self',
                    })
                  : onPromptClick({ item })
              "
              :disabled="
                item.isBookmark &&
                (hasSameUrlAsActual(item) || (!item.destination && !props.destination))
              "
            >
              {{ item.label }}
            </el-button>
          </Tooltip>

          <Tooltip
            v-if="item.premium && isAdmin"
            :content="t('menu.promptPremiumEnabled')"
            placement="top-start"
            effect="light"
            :hide-after="50"
            class="pointer-events-none"
            :enterable="false"
          >
            <el-icon v-if="item.premium && isAdmin" class="text-purple-400"><Unlock /></el-icon>
          </Tooltip>
          <div class="flex items-center">
            <el-popover
              :width="300"
              trigger="hover"
              :teleported="false"
              :offset="0"
              :show-after="300"
            >
              <template #reference>
                <span class="flex items-center">
                  <el-button
                    class="p-1 mr-0 !bg-transparent"
                    icon="InfoFilled"
                    text
                    @click.stop
                  ></el-button>
                </span>
              </template>
              <template #default>
                <div>
                  <div
                    class="text-xs"
                    v-if="item.destination || props.destination || isShareVisible"
                  >
                    <p
                      v-if="item.destination || props.destination"
                      class="mb-2 text-center whitespace-break-spaces break-normal"
                    >
                      <!-- Prompt destination: -->
                      <span class="italic">{{ item.destination || props.destination }}</span>
                    </p>
                    <div class="text-center flex flex-wrap gap-1 justify-center">
                      <el-button
                        v-if="item.destination || props.destination"
                        @click.stop="
                          emits('onPromptClick', {
                            item,
                            destination: item.destination || props.destination,
                            type: 'redirect',
                            target: '_self',
                          })
                        "
                        icon="Link"
                        size="small"
                        >{{ t('btn.openUrl') }}</el-button
                      >
                      <el-button
                        v-if="item.destination || props.destination"
                        @click.stop="
                          emits('onPromptClick', {
                            item,
                            destination: item.destination || props.destination,
                            type: 'redirect',
                            target: '_blank',
                          })
                        "
                        icon="TopRight"
                        size="small"
                        >{{ t('btn.openInNewTab') }}
                      </el-button>
                      <Tooltip
                        v-if="isShareVisible && (!item.premium || hasSubscription)"
                        content="Share prompt"
                        placement="top"
                        effect="light"
                        :hide-after="50"
                        class="pointer-events-none"
                        :enterable="false"
                      >
                        <el-button
                          v-if="isShareVisible && (!item.premium || hasSubscription)"
                          class=""
                          @click.stop="openSharePrompt(item)"
                          icon="Share"
                          size="small"
                        >
                          {{ t('btn.share') }}
                        </el-button>
                      </Tooltip>
                    </div>
                  </div>

                  <div v-if="item.description && item.description !== ''">
                    <el-divider
                      class="my-4"
                      v-if="item.destination || props.destination || isShareVisible"
                    />
                    <el-text
                      class="block w-full max-h-[45vh] overflow-auto whitespace-break-spaces break-normal text-start"
                    >
                      {{ getDescription(item) }}
                    </el-text>
                  </div>

                  <div
                    v-if="
                      (!item.premium || (item.prompt && hasSubscription)) &&
                      !item.isBookmark &&
                      item.prompt !== ''
                    "
                  >
                    <el-divider
                      class="my-4"
                      v-if="
                        item.destination || props.destination || isShareVisible || item.description
                      "
                    />
                    <el-text
                      class="block w-full max-h-[45vh] overflow-auto whitespace-break-spaces break-normal text-start"
                    >
                      {{ item.prompt }}
                    </el-text>
                  </div>

                  <div v-if="item.premium && !hasSubscription" class="text-center">
                    <div class="pb-2">{{ t('menu.premiumPromptsRequireSubscription') }}</div>
                    <el-button
                      type="info"
                      plain
                      @click="buySubscription('premium_prompt')"
                      class="text-purple-400 focus:bg-purple-400 hover:!bg-purple-500 hover:!text-white"
                      >{{ t('menu.buySubscription') }}
                    </el-button>
                  </div>

                  <!-- <div v-if="item.premium && isAdmin" class="py-2 text-orange-600 text-center">
                    <el-divider class="my-4" />
                    <p>Prompt premium</p>
                    <p>Admin mode - prompt enabled - Enjoy!</p>
                  </div> -->
                </div>
              </template>
            </el-popover>
          </div>
        </div>
      </el-menu-item>
    </template>
  </div>

  <el-dialog
    v-model="isPromptModalOpen"
    :title="promptStore.currentPromptName"
    width="clamp(min(100%, 500px), 90vw, 1200px)"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeDialog"
    destroy-on-close
    :modal="true"
  >
    <div id="promptForm" class="formatted-prompt" @click.stop @keydown.stop>
      <div v-if="promptStore.currentPrompt?.description">
        <div class="mb-2">{{ t('promptConfig.description') }}:</div>
        <el-text
          class="block w-full max-h-[45vh] overflow-auto whitespace-break-spaces break-normal text-start"
        >
          {{ promptStore.currentPrompt.description }}
        </el-text>
      </div>
      <div v-if="promptStore.currentPrompt?.destination" class="flex gap-2 justify-between">
        <el-text
          class="block w-full max-h-[45vh] mb-2 overflow-auto whitespace-break-spaces break-normal text-start text-xs"
          >Destination: <span class="italic">{{ promptStore.currentPrompt.destination }}</span>
        </el-text>
        <el-button
          @click.stop="
            emits('onPromptClickUrl', {
              item: promptStore.currentPrompt,
              destination: promptStore.currentPrompt.destination,
              type: 'redirect',
              target: '_self',
            })
          "
          size="small"
        >
          {{ t('btn.openUrl') }}
        </el-button>
        <el-button
          @click.stop="
            emits('onPromptClickUrl', {
              item: promptStore.currentPrompt,
              destination: promptStore.currentPrompt.destination,
              type: 'redirect',
              target: '_blank',
            })
          "
          size="small"
        >
          {{ t('btn.openInNewTab') }}
        </el-button>
      </div>
      <el-divider />
      <div v-if="promptStore.currentPrompt" class="prompt-box">
        <template v-for="(part, index) in promptStore.currentPrompt.processedPrompt" :key="index">
          <div v-if="part.type === 'select'" class="prompt-part">
            <el-select
              v-model="part.value"
              :style="{
                width: `${
                  Math.max(...part.options.map((option) => option.label.length)) * 10 + 100
                }px`,
              }"
              @change="handleSelectChange"
            >
              <el-option
                v-for="option in part.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <div
              v-if="
                promptStore.currentPrompt.processedPrompt[index + 1] &&
                promptStore.currentPrompt.processedPrompt[index + 1].type === 'comment'
              "
              class="comment"
            >
              {{ promptStore.currentPrompt.processedPrompt[index + 1].content }}
            </div>
          </div>
          <div v-else-if="part.type === 'input'" class="prompt-part">
            <el-input
              v-model="part.value"
              :placeholder="part.placeholder"
              :style="{ width: `${part.placeholder.length * 10 + 100}px` }"
              @keydown.stop
            />
            <div
              v-if="
                promptStore.currentPrompt.processedPrompt[index + 1] &&
                promptStore.currentPrompt.processedPrompt[index + 1].type === 'comment'
              "
              class="comment"
            >
              {{ promptStore.currentPrompt.processedPrompt[index + 1].content }}
            </div>
          </div>
          <div v-else-if="part.type === 'textarea'" class="prompt-part">
            <el-input
              v-model="part.value"
              type="textarea"
              :placeholder="part.placeholder"
              :style="{ width: `${Math.min(part.placeholder.length * 10 + 300, 500)}px` }"
              @keydown.stop
            />
            <div
              v-if="
                promptStore.currentPrompt.processedPrompt[index + 1] &&
                promptStore.currentPrompt.processedPrompt[index + 1].type === 'comment'
              "
              class="comment"
            >
              {{ promptStore.currentPrompt.processedPrompt[index + 1].content }}
            </div>
          </div>
          <span v-else-if="part.type === 'text'" class="prompt-text">{{ part.content }}</span>
          <br v-else-if="part.type === 'linebreak'" />
          <div v-else-if="part.type === 'comment'" class="comment">{{ part.content }}</div>
        </template>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ t('btn.cancel') }}</el-button>
        <el-button @click="copyPromptToClipboard">{{ t('btn.copy') }}</el-button>
        <el-button type="primary" @click="insertPrompt">{{ t('btn.insert') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scope>
.el-menu.el-menu-recursive {
  border: none !important;
  padding-left: 0.5rem !important;
}

.el-menu--horizontal .recursive-submenu .el-sub-menu__title,
.el-menu.el-menu--horizontal .el-menu-item.recursive-link {
  line-height: 2rem !important;
  height: 2rem !important;
}

.prompt-recursiveMenu .el-menu--popup .el-sub-menu:not(.is-active) {
  background: transparent !important;
}

.prompt-recursiveMenu li:not(:hover) > button ~ div,
.prompt-recursiveMenu li:not(:hover) li {
  pointer-events: none;
}

.prompt-recursiveMenu li > button ~ div {
  transition: opacity 200ms ease-in-out;
}

.prompt-recursiveMenu li > button ~ div {
  opacity: 0;
}

.prompt-recursiveMenu li:hover > button ~ div {
  opacity: 1;
}

.prompt-recursiveMenu li li:hover > button > svg {
  transform: rotate(-90deg);
}

.prompt-recursiveMenu li:hover > button {
  background-color: rgb(0 0 0 / 10%);
}

.prompt-recursiveMenu div > a,
.prompt-recursiveMenu div > button,
.prompt-recursiveMenu div > button > span {
  max-width: 15rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.prompt-recursiveMenu div > button > span {
  max-width: 18rem;
  display: block;
}

.prompt-recursiveMenu .el-menu-item,
.prompt-recursiveMenu .el-sub-menu {
  background-color: rgba(115, 115, 115, 0.2);
}

.prompt-recursiveMenu .el-menu-item.is-active,
.prompt-recursiveMenu .el-sub-menu.is-active {
  background-color: rgba(59, 131, 246, 0.1);
}

.formatted-prompt {
  font-size: 1.2em;
  line-height: 1.5;
}

.prompt-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.prompt-part {
  display: flex;
  align-items: center;
  margin-right: 1em;
  margin-bottom: 0.5em;
}

.prompt-text {
  margin-right: 0.5em;
  margin-bottom: 0.5em;
}

#prompt-manager br {
  width: 100%;
  content: '';
  display: block;
  margin-bottom: 0.5em;
}

.prompt-recursiveMenu .el-menu-item [class^='el-icon'] {
  margin-right: 0;
}

.prompt-recursiveMenu .el-menu--popup {
  padding: 5px;
}

.prompt-recursiveMenu .el-button:disabled,
.prompt-recursiveMenu .el-button.is-disabled {
  color: #e3e2e2 !important;
}
</style>
