<template>
  <div>
    <input
      class="input-field"
      :value="modelValue"
      :placeholder="placeholder"
      @input="updateValue"
      :type="type"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  type: { type: String, default: 'text' },
})
const emit = defineEmits(['update:modelValue'])

const updateValue = (event) => {
  emit('update:modelValue', event.target.value)
}
</script>

<style scoped>
.input-field {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 5px;
  width: 200px;
}
</style>
