<script setup>
import { computed } from 'vue'

import { avaibleSitesUrlAndName } from '../sites_index.js'
import { sitesOrder } from './sites_config.js'

const props = defineProps({
  wide: {
    type: Boolean,
    default: false,
  },
  roundedBorder: {
    type: Boolean,
    default: false,
  },
})

const gridColsClass = computed(() => {
  return props.wide ? 'grid-cols-5' : 'grid-cols-2'
})

const mainWidthClass = computed(() => {
  return props.wide ? '' : 'w-96'
})

const roundedClass = computed(() => {
  return props.roundedBorder ? 'rounded-lg' : ''
})

const avaibleSitesUrlAndNameById = Object.fromEntries(
  avaibleSitesUrlAndName.map((site) => [site.id, site]),
)

const processedSites = computed(() =>
  sitesOrder.map((id) => avaibleSitesUrlAndNameById[id]).filter(Boolean),
)

const openLink = (url) => {
  if (chrome?.tabs?.create) {
    chrome.tabs.create({ url })
  } else {
    window.open(url, '_blank')
  }
}
</script>

<template>
  <main
    :class="[
      'p-2',
      mainWidthClass,
      roundedClass,
      'bg-white',
      'dark:bg-neutral-900',
      'border-1',
      'border-gray-200',
      'dark:border-gray-800',
    ]"
  >
    <div :class="['grid', gridColsClass, 'gap-2']">
      <div
        v-for="site in processedSites"
        :key="site.url"
        @click="openLink(site.url)"
        class="group bg-gray-100 dark:bg-neutral-800 px-2 py-1 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-800 dark:hover:bg-neutral-700 hover:scale-105 active:scale-95"
      >
        <div class="flex items-center justify-between">
          <div class="flex-1 min-w-0">
            <div
              class="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-sm truncate mt-2"
            >
              {{ site.name }}
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate -mt-1 mb-2">
              {{ site.url.replace('https://', '').split('/')[0] }}
            </p>
          </div>
          <div
            class="opacity-0 group-hover:opacity-100 transition-opacity text-blue-600 dark:text-blue-400 ml-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped></style>
