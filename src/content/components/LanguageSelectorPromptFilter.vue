<script setup>
import { storeToRefs } from 'pinia'
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '../../stores/appState'
import { TABS } from '../utils.js'

const appState = useAppStateStore()
const {
  activeTab,
  selectedBarId,
  areLibraryBarsLoaded,
  arePopularBarsLoaded,
  popularBars,
  libraryBars,
  selectedLangInPopularDropdown,
  selectedLangInLibraryDropdown,
  supportedLanguages,
} = storeToRefs(appState)

const { locale } = useI18n()

const lang = ref(
  activeTab.value === TABS.Popular
    ? localStorage.getItem('barLangPopular') || locale.value
    : activeTab.value === TABS.Library
    ? localStorage.getItem('barLangLibrary') || locale.value
    : locale.value,
)

const handleLocaleChange = (value) => {
  localStorage.removeItem(activeTab.value)
  selectedBarId.value = null
  if (activeTab.value === TABS.Popular) {
    selectedLangInPopularDropdown.value = value
    localStorage.setItem('barLangPopular', value)
    arePopularBarsLoaded.value = false
    popularBars.value = []
  } else if (activeTab.value === TABS.Library) {
    selectedLangInLibraryDropdown.value = value
    localStorage.setItem('barLangLibrary', value)
    areLibraryBarsLoaded.value = false
    libraryBars.value = []
  }
}

watch(selectedLangInLibraryDropdown, (newLang) => {
  lang.value = newLang
})

// Blur handler should just return to prevent default behavior
const handleBlur = () => {
  return
}
</script>

<template>
  <el-select
    v-model="lang"
    size="small"
    @change="handleLocaleChange"
    @blur="handleBlur"
    popper-class="language-selector-popper"
    :teleported="false"
    aria-label="Language selector"
    data-testid="language-filter-dropdown"
  >
    <el-option
      v-for="item in supportedLanguages"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      data-testid="language-filter-option"
    />
  </el-select>
</template>

<style scoped>
:deep(.language-selector-popper) {
  z-index: 3000;
}
</style>
