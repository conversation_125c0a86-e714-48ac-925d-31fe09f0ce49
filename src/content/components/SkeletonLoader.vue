<template>
  <div class="skeleton-loader">
    <div class="skeleton-header">
      <div class="skeleton-title"></div>
      <div class="skeleton-actions">
        <div class="skeleton-button"></div>
        <div class="skeleton-button"></div>
      </div>
    </div>
    <div class="skeleton-content">
      <div class="skeleton-text"></div>
      <div class="skeleton-text"></div>
      <div class="skeleton-text"></div>
    </div>
    <div class="skeleton-footer">
      <div class="skeleton-tags">
        <div class="skeleton-tag"></div>
        <div class="skeleton-tag"></div>
        <div class="skeleton-tag"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.skeleton-loader {
  padding: 1rem;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.skeleton-title {
  width: 200px;
  height: 24px;
  background: var(--el-skeleton-color);
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-actions {
  display: flex;
  gap: 0.5rem;
}

.skeleton-button {
  width: 32px;
  height: 32px;
  background: var(--el-skeleton-color);
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-content {
  margin-bottom: 1rem;
}

.skeleton-text {
  height: 16px;
  background: var(--el-skeleton-color);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-text:last-child {
  width: 80%;
}

.skeleton-footer {
  display: flex;
  justify-content: flex-start;
}

.skeleton-tags {
  display: flex;
  gap: 0.5rem;
}

.skeleton-tag {
  width: 60px;
  height: 24px;
  background: var(--el-skeleton-color);
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
