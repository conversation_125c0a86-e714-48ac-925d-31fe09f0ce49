<template>
  <div class="ai-management" :class="{ 'dark-theme': isDarkTheme }">
    <!-- Management Tabs -->
    <div class="management-tabs">
      <el-tabs v-model="activeManagementTab" type="border-card">
        <el-tab-pane label="My AI Management" name="myai">
          <!-- My AI Management Content -->
          <div class="myai-management">
            <!-- Add New AI Entry Form -->
            <div class="add-ai-form">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="form-header">
                    <h3>{{ t('aiManagement.addForm.title') }}</h3>
                  </div>
                </template>

                <el-form
                  ref="addFormRef"
                  :model="newAIEntry"
                  :rules="formRules"
                  label-width="120px"
                  @submit.prevent="addAIEntry"
                >
                  <el-form-item :label="t('aiManagement.form.name')" prop="name">
                    <el-input
                      v-model="newAIEntry.name"
                      :placeholder="t('aiManagement.form.namePlaceholder')"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item :label="t('aiManagement.form.website')" prop="url">
                    <el-input
                      v-model="newAIEntry.url"
                      :placeholder="t('aiManagement.form.websitePlaceholder')"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item :label="t('aiManagement.form.tags')" prop="tags">
                    <el-select
                      v-model="newAIEntry.tags"
                      multiple
                      filterable
                      allow-create
                      default-first-option
                      :placeholder="t('aiManagement.form.tagsPlaceholder')"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="tag in availableTags"
                        :key="tag"
                        :label="tag"
                        :value="tag"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item :label="t('aiManagement.form.description')" prop="description">
                    <el-input
                      v-model="newAIEntry.description"
                      type="textarea"
                      :rows="3"
                      :placeholder="t('aiManagement.form.descriptionPlaceholder')"
                    />
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="addAIEntry" :icon="Plus">
                      {{ t('aiManagement.actions.add') }}
                    </el-button>
                    <el-button @click="resetForm">
                      {{ t('aiManagement.actions.reset') }}
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>

            <!-- AI Entries Table -->
            <div class="ai-entries-table">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="table-header">
                    <h3>{{ t('aiManagement.table.title') }} ({{ aiEntries.length }})</h3>
                    <div class="table-actions">
                      <el-input
                        v-model="searchQuery"
                        :placeholder="t('aiManagement.search.placeholder')"
                        clearable
                        style="width: 300px"
                      >
                        <template #prefix>
                          <el-icon><Search /></el-icon>
                        </template>
                      </el-input>
                    </div>
                  </div>
                </template>

                <el-table
                  :data="filteredAIEntries"
                  style="width: 100%"
                  :empty-text="t('aiManagement.table.empty')"
                  stripe
                >
                  <el-table-column
                    prop="name"
                    :label="t('aiManagement.table.name')"
                    min-width="150"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="url"
                    :label="t('aiManagement.table.website')"
                    min-width="200"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      <el-link :href="row.url" target="_blank" type="primary">
                        {{ row.url }}
                      </el-link>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="tags"
                    :label="t('aiManagement.table.tags')"
                    min-width="200"
                  >
                    <template #default="{ row }">
                      <el-tag
                        v-for="tag in row.tags"
                        :key="tag"
                        size="small"
                        style="margin-right: 4px; margin-bottom: 4px"
                      >
                        {{ tag }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="description"
                    :label="t('aiManagement.table.description')"
                    min-width="200"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="dateAdded"
                    :label="t('aiManagement.table.dateAdded')"
                    width="120"
                  >
                    <template #default="{ row }">
                      {{ formatDate(row.dateAdded) }}
                    </template>
                  </el-table-column>

                  <el-table-column
                    :label="t('aiManagement.table.actions')"
                    width="120"
                    fixed="right"
                  >
                    <template #default="{ row }">
                      <el-button size="small" type="primary" @click="editAIEntry(row)" :icon="Edit">
                        {{ t('aiManagement.actions.edit') }}
                      </el-button>
                      <el-popconfirm
                        :title="t('aiManagement.confirmations.delete')"
                        @confirm="deleteAIEntry(row)"
                        :width="250"
                      >
                        <template #reference>
                          <el-button
                            size="small"
                            type="danger"
                            :icon="Delete"
                            style="margin-left: 8px"
                          >
                            {{ t('aiManagement.actions.delete') }}
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </div>

            <!-- Edit AI Entry Dialog -->
            <el-dialog
              v-model="editDialogVisible"
              :title="t('aiManagement.editDialog.title')"
              width="600px"
              destroy-on-close
              class="edit-ai-dialog"
              :class="{ 'dark-theme': isDarkTheme }"
            >
              <el-form
                ref="editFormRef"
                :model="editingEntry"
                :rules="formRules"
                label-width="120px"
                v-if="editingEntry"
              >
                <el-form-item :label="t('aiManagement.form.name')" prop="name">
                  <el-input
                    v-model="editingEntry.name"
                    :placeholder="t('aiManagement.form.namePlaceholder')"
                    clearable
                  />
                </el-form-item>

                <el-form-item :label="t('aiManagement.form.website')" prop="url">
                  <el-input
                    v-model="editingEntry.url"
                    :placeholder="t('aiManagement.form.websitePlaceholder')"
                    clearable
                  />
                </el-form-item>

                <el-form-item :label="t('aiManagement.form.tags')" prop="tags">
                  <el-select
                    v-model="editingEntry.tags"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    :placeholder="t('aiManagement.form.tagsPlaceholder')"
                    style="width: 100%"
                  >
                    <el-option v-for="tag in availableTags" :key="tag" :label="tag" :value="tag" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="t('aiManagement.form.description')" prop="description">
                  <el-input
                    v-model="editingEntry.description"
                    type="textarea"
                    :rows="3"
                    :placeholder="t('aiManagement.form.descriptionPlaceholder')"
                  />
                </el-form-item>
              </el-form>

              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="editDialogVisible = false">
                    {{ t('aiManagement.actions.cancel') }}
                  </el-button>
                  <el-button type="primary" @click="saveEditedEntry">
                    {{ t('aiManagement.actions.save') }}
                  </el-button>
                </div>
              </template>
            </el-dialog>
          </div>
        </el-tab-pane>

        <el-tab-pane label="AI Sites Management" name="sites">
          <!-- AI Sites Management Content -->
          <div class="sites-management">
            <!-- Add New Site Form -->
            <div class="add-site-form">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="form-header">
                    <h3>Add New AI Site</h3>
                  </div>
                </template>

                <el-form
                  ref="addSiteFormRef"
                  :model="newSite"
                  :rules="siteFormRules"
                  label-width="120px"
                  @submit.prevent="addNewSite"
                >
                  <el-form-item label="Site Name" prop="name">
                    <el-input v-model="newSite.name" placeholder="Enter site name" clearable />
                  </el-form-item>

                  <el-form-item label="Website URL" prop="url">
                    <el-input v-model="newSite.url" placeholder="https://example.com" clearable />
                  </el-form-item>

                  <el-form-item label="Tags" prop="tags">
                    <el-select
                      v-model="newSite.tags"
                      multiple
                      filterable
                      allow-create
                      default-first-option
                      placeholder="Select or create tags"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="tag in availableSiteTags"
                        :key="tag"
                        :label="tag"
                        :value="tag"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="addNewSite" :icon="Plus">
                      Add Site
                    </el-button>
                    <el-button @click="resetSiteForm"> Reset </el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>

            <!-- Sites Table -->
            <div class="sites-table">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="table-header">
                    <h3>AI Sites ({{ sitesCount }})</h3>
                    <div class="table-actions">
                      <el-input
                        v-model="sitesSearchQuery"
                        placeholder="Search sites..."
                        clearable
                        style="width: 300px"
                      >
                        <template #prefix>
                          <el-icon><Search /></el-icon>
                        </template>
                      </el-input>
                    </div>
                  </div>
                </template>

                <el-table
                  :data="filteredSites"
                  style="width: 100%"
                  empty-text="No sites found"
                  stripe
                  v-loading="isSitesLoading"
                >
                  <el-table-column
                    prop="name"
                    label="Site Name"
                    min-width="150"
                    show-overflow-tooltip
                  />

                  <el-table-column prop="url" label="Website" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                      <el-link :href="row.url" target="_blank" type="primary">
                        {{ row.url }}
                      </el-link>
                    </template>
                  </el-table-column>

                  <el-table-column prop="tags" label="Tags" min-width="200">
                    <template #default="{ row }">
                      <el-tag
                        v-for="tag in row.tags"
                        :key="tag"
                        size="small"
                        style="margin-right: 4px; margin-bottom: 4px"
                      >
                        {{ tag }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column label="Actions" width="120" fixed="right">
                    <template #default="{ row }">
                      <el-button size="small" type="primary" @click="editSite(row)" :icon="Edit">
                        Edit
                      </el-button>
                      <el-popconfirm
                        title="Are you sure you want to delete this site?"
                        @confirm="deleteSite(row)"
                        :width="250"
                      >
                        <template #reference>
                          <el-button
                            size="small"
                            type="danger"
                            :icon="Delete"
                            style="margin-left: 8px"
                          >
                            Delete
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </div>

            <!-- Edit Site Dialog -->
            <el-dialog
              v-model="sitesEditDialogVisible"
              title="Edit AI Site"
              width="600px"
              destroy-on-close
              class="edit-site-dialog"
              :class="{ 'dark-theme': isDarkTheme }"
            >
              <el-form
                ref="editSiteFormRef"
                :model="editingSite"
                :rules="siteFormRules"
                label-width="120px"
                v-if="editingSite"
              >
                <el-form-item label="Site Name" prop="name">
                  <el-input v-model="editingSite.name" placeholder="Enter site name" clearable />
                </el-form-item>

                <el-form-item label="Website URL" prop="url">
                  <el-input v-model="editingSite.url" placeholder="https://example.com" clearable />
                </el-form-item>

                <el-form-item label="Tags" prop="tags">
                  <el-select
                    v-model="editingSite.tags"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    placeholder="Select or create tags"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="tag in availableSiteTags"
                      :key="tag"
                      :label="tag"
                      :value="tag"
                    />
                  </el-select>
                </el-form-item>
              </el-form>

              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="sitesEditDialogVisible = false"> Cancel </el-button>
                  <el-button type="primary" @click="saveEditedSite"> Save </el-button>
                </div>
              </template>
            </el-dialog>
          </div>
        </el-tab-pane>

        <el-tab-pane label="Tags Management" name="tags">
          <!-- Tags Management Content -->
          <div class="tags-management">
            <!-- Add New Tag Form -->
            <div class="add-tag-form">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="form-header">
                    <h3>Add New Tag</h3>
                  </div>
                </template>

                <el-form @submit.prevent="addNewTag">
                  <el-form-item>
                    <el-input
                      v-model="newTagName"
                      placeholder="Enter tag name"
                      clearable
                      @keyup.enter="addNewTag"
                      style="width: 300px; margin-right: 1rem"
                    />
                    <el-button type="primary" @click="addNewTag" :icon="Plus"> Add Tag </el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>

            <!-- Tags List -->
            <div class="tags-list">
              <el-card shadow="hover" :body-style="{ padding: '1.5rem' }">
                <template #header>
                  <div class="table-header">
                    <h3>All Tags ({{ tagsCount }})</h3>
                    <div class="table-actions">
                      <el-button @click="cleanupUnusedTags" type="warning" size="small">
                        Cleanup Unused Tags
                      </el-button>
                    </div>
                  </div>
                </template>

                <div class="tags-grid">
                  <el-tag
                    v-for="tag in allTags"
                    :key="tag"
                    closable
                    size="large"
                    @close="removeTagWithConfirmation(tag)"
                    style="margin: 4px"
                  >
                    {{ tag }}
                    <span class="tag-usage-count"> ({{ getTagUsageCount(tag) }}) </span>
                  </el-tag>
                </div>

                <div v-if="allTags.length === 0" class="empty-tags">
                  <p>No tags available. Add some tags above.</p>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { Delete, Edit, Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAISitesManagement } from '../composables/useAISitesManagement.js'
import { useMyAISettings } from '../composables/useMyAISettings.js'

const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const { t } = useI18n()

// Use the existing AI settings composable
const { myAI, addToMyAI, removeFromMyAI, updateMyAIItem } = useMyAISettings()

// Use the AI sites management composable
const {
  sitesWithTags,
  allTags,
  isLoading: isSitesLoading,
  sitesCount,
  tagsCount,
  addSite,
  updateSite,
  removeSite,
  addTag,
  removeTag,
} = useAISitesManagement()

// Reactive data
const searchQuery = ref('')
const editDialogVisible = ref(false)
const editingEntry = ref(null)
const addFormRef = ref(null)
const editFormRef = ref(null)

// Sites management reactive data
const sitesSearchQuery = ref('')
const sitesEditDialogVisible = ref(false)
const editingSite = ref(null)
const addSiteFormRef = ref(null)
const editSiteFormRef = ref(null)
const activeManagementTab = ref('myai') // 'myai', 'sites', or 'tags'

// Tags management reactive data
const newTagName = ref('')

// New AI entry form data
const newAIEntry = ref({
  name: '',
  url: '',
  tags: [],
  description: '',
})

// New site form data
const newSite = ref({
  name: '',
  url: '',
  tags: [],
})

// Form validation rules
const formRules = {
  name: [
    { required: true, message: 'Name is required', trigger: 'blur' },
    { min: 2, max: 100, message: 'Name should be 2-100 characters', trigger: 'blur' },
  ],
  url: [
    { required: true, message: 'Website URL is required', trigger: 'blur' },
    { type: 'url', message: 'Please enter a valid URL', trigger: 'blur' },
  ],
}

// Site form validation rules
const siteFormRules = {
  name: [
    { required: true, message: 'Site name is required', trigger: 'blur' },
    { min: 2, max: 100, message: 'Site name should be 2-100 characters', trigger: 'blur' },
  ],
  url: [
    { required: true, message: 'Site URL is required', trigger: 'blur' },
    { type: 'url', message: 'Please enter a valid URL', trigger: 'blur' },
  ],
}

// Computed properties
const aiEntries = computed(() => myAI.value || [])

const filteredAIEntries = computed(() => {
  if (!searchQuery.value) return aiEntries.value

  const query = searchQuery.value.toLowerCase()
  return aiEntries.value.filter(
    (entry) =>
      entry.name.toLowerCase().includes(query) ||
      entry.url.toLowerCase().includes(query) ||
      entry.description?.toLowerCase().includes(query) ||
      entry.tags.some((tag) => tag.toLowerCase().includes(query)),
  )
})

const availableTags = computed(() => {
  const tags = new Set()
  aiEntries.value.forEach((entry) => {
    if (entry.tags) {
      entry.tags.forEach((tag) => tags.add(tag))
    }
  })
  return Array.from(tags).sort()
})

// Sites computed properties
const filteredSites = computed(() => {
  if (!sitesSearchQuery.value) return sitesWithTags.value

  const query = sitesSearchQuery.value.toLowerCase()
  return sitesWithTags.value.filter(
    (site) =>
      site.name.toLowerCase().includes(query) ||
      site.url.toLowerCase().includes(query) ||
      site.tags.some((tag) => tag.toLowerCase().includes(query)),
  )
})

const availableSiteTags = computed(() => {
  return allTags.value || []
})

// Methods
const addAIEntry = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()

    // Validate URL format
    if (!isValidUrl(newAIEntry.value.url)) {
      ElMessage.error(t('aiManagement.messages.invalidUrl'))
      return
    }

    // Add the new entry
    addToMyAI({
      name: newAIEntry.value.name,
      url: newAIEntry.value.url,
      tags: newAIEntry.value.tags || [],
      description: newAIEntry.value.description || '',
    })

    ElMessage.success(t('aiManagement.messages.addSuccess'))
    resetForm()
  } catch (error) {
    console.error('Validation failed:', error)
  }
}

const resetForm = () => {
  newAIEntry.value = {
    name: '',
    url: '',
    tags: [],
    description: '',
  }
  if (addFormRef.value) {
    addFormRef.value.clearValidate()
  }
}

const editAIEntry = (entry) => {
  editingEntry.value = { ...entry }
  editDialogVisible.value = true
}

const saveEditedEntry = async () => {
  if (!editFormRef.value || !editingEntry.value) return

  try {
    await editFormRef.value.validate()

    // Validate URL format
    if (!isValidUrl(editingEntry.value.url)) {
      ElMessage.error(t('aiManagement.messages.invalidUrl'))
      return
    }

    // Find the original entry to update
    const originalEntry = aiEntries.value.find((entry) => entry.id === editingEntry.value.id)
    if (originalEntry) {
      updateMyAIItem(originalEntry, editingEntry.value)
      ElMessage.success(t('aiManagement.messages.updateSuccess'))
      editDialogVisible.value = false
      editingEntry.value = null
    }
  } catch (error) {
    console.error('Validation failed:', error)
  }
}

const deleteAIEntry = (entry) => {
  removeFromMyAI(entry)
  ElMessage.success(t('aiManagement.messages.deleteSuccess'))
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return ''
  }
}

const isValidUrl = (string) => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

// Sites management methods
const addNewSite = async () => {
  if (!addSiteFormRef.value) return

  try {
    await addSiteFormRef.value.validate()

    // Validate URL format
    if (!isValidUrl(newSite.value.url)) {
      ElMessage.error('Please enter a valid URL')
      return
    }

    // Check if site already exists
    const existingSite = sitesWithTags.value.find(
      (site) => site.url === newSite.value.url || site.name === newSite.value.name,
    )
    if (existingSite) {
      ElMessage.error('A site with this name or URL already exists')
      return
    }

    // Add the new site
    addSite({
      name: newSite.value.name,
      url: newSite.value.url,
      tags: newSite.value.tags || [],
    })

    ElMessage.success('Site added successfully')
    resetSiteForm()
  } catch (error) {
    console.error('Validation failed:', error)
  }
}

const resetSiteForm = () => {
  newSite.value = {
    name: '',
    url: '',
    tags: [],
  }
  if (addSiteFormRef.value) {
    addSiteFormRef.value.clearValidate()
  }
}

const editSite = (site) => {
  editingSite.value = { ...site }
  sitesEditDialogVisible.value = true
}

const saveEditedSite = async () => {
  if (!editSiteFormRef.value || !editingSite.value) return

  try {
    await editSiteFormRef.value.validate()

    // Validate URL format
    if (!isValidUrl(editingSite.value.url)) {
      ElMessage.error('Please enter a valid URL')
      return
    }

    // Check if another site with same name/URL exists (excluding current site)
    const existingSite = sitesWithTags.value.find(
      (site) =>
        site.id !== editingSite.value.id &&
        (site.url === editingSite.value.url || site.name === editingSite.value.name),
    )
    if (existingSite) {
      ElMessage.error('A site with this name or URL already exists')
      return
    }

    // Find the original site to update
    const originalSite = sitesWithTags.value.find((site) => site.id === editingSite.value.id)
    if (originalSite) {
      updateSite(originalSite, editingSite.value)
      ElMessage.success('Site updated successfully')
      sitesEditDialogVisible.value = false
      editingSite.value = null
    }
  } catch (error) {
    console.error('Validation failed:', error)
  }
}

const deleteSite = (site) => {
  removeSite(site)
  ElMessage.success('Site deleted successfully')
}

// Tag management methods
const addNewTag = () => {
  const tagName = newTagName.value.trim()
  if (!tagName) {
    ElMessage.error('Please enter a tag name')
    return
  }

  if (allTags.value.includes(tagName)) {
    ElMessage.error('This tag already exists')
    return
  }

  addTag(tagName)
  newTagName.value = ''
  ElMessage.success('Tag added successfully')
}

const removeTagWithConfirmation = (tagName) => {
  const usageCount = getTagUsageCount(tagName)
  if (usageCount > 0) {
    ElMessageBox.confirm(
      `This tag is used by ${usageCount} site(s). Removing it will also remove it from all sites. Continue?`,
      'Confirm Tag Removal',
      {
        confirmButtonText: 'Remove',
        cancelButtonText: 'Cancel',
        type: 'warning',
      },
    )
      .then(() => {
        removeTag(tagName)
        ElMessage.success('Tag removed successfully')
      })
      .catch(() => {
        // User cancelled
      })
  } else {
    removeTag(tagName)
    ElMessage.success('Tag removed successfully')
  }
}

const getTagUsageCount = (tagName) => {
  return sitesWithTags.value.filter((site) => site.tags && site.tags.includes(tagName)).length
}

// Lifecycle
onMounted(() => {
  // Component is ready
})
</script>

<style scoped>
.ai-management {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0;
}

.management-tabs {
  width: 100%;
}

.myai-management,
.sites-management,
.tags-management {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.form-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}

.form-header h3,
.table-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.add-ai-form .el-card,
.ai-entries-table .el-card,
.add-site-form .el-card,
.sites-table .el-card,
.add-tag-form .el-card,
.tags-list .el-card {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.add-ai-form .el-form-item,
.add-site-form .el-form-item,
.add-tag-form .el-form-item {
  margin-bottom: 1.5rem;
}

.add-ai-form .el-form-item:last-child,
.add-site-form .el-form-item:last-child,
.add-tag-form .el-form-item:last-child {
  margin-bottom: 0;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 60px;
}

.tag-usage-count {
  margin-left: 4px;
  opacity: 0.7;
  font-size: 0.9em;
}

.empty-tags {
  text-align: center;
  color: var(--el-text-color-secondary);
  padding: 2rem;
}

.el-table {
  border-radius: 6px;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Dark theme styles */
.ai-management.dark-theme .form-header h3,
.ai-management.dark-theme .table-header h3 {
  color: var(--el-text-color-primary);
}

.ai-management.dark-theme .el-card {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

.edit-ai-dialog.dark-theme :deep(.el-dialog),
.edit-site-dialog.dark-theme :deep(.el-dialog) {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .ai-management {
    gap: 1rem;
  }

  .myai-management,
  .sites-management,
  .tags-management {
    gap: 1rem;
  }

  .form-header,
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .table-actions .el-input {
    width: 100% !important;
  }

  .el-table-column {
    min-width: 120px !important;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .add-ai-form .el-form,
  .add-site-form .el-form,
  .add-tag-form .el-form {
    padding: 0;
  }

  .add-ai-form .el-form-item,
  .add-site-form .el-form-item,
  .add-tag-form .el-form-item {
    margin-bottom: 1rem;
  }

  .el-table :deep(.el-table__cell) {
    padding: 8px 4px;
    font-size: 12px;
  }
}
</style>
