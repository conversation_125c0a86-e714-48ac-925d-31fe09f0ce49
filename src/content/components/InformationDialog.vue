<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps(['isOpen', 'linkItem'])
const emit = defineEmits(['closeDialog'])

const { t } = useI18n()

const close = () => emit('closeDialog')
// console.log('props', props)

const customLink = computed(() => {
  if (props.linkItem.link.includes('youtube.com') && !props.linkItem.link.includes('embed/')) {
    return props.linkItem.link.replace('watch?v=', 'embed/')
  }
  return props.linkItem.link
})

// if(customLink.value.includes('youtube.com')){
//   customLink.value = customLink.value.replace('watch?v=', 'embed/')
// }
</script>
<template>
  <el-dialog
    :model-value="props.isOpen"
    :title="props.linkItem.label"
    width="clamp(300px, 90vw, 960px)"
    @close="close"
    destroy-on-close
  >
    <template v-if="props.linkItem.linkType === 'modal'">
      {{ props.linkItem.link }}
    </template>
    <template v-else>
      <iframe
        :src="customLink"
        style="width: 100%; aspect-ratio: 16/9"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowfullscreen
      >
      </iframe>
    </template>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close" :icon="Close">{{ t('btn.cancel') }}</el-button>
        <el-button type="primary" :icon="Check">{{ t('btn.add') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style scoped></style>
