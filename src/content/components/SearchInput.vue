<template>
  <div class="search-autocomplete-wrapper">
    <el-input
      ref="searchInputRef"
      v-model="searchQuery"
      :placeholder="placeholder || t('searchInput.placeholder')"
      class="search-input"
      :size="size"
      clearable
      :prefix-icon="prefixIcon"
      @keydown="handleKeydown"
      @blur="hideAutocomplete"
      @focus="updateAutocomplete(searchQuery)"
      @input="handleInput"
    />

    <!-- Autocomplete Dropdown -->
    <div
      v-if="showAutocomplete"
      ref="autocompleteRef"
      class="autocomplete-dropdown"
      :class="{ 'dark-theme': isDarkTheme }"
    >
      <div
        v-for="(suggestion, index) in autocompleteSuggestions"
        :key="suggestion"
        class="autocomplete-item"
        :class="{ selected: index === selectedSuggestionIndex, 'dark-theme': isDarkTheme }"
        @click="selectSuggestion(suggestion)"
      >
        <el-tag size="small" class="suggestion-tag" :style="getTagStyle(suggestion)">
          {{ suggestion }}
        </el-tag>
        <span class="suggestion-text">{{ suggestion }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue'
import { computed, nextTick, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useTagColors } from '../composables/useTagColors.js'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
  selectedTags: {
    type: Array,
    default: () => [],
  },
  availableTags: {
    type: Array,
    default: () => [],
  },
  size: {
    type: String,
    default: 'large',
    validator: (value) => ['small', 'default', 'large'].includes(value),
  },
  prefixIcon: {
    type: Object,
    default: () => Search,
  },
  debounceDelay: {
    type: Number,
    default: 200,
  },
  maxSuggestions: {
    type: Number,
    default: 8,
  },
  minQueryLength: {
    type: Number,
    default: 2,
  },
})

const emit = defineEmits(['update:modelValue', 'tag-selected', 'search-change'])

const { t } = useI18n()

// Internal state
const searchQuery = ref(props.modelValue)
const showAutocomplete = ref(false)
const autocompleteSuggestions = ref([])
const selectedSuggestionIndex = ref(-1)
const searchInputRef = ref(null)
const autocompleteRef = ref(null)
let debounceTimer = null

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== searchQuery.value) {
      searchQuery.value = newValue
    }
  },
)

// Watch search query for autocomplete
watch(searchQuery, (newQuery) => {
  updateAutocomplete(newQuery)
})

// Tag styling
const getTagStyle = (tag) => {
  const { getTagStyle } = useTagColors(props.isDarkTheme)
  const isSelected = props.selectedTags.includes(tag)
  return getTagStyle(tag, isSelected)
}

// Autocomplete functionality
const getTagSuggestions = (query) => {
  if (!query || query.trim().length < props.minQueryLength) {
    return []
  }

  const lowerQuery = query.trim().toLowerCase()

  // Get all unique tags from availableTags prop
  const tagArray = [...new Set(props.availableTags)]

  // Priority 1: Tags that start with the query (prefix matching)
  const prefixMatches = tagArray.filter((tag) => tag.toLowerCase().startsWith(lowerQuery))

  // Priority 2: Tags that contain the query (partial matching)
  const partialMatches = tagArray.filter(
    (tag) => tag.toLowerCase().includes(lowerQuery) && !tag.toLowerCase().startsWith(lowerQuery),
  )

  // Combine and limit results
  const allMatches = [...prefixMatches, ...partialMatches]
  return allMatches.slice(0, props.maxSuggestions)
}

const updateAutocomplete = (query) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  debounceTimer = setTimeout(() => {
    const suggestions = getTagSuggestions(query)
    autocompleteSuggestions.value = suggestions
    showAutocomplete.value = suggestions.length > 0 && query.length >= props.minQueryLength
    selectedSuggestionIndex.value = -1
  }, props.debounceDelay)
}

const selectSuggestion = (tag) => {
  // Validate tag before adding
  if (!tag || typeof tag !== 'string' || tag.trim() === '') {
    return
  }

  // Emit tag selection event
  emit('tag-selected', tag)

  // Clear the search input
  searchQuery.value = ''
  showAutocomplete.value = false
  selectedSuggestionIndex.value = -1

  // Focus back to input for better UX
  nextTick(() => {
    if (searchInputRef.value) {
      searchInputRef.value.focus()
    }
  })
}

const handleKeydown = (event) => {
  if (!showAutocomplete.value || autocompleteSuggestions.value.length === 0) {
    return
  }

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.min(
        selectedSuggestionIndex.value + 1,
        autocompleteSuggestions.value.length - 1,
      )
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedSuggestionIndex.value = Math.max(selectedSuggestionIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (selectedSuggestionIndex.value >= 0) {
        selectSuggestion(autocompleteSuggestions.value[selectedSuggestionIndex.value])
      }
      break
    case 'Escape':
      showAutocomplete.value = false
      selectedSuggestionIndex.value = -1
      break
  }
}

const hideAutocomplete = () => {
  // Small delay to allow click events on suggestions
  setTimeout(() => {
    showAutocomplete.value = false
    selectedSuggestionIndex.value = -1
  }, 150)
}

const handleInput = () => {
  // Emit the updated value
  emit('update:modelValue', searchQuery.value)
  emit('search-change', searchQuery.value)
}

// Expose focus method for parent components
const focus = () => {
  if (searchInputRef.value) {
    searchInputRef.value.focus()
  }
}

defineExpose({
  focus,
})
</script>

<style scoped>
.search-autocomplete-wrapper {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
}

/* Autocomplete Styles */
.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.autocomplete-dropdown.dark-theme {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.autocomplete-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
  background-color: var(--el-color-primary-light-9);
}

.autocomplete-item.dark-theme:hover,
.autocomplete-item.dark-theme.selected {
  background-color: var(--el-color-primary-dark-2);
}

.suggestion-tag {
  flex-shrink: 0;
}

.suggestion-text {
  color: var(--el-text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Autocomplete scrollbar styling */
.autocomplete-dropdown::-webkit-scrollbar {
  width: 6px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  /* Autocomplete mobile styles */
  .autocomplete-dropdown {
    max-height: 200px;
    width: 100%;
    left: 0;
    right: 0;
  }

  .autocomplete-item {
    padding: 0.75rem;
    gap: 0.5rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .suggestion-text {
    font-size: 0.9rem;
  }

  .suggestion-tag {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .search-input {
    font-size: 1rem;
    min-height: 48px;
  }

  /* Autocomplete for very small screens */
  .autocomplete-item {
    padding: 1rem;
    min-height: 48px;
  }

  .suggestion-text {
    font-size: 1rem;
  }
}
</style>
