<template>
  <el-card class="my-ai-favorites-card" shadow="hover" :body-style="{ padding: '1.5rem' }">
    <!-- Card Header -->
    <template #header>
      <div class="favorites-header-content">
        <div class="favorites-title">
          <el-text size="large" tag="h3" :type="isDarkTheme ? 'primary' : 'default'">
            <span v-if="isFilteringActive">
              {{ t('myAIFavorites.title.withTags') }} ({{ displayedCount }})
            </span>
            <span v-else> {{ t('myAIFavorites.title.default') }} ({{ displayedCount }}) </span>
          </el-text>
          <el-text v-if="isEditMode" size="small" type="info" class="edit-mode-info">
            {{ t('myAIFavorites.editMode.info') }}
          </el-text>
          <el-text v-if="isFilteringActive" size="small" type="warning" class="filter-mode-info">
            {{ t('myAIFavorites.filtering.info') }} {{ selectedTags.join(', ') }}
          </el-text>
        </div>
        <div class="header-actions">
          <el-tooltip
            v-if="isFilteringActive"
            :content="t('myAIFavorites.tooltips.clearFilters')"
            placement="top"
          >
            <el-button @click="clearFilters" type="warning" size="small" plain>
              {{ t('myAIFavorites.actions.clearFilters') }}
            </el-button>
          </el-tooltip>
          <el-tooltip
            :content="
              viewMode === 'card'
                ? t('sitesGrid.viewToggle.switchToList')
                : t('sitesGrid.viewToggle.switchToCard')
            "
            placement="top"
          >
            <el-button
              @click="toggleViewMode"
              class="view-toggle-btn"
              size="small"
              :icon="viewMode === 'card' ? List : Grid"
              circle
            />
          </el-tooltip>
          <el-tooltip
            :content="
              isEditMode
                ? t('myAIFavorites.tooltips.finishEdit')
                : t('myAIFavorites.tooltips.editMode')
            "
            placement="top"
          >
            <el-button
              @click="toggleEditMode"
              :type="isEditMode ? 'primary' : 'default'"
              size="small"
              :icon="Setting"
              circle
            />
          </el-tooltip>
        </div>
      </div>
    </template>

    <!-- Favorites Content -->
    <div v-if="displayedSites.length > 0" class="favorites-content">
      <!-- Card View -->
      <div
        v-if="viewMode === 'card'"
        class="sites-grid"
        :class="[gridColsClass, { 'drag-enabled': isEditMode }]"
      >
        <SiteCard
          v-for="(site, index) in displayedSites"
          :key="site.id || site.url"
          :site="site"
          :isDarkTheme="isDarkTheme"
          :selectedTags="selectedTags"
          :showConfigIcon="isEditMode"
          :showRemoveIcon="isEditMode"
          :showDragHandle="isEditMode && !isFilteringActive"
          :isSearchTab="false"
          @click="handleCardClick"
          @favorite-changed="handleFavoriteChanged"
          @config-click="handleConfigClick"
          @tag-selected="handleTagSelected"
          @dragstart="handleDragStart($event, index)"
          @dragover="handleDragOver"
          @drop="handleDrop($event, index)"
        />
      </div>

      <!-- List View -->
      <div v-else class="sites-list">
        <SiteList
          v-for="(site, index) in displayedSites"
          :key="site.id || site.url"
          :site="site"
          :isDarkTheme="isDarkTheme"
          :selectedTags="selectedTags"
          :showConfigIcon="isEditMode"
          :showRemoveIcon="isEditMode"
          :showDragHandle="isEditMode && !isFilteringActive"
          :isSearchTab="false"
          @click="handleCardClick"
          @favorite-changed="handleFavoriteChanged"
          @config-click="handleConfigClick"
          @tag-selected="handleTagSelected"
          @dragstart="handleDragStart($event, index)"
          @dragover="handleDragOver"
          @drop="handleDrop($event, index)"
        />
      </div>
    </div>

    <!-- Empty State -->
    <el-empty v-else description="" class="my-ai-empty">
      <template #image>
        <el-icon
          :size="64"
          :color="isFilteringActive ? 'var(--el-color-info)' : 'var(--el-color-warning)'"
        >
          <Star />
        </el-icon>
      </template>
      <template #description>
        <el-text v-if="isFilteringActive" size="large">{{
          t('myAIFavorites.empty.withTags')
        }}</el-text>
        <el-text v-else size="large">{{ t('myAIFavorites.empty.default') }}</el-text>
        <br />
        <el-text v-if="isFilteringActive" type="info">
          {{ t('myAIFavorites.empty.noTagsMessage') }} {{ selectedTags.join(', ') }}
        </el-text>
        <el-text v-else type="info">
          {{ t('myAIFavorites.empty.addFavoritesMessage') }}
        </el-text>
      </template>
      <el-button v-if="isFilteringActive" type="warning" @click="clearFilters">
        {{ t('myAIFavorites.actions.clearFilters') }}
      </el-button>
      <el-button v-else type="primary" @click="$emit('browse-tools')">
        {{ t('myAIFavorites.actions.browseTools') }}
      </el-button>
    </el-empty>

    <!-- Config Modal -->
    <el-dialog
      v-model="showConfigModal"
      :title="t('myAIFavorites.config.title')"
      width="500px"
      :before-close="closeConfigModal"
      append-to-body
    >
      <el-form v-if="editingSite" label-width="80px" label-position="left">
        <el-form-item :label="t('myAIFavorites.config.name')">
          <el-input
            v-model="editingSite.name"
            :placeholder="t('myAIFavorites.config.namePlaceholder')"
            clearable
          />
        </el-form-item>

        <el-form-item :label="t('myAIFavorites.config.url')">
          <el-input
            v-model="editingSite.url"
            :placeholder="t('myAIFavorites.config.urlPlaceholder')"
            clearable
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeConfigModal">{{ t('btn.cancel') }}</el-button>
          <el-button type="primary" @click="saveConfig">{{ t('btn.save') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { Grid, List, Setting, Star } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { useMyAISettings } from '../composables/useMyAISettings.js'
import SiteCard from './SiteCard.vue'
import SiteList from './SiteList.vue'

const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
  viewMode: {
    type: String,
    required: true,
    validator: (value) => ['card', 'list'].includes(value),
  },
  selectedTags: {
    type: Array,
    default: () => [],
  },
  gridColsClass: {
    type: String,
    default: 'grid-cols-4',
  },
})

const emit = defineEmits([
  'item-click',
  'favorite-changed',
  'browse-tools',
  'tag-selected',
  'clear-filters',
  'toggle-view-mode',
])

const { t } = useI18n()

// My AI Settings
const { myAI, myAICount, updateMyAIItem, reorderMyAI } = useMyAISettings()

// Determine if we're in filtering mode
const isFilteringActive = computed(() => props.selectedTags.length > 0)

// Display sites based on filtering state
const displayedSites = computed(() => {
  if (!isFilteringActive.value) {
    // No tags selected - show all favorites in their original order
    return myAI.value
  }

  // Tags selected - filter only within favorited tools
  let filtered = myAI.value.filter((site) =>
    props.selectedTags.some((tag) => site.tags.includes(tag)),
  )

  // Sort by selected tags priority within the favorited tools
  if (props.selectedTags.length > 0) {
    filtered.sort((a, b) => {
      const getTagScore = (site) => {
        let score = 0
        props.selectedTags.forEach((selectedTag, index) => {
          const tagPosition = site.tags.indexOf(selectedTag)
          if (tagPosition !== -1) {
            score += (10 - tagPosition) * (props.selectedTags.length - index)
          }
        })
        return score
      }
      return getTagScore(b) - getTagScore(a)
    })
  }

  return filtered
})

// Count for display
const displayedCount = computed(() => {
  if (isFilteringActive.value) {
    return displayedSites.value.length
  }
  return myAICount.value
})

// Edit mode state
const isEditMode = ref(false)
const showConfigModal = ref(false)
const editingSite = ref(null)
const draggedIndex = ref(null)

// Edit mode functionality
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value
}

// Configuration modal
const handleConfigClick = (site) => {
  editingSite.value = { ...site, originalSite: site }
  showConfigModal.value = true
}

const closeConfigModal = () => {
  showConfigModal.value = false
  editingSite.value = null
}

const saveConfig = () => {
  if (editingSite.value && updateMyAIItem) {
    // Pass the original item (with ID) instead of just the URL
    const originalItem = editingSite.value.originalSite || editingSite.value
    updateMyAIItem(originalItem, editingSite.value)
  }
  closeConfigModal()
}

// Event handlers
const handleCardClick = (url) => {
  if (!isEditMode.value) {
    // Open URL in new tab, following the same pattern as SiteCard and SiteList components
    window.open(url, '_blank')
    // Also emit the event for any parent component that might need it
    emit('item-click', url)
  }
}

const handleFavoriteChanged = (wasAdded) => {
  emit('favorite-changed', wasAdded, false) // false indicates not from search tab
}

const handleTagSelected = (tag) => {
  emit('tag-selected', tag)
}

const clearFilters = () => {
  emit('clear-filters')
}

const toggleViewMode = () => {
  emit('toggle-view-mode')
}

// Drag and drop functionality
const handleDragStart = (event, index) => {
  draggedIndex.value = index
  event.dataTransfer.effectAllowed = 'move'
}

const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
}

const handleDrop = (event, dropIndex) => {
  event.preventDefault()

  if (draggedIndex.value === null || draggedIndex.value === dropIndex) {
    return
  }

  // Reorder favorites
  if (reorderMyAI) {
    reorderMyAI(draggedIndex.value, dropIndex)
  }

  draggedIndex.value = null
}
</script>

<style scoped>
.my-ai-favorites-card {
  width: 100%;
  transition: all 0.2s ease;
}

.favorites-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.favorites-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Ensure consistent button sizing across all action buttons */
.header-actions .el-button {
  flex-shrink: 0;
  transition: all 0.2s ease;
}

/* Hover effect for all action buttons */
.header-actions .el-button:hover {
  transform: scale(1.05);
}

.edit-mode-info,
.filter-mode-info {
  margin-top: 0.25rem;
}

.filter-mode-info {
  color: var(--el-color-warning);
  font-weight: 500;
}

.favorites-content {
  width: 100%;
}

.sites-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

/* Default grid layout (4 columns) */
.sites-grid {
  grid-template-columns: repeat(4, 1fr);
}

/* Side panel visible - use fewer columns for better fit */
.sites-grid.side-panel-visible {
  grid-template-columns: repeat(2, 1fr);
}

/* Side panel hidden - use more columns to utilize space */
.sites-grid.side-panel-hidden {
  grid-template-columns: repeat(4, 1fr);
}

.sites-grid.drag-enabled {
  user-select: none;
}

.sites-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.my-ai-empty {
  padding: 2rem 1rem;
}

/* Element Plus dialog footer spacing */
.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Responsive grid layout */
@media (max-width: 1024px) {
  .sites-grid.side-panel-visible {
    grid-template-columns: repeat(2, 1fr);
  }

  .sites-grid.side-panel-hidden {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .sites-grid.side-panel-visible {
    grid-template-columns: 1fr;
  }

  .sites-grid.side-panel-hidden {
    grid-template-columns: repeat(2, 1fr);
  }

  .favorites-header-content {
    flex-direction: row;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .favorites-title {
    flex: 1;
    min-width: 0;
  }

  .header-actions {
    flex-shrink: 0;
    gap: 0.5rem;
  }

  /* Consistent touch-friendly sizing for all action buttons */
  .header-actions .el-button {
    min-height: 44px;
    min-width: 44px;
  }
}

@media (max-width: 480px) {
  .sites-grid.side-panel-visible,
  .sites-grid.side-panel-hidden {
    grid-template-columns: 1fr;
  }

  .my-ai-favorites-card {
    margin: 0;
  }

  .favorites-header-content {
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: nowrap;
  }

  .favorites-title {
    flex: 1;
    min-width: 0;
  }

  .header-actions {
    flex-shrink: 0;
    gap: 0.5rem;
  }

  /* Larger touch-friendly sizing for all action buttons on small screens */
  .header-actions .el-button {
    min-height: 48px;
    min-width: 48px;
  }
}
</style>
