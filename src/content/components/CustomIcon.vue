<script setup>
const props = defineProps(['name', 'size', 'colorClass', 'colorClassDark'])

const defaultSize = props.size
  ? props.size.includes('px') || props.size.includes('em') || props.size.includes('rem')
    ? props.size
    : props.size + 'px'
  : '1em'
const defaultColor = props.colorClass
const defaultColorDark = props.colorClassDark

const getImage = () => {
  if (props.name && props.name.includes('PromptLab')) {
    return `${chrome.runtime.getURL('lab/' + props.name)}`
  }
  if (props.name) {
    return `${chrome.runtime.getURL('icons/' + props.name)}`
  }
}
</script>
<template>
  <div
    v-if="props.name"
    :style="{
      width: defaultSize,
      height: defaultSize,
      webkitMaskImage: 'url(' + getImage() + ')',
      maskImage: 'url(' + getImage() + ')',
    }"
    :class="{
      defaultColor: defaultColor !== undefined,
      defaultColorDark: defaultColorDark !== undefined,
    }"
    class="maskImage"
  ></div>
</template>
<style scoped>
.maskImage {
  --color: var(--el-button-text-color, var(--el-text-color-primary));
  display: inline-block;
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  aspect-ratio: 1/1;
  height: 100%;
  background-color: var(--color);
  transition: inherit;
}
.maskImage + * {
  margin-right: 0.5em;
}
* + .maskImage {
  margin-right: 0.5em;
}
.maskImage:hover {
  --color: var(--el-text-color-primary);
}
button:hover .maskImage {
  --color: var(--el-button-hover-text-color);
}
</style>
