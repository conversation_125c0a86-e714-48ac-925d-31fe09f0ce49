<template>
  <Transition name="menu-buttons" mode="out-in">
    <div v-show="mainAppVisible" class="menu-hide-buttons" :class="{ 'dark-theme': isDarkTheme }">
      <button
        @click="$emit('changeMenuHideLevel', -1)"
        :class="['control-button', 'menu-button']"
        :disabled="menuHideLevel === 0"
        :title="t('menuButtons.show.tooltip')"
        data-testid="button-show-menu"
      >
        <el-icon class="button-icon arrow-up">
          <ArrowUpBold />
        </el-icon>
      </button>
      <button
        @click="$emit('changeMenuHideLevel', 1)"
        :class="['control-button', 'menu-button']"
        :disabled="menuHideLevel === 2"
        :title="t('menuButtons.hide.tooltip')"
        data-testid="button-hide-menu"
      >
        <el-icon class="button-icon arrow-down">
          <ArrowDownBold />
        </el-icon>
      </button>
    </div>
  </Transition>
</template>

<script setup>
import { ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps({
  mainAppVisible: Boolean,
  menuHideLevel: Number,
  isDarkTheme: Boolean,
})

defineEmits(['changeMenuHideLevel'])
</script>

<style scoped>
.menu-hide-buttons {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 6px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 6px;
}

.control-button {
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: visible;
}

.control-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #6b7280;
  transform: rotate(2deg);
  box-shadow:
    0 4px 15px rgba(107, 114, 128, 0.3),
    0 0 20px rgba(107, 114, 128, 0.1);
}

.control-button:hover::before {
  left: 100%;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-button:disabled:hover {
  transform: none;
  box-shadow: none;
  background: rgba(255, 255, 255, 0.8);
  border-color: #e5e7eb;
}

.button-icon {
  font-size: 10px;
  color: #374151;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dark-theme .button-icon {
  color: #d1d5db;
}

.dark-theme .control-button {
  background: rgba(64, 64, 64, 0.8);
  border-color: rgba(115, 115, 115, 0.6);
}

.dark-theme .control-button:hover {
  background: rgba(75, 75, 75, 1);
  border-color: #9ca3af;
}

.button-icon svg {
  width: 10px;
  height: 10px;
  transition: inherit;
}

.button-icon.arrow-up:hover {
  animation: bounceUp 0.6s ease-in-out;
}

.button-icon.arrow-down:hover {
  animation: bounceDown 0.6s ease-in-out;
}

.control-button.menu-button:active .button-icon.arrow-up {
  animation: quickBounceUp 0.2s ease-out;
}

.control-button.menu-button:active .button-icon.arrow-down {
  animation: quickBounceDown 0.2s ease-out;
}

@keyframes bounceUp {
  0%,
  100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-4px) scale(1.1);
  }
  50% {
    transform: translateY(-2px);
  }
  75% {
    transform: translateY(-3px) scale(1.05);
  }
}

@keyframes bounceDown {
  0%,
  100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(4px) scale(1.1);
  }
  50% {
    transform: translateY(2px);
  }
  75% {
    transform: translateY(3px) scale(1.05);
  }
}

@keyframes quickBounceUp {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px) scale(1.2);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes quickBounceDown {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(6px) scale(1.2);
  }
  100% {
    transform: translateY(0);
  }
}

.menu-buttons-enter-active {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.menu-buttons-leave-active {
  transition: all 0.3s ease-out;
}

.menu-buttons-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.menu-buttons-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.95);
}

.menu-buttons-enter-to,
.menu-buttons-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
</style>
