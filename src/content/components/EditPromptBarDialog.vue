<script setup>
import { Check, Close, Loading, Refresh } from '@element-plus/icons-vue'
import { ElInput, ElMessage, ElNotification } from 'element-plus'
import { doc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser, useDocument } from 'vuefire'

import Analytics from '../../Analytics'
import { useAppStateStore } from '../../stores/appState'
import { useDebounceFn } from '../composables/useDebounce'
import { usePromptBarManagement } from '../composables/usePromptBarManagement'
import {
  barsRef,
  editBar,
  getCopyToUserList,
  getUserBars,
  isAdminOrSuperAdmin,
  syncBarProperty,
  syncBarWithLibraryBars,
  syncBarWithPopularBars,
  syncBarWithPublicBars,
  syncBarWithTeamBars,
} from '../firebase'
import { updateMyPromptsBarKeyInLocalStorage } from '../localStorage'
import { avaibleSitesUrlAndName } from '../sites_index'
import { convertIdToString, createSharePage, deleteShared, SHARE_LINK_BASE } from '../utils'
import EditPromptCopyTab from './EditPromptCopyTab.vue'
import EditPromptTree from './EditPromptTree.vue'
import EditTags from './EditTags.vue'
import LanguagePromptBarSelector from './LanguagePromptBarSelector.vue'
import PromptBarTranslationTab from './PromptBarTranslationTab.vue'
import SelectIconPopover from './SelectIconPopover.vue'
import TeamDropdown from './TeamDropdown.vue'
import Tooltip from './Tooltip.vue'

const message = (setContent, setType = 'info') => {
  ElMessage.closeAll()
  ElMessage({
    message: setContent,
    type: setType,
  })
}
const { t } = useI18n()

const props = defineProps([
  'isOpen',
  'areBarsLoaded',
  'userBars',
  'bar',
  'role',
  'isAdmin',
  'isPromptBarPublic',
  'isPromptBarListed',
  'isPromptBarEditable',
  'initialUrl',
  'isPromptBookmark',
])
// console.log('EditPromptBarDialog props: ', props)
const emit = defineEmits(['closeDialog', 'openShare', 'updateBars'])

const appState = useAppStateStore()
const { userDoc, promptBarLanguage, isGeneratingPromptName, nameForGeneratedPrompt } =
  storeToRefs(appState)
const { setEditTab } = usePromptBarManagement()

const EditTABS = {
  promptsTab: t('editPromptBar.tabs.promptsTab'),
  linksTab: t('editPromptBar.tabs.linksTab'),
  copyTab: t('editPromptBar.tabs.copyTab'),
  translationTab: t('editPromptBar.tabs.translationTab'),
}

const CollapseName = {
  barPrompts: t('editPromptBar.collapse.barPrompts'),
  barDesc: t('editPromptBar.collapse.barDesc'),
  barRawJson: t('editPromptBar.collapse.barRawJson'),
  barChatLink: t('editPromptBar.collapse.barChatLink'),
  barLinks: t('editPromptBar.collapse.barLinks'),
  folderChatLink: t('editPromptBar.collapse.folderChatLink'),
  promptContents: t('editPromptBar.collapse.promptContents'),
  promptDesc: t('editPromptBar.collapse.promptDesc'),
  promptChatLink: t('editPromptBar.collapse.promptChatLink'),
}

const clearWhSp = (str) => {
  return str.replace(/\s+/g, '').trim()
}

const user = useCurrentUser()

const isAdmin = ref(props.isAdmin)

const type = ref(['prompt', 'link'])
const currentType = ref('')

const dynamicTags = ref([])
const InputRef = ref(null)
const isPublic = ref(props.bar.isPublic ?? false)
const isPopular = ref(props.bar.isPopular ?? false)
const isListed = ref(props.bar.isListed ?? false)
const isEditable = ref(props.bar.isEditable ?? false)
const isInLibrary = ref(props.bar.isInLibrary ?? false)
const isPremium = ref(false)
const invalidInput = ref(false)

const promptBarName = ref(props.bar.name)
const promptBarNameError = ref('')
const promptBarNameBlur = ref(false)
const promptText = ref('')
const promptDesc = ref()

const linkText = ref()
const description = ref('')
const rawjson = ref('')
// const value = ref(null)
const isPromptBarNameEditModeEnabled = ref(false)
const promptBarNameInput = ref(null)

const selectedPromptData = ref(null)
watch(selectedPromptData, (oldValue, newValue) => {
  // console.log('watch selectedPromptData: ', oldValue, newValue)
})
const selectedPromptNode = ref(null)

const linkType = ref('')
// const isAdmin = ref(props.role === 'Admin' || props.role === 'Super Admin' ? true : false)
const showPromptEdit = ref(false)
const showLinkEdit = ref(false)
const showFolderEdit = ref(false)
const selectedFolderData = ref(null)
watch(selectedFolderData, () => {
  // console.log('watch selectedFolderData: ', selectedFolderData.value)
})
const selectedFolderNode = ref(null)

const setFolderPremium = ref(false)

const activeTabName = EditTABS[setEditTab.value] || EditTABS.promptsTab

const isBookmark = ref(props.isPromptBookmark || false)

const activeEditTab = ref(clearWhSp(activeTabName))
const activeAccordionBarPrompts = ref(clearWhSp(CollapseName.barPrompts))
const activeAccordionBarLinks = ref(clearWhSp(CollapseName.barLinks))

const activeAccordionFolderEdit = ref(clearWhSp(CollapseName.folderChatLink))
const activeAccordionPromptEdit = ref(getActiveAccordionPromptEdit())

function getActiveAccordionPromptEdit() {
  if (isBookmark.value) {
    // console.log('activeAccordionPromptEdit: ', clearWhSp(CollapseName.promptChatLink))
    return [clearWhSp(CollapseName.promptChatLink), clearWhSp(CollapseName.promptDesc)]
  } else {
    // console.log('activeAccordionPromptEdit: ', clearWhSp(CollapseName.promptContents))
    return clearWhSp(CollapseName.promptContents)
  }
}

const chatLinkName = 'destination'
const barChatLinkRef = ref('')
// const barChatLink = ref(props.initialUrl || props.bar[chatLinkName] || '')
const barChatLink = ref(props.bar[chatLinkName] || '')
const folderChatLinkRef = ref('')
const folderChatLink = ref('')
const folderDefaultChatLink = ref(barChatLink.value || '')
const promptChatLinkRef = ref('')
const promptChatLink = ref('')
const promptDefaultChatLink = ref(barChatLink.value || '')

const customChatLink = 'Custom'
const defaultChatLink = 'Default'

// destination select options
const chatLinkSelect = computed(() => {
  return [
    { name: defaultChatLink, url: defaultChatLink },
    { name: customChatLink, url: customChatLink },
    ...avaibleSitesUrlAndName,
  ]
})

// destination select options for nodes
const nodesChatLinkSelect = computed(() => {
  return [
    { name: defaultChatLink, url: defaultChatLink },
    { name: customChatLink, url: customChatLink },
    ...avaibleSitesUrlAndName,
  ]
})

const shareLink = ref(SHARE_LINK_BASE + props.bar.id)
const shareImg = ref('')
const doSharePageUpdate = ref(true)
const hasCreatedSharePage = ref(false)

const isChatLinkOnList = (url) => {
  let findURL = avaibleSitesUrlAndName.find((item) => item.url === url)
  if (findURL && url !== '') return true
  return false
}
const checkAndSetChatLinkSelected = (url) => {
  if (url) {
    if (isChatLinkOnList(url)) {
      return url
    }
    return customChatLink
  }
  return defaultChatLink
}
// console.log('isChatLinkOnList', isChatLinkOnList(barChatLink.value))
const barChatLinkSelectRef = ref(null)
const barChatLinkSelected = ref(
  isChatLinkOnList(barChatLink.value)
    ? barChatLink.value
    : barChatLink.value
    ? customChatLink
    : defaultChatLink,
)
const folderChatLinkSelectRef = ref(null)
const folderChatLinkSelected = ref(checkAndSetChatLinkSelected(folderChatLink.value))
const promptChatLinkSelectRef = ref(null)
const promptChatLinkSelected = ref(checkAndSetChatLinkSelected(promptChatLink.value))

const selectedTeams = ref([])

// Keep selectedTeams in sync with available teams
const availableTeamIds = computed(() => (userDoc.value?.teams || []).map((team) => team.id))

watch(
  [selectedTeams, availableTeamIds],
  ([newSelected, newAvailable]) => {
    const filtered = newSelected.filter((id) => newAvailable.includes(id))
    if (filtered.length !== newSelected.length) {
      selectedTeams.value = filtered
    }
  },
  { immediate: true },
)

watch(
  selectedTeams,
  async (newTeams, oldTeams) => {
    if (!props.bar || !user.value || !oldTeams) return
    const added = newTeams.filter((id) => !oldTeams.includes(id))
    const removed = oldTeams.filter((id) => !newTeams.includes(id))
    if (added.length === 0 && removed.length === 0) return

    let barTeams = Array.isArray(props.bar.teams) ? [...props.bar.teams] : []
    for (const teamId of added) {
      await syncBarWithTeamBars({ bar: props.bar, teamId, shouldRemove: false })
      if (!barTeams.includes(teamId)) barTeams.push(teamId)

      // Refresh team bars for the added team to ensure immediate visibility
      if (appState.refreshTeamBars.value && typeof appState.refreshTeamBars.value === 'function') {
        await appState.refreshTeamBars.value(teamId)
      }
    }
    for (const teamId of removed) {
      await syncBarWithTeamBars({ bar: props.bar, teamId, shouldRemove: true })
      barTeams = barTeams.filter((id) => id !== teamId)

      // Refresh team bars for the removed team to ensure immediate update
      if (appState.refreshTeamBars.value && typeof appState.refreshTeamBars.value === 'function') {
        await appState.refreshTeamBars.value(teamId)
      }
    }
    props.bar.teams = barTeams
    await editBar(props.bar, { teams: barTeams })
  },
  { immediate: false },
)

onMounted(() => {
  if (props.bar) {
    dynamicTags.value = props.bar.tags || []
    description.value = props.bar.description || ''
    rawjson.value = props.bar.prompt_menu || JSON.stringify(props.bar.promptMenu) || ''
    if (Array.isArray(props.bar.teams) && props.bar.teams.length > 0) {
      selectedTeams.value = props.bar.teams.map((team) => team.id || team)
    }
  }
})

const close = () => {
  emit('closeDialog')
}

function isOwner() {
  if (!props.bar.owner?.id || !user.value?.uid) return false
  return props.bar.owner.id === user.value.uid
}

function updateBarPropertyInUserBars(bar, property) {
  if (bar.owner?.id !== user.value?.uid) return
  if (!bar || !property) return
  props.userBars.find((userBar) => userBar.id === bar.id)[property] = bar[property]
}

const togglePromptBarNameEditMode = async (shouldBeEnabled, cancelEdit) => {
  isPromptBarNameEditModeEnabled.value = shouldBeEnabled
  if (isPromptBarNameEditModeEnabled.value) {
    setTimeout(() => {
      promptBarNameInput.value.select()
    }, 100)
  }
  if (!isPromptBarNameEditModeEnabled.value && !cancelEdit) {
    if (promptBarName.value === props.bar.name) {
      promptBarNameError.value = ''
      return
    }

    if (promptBarName.value.length < 2) {
      promptBarNameError.value = 'Name must be at least 2 characters'
      isPromptBarNameEditModeEnabled.value = true
      return
    }

    const userId = user.value.uid
    const userBars = (props.areBarsLoaded.value ? props.userBars : await getUserBars(userId)) ?? []
    const barNames = userBars.map((bar) => bar.name) ?? []

    if (barNames.includes(promptBarName.value)) {
      promptBarNameError.value = 'Name already exists'
      isPromptBarNameEditModeEnabled.value = true
      return
    }

    promptBarNameError.value = ''

    props.bar.name = promptBarName.value

    updateBarPropertyInUserBars(props.bar, 'name')

    await syncBarProperty(props.bar, 'name')

    await editBar(props.bar, {
      name: props.bar.name,
    })

    updateMyPromptsBarKeyInLocalStorage('name', props.bar.name)

    await createSharePageHandler()
  } else {
    promptBarName.value = props.bar.name
    promptBarNameError.value = ''
  }
}

const openEditTextarea = (node, type, removed = false) => {
  // console.log('openEditTextarea: ', node, node?.data, 'type: ' + type, props.bar)
  if (removed) {
    showPromptEdit.value = false
    showLinkEdit.value = false
    isBookmark.value = false
    return
  }
  if (!node) {
    return
  }
  // if folder
  if (node && node.data && node.data.children) {
    showPromptEdit.value = false
    showLinkEdit.value = false
    return
  }
  showFolderEdit.value = false

  selectedPromptNode.value = node ? node : null
  selectedPromptData.value = node ? node.data : null
  currentType.value = type

  activeAccordionPromptEdit.value = clearWhSp(CollapseName.promptContents)

  let newData = JSON.parse(JSON.stringify(props.bar[type + 'Menu']))
  let nodeToFind = findNode(newData, node ? node.data.id : null)
  if (type === 'prompt') {
    showPromptEdit.value = true
    showLinkEdit.value = false
    promptText.value = nodeToFind ? nodeToFind.prompt : ''
    promptDesc.value = nodeToFind ? nodeToFind.description : ''
    isPremium.value = nodeToFind ? nodeToFind.premium : false
    promptChatLink.value = node ? node.data[chatLinkName] || '' : ''
    promptChatLinkSelected.value = checkAndSetChatLinkSelected(promptChatLink.value)
    promptDefaultChatLink.value = barChatLink.value

    if (findParentWithChatLink(node && node.parent)) {
      // console.log('findParentWithChatLink data: ', findParentWithChatLink(node.parent).data)
      promptDefaultChatLink.value =
        findParentWithChatLink(node.parent).data[chatLinkName] || barChatLink.value
    }

    nextTick(() => {
      isBookmark.value = nodeToFind && nodeToFind.isBookmark ? true : false
      // console.log('isBookmark: ', isBookmark.value)
      activeAccordionPromptEdit.value = getActiveAccordionPromptEdit()
      // console.log('activeAccordionPromptEdit: ', activeAccordionPromptEdit.value)
    })
  } else if (type === 'link') {
    showPromptEdit.value = false
    showLinkEdit.value = true
    linkType.value = node.data.linkType
    linkText.value = nodeToFind ? nodeToFind.link : ''
    invalidInput.value = false
  }
}

const openEditFolder = (node, removed = false) => {
  // console.log('openEditFolder: ', node)
  if (removed) {
    // console.log('openEditFolder removed: ', node)
    showFolderEdit.value = false
    return
  }
  if (!node?.data?.children) {
    // console.log('openEditFolder no children: ', node)
    // showFolderEdit.value = false
    // return
  }
  showPromptEdit.value = false
  showLinkEdit.value = false

  showFolderEdit.value = true
  if (!node) return
  selectedFolderNode.value = node
  selectedFolderData.value = node.data
  folderChatLink.value = node.data[chatLinkName]
  folderChatLinkSelected.value = checkAndSetChatLinkSelected(folderChatLink.value)

  folderDefaultChatLink.value = barChatLink.value

  if (findParentWithChatLink(node.parent)) {
    // console.log('findParentWithChatLink data: ', findParentWithChatLink(node.parent).data)
    folderDefaultChatLink.value =
      findParentWithChatLink(node.parent).data[chatLinkName] || barChatLink.value
  }
  // findParentWithChatLink(node.parent)
}

let promptsCount = 0
function setPremium(dataValue) {
  // console.log('setPremium: ', dataValue)
  for (let node of dataValue) {
    if (node.children) {
      setPremium(node.children)
    } else {
      if (promptsCount === 0) {
        setFolderPremium.value = !node.premium
      }
      // console.log('setFolderAsPremium: ', node)
      node.premium = setFolderPremium.value
      promptsCount += 1
    }
  }
  return promptsCount
}

const setFolderAsPremium = async () => {
  // console.log('setFolderAsPremium: ', selectedFolderData.value)
  setFolderPremium.value = !setFolderPremium.value
  // selectedFolderData.value = data
  if (setPremium(selectedFolderData.value.children) > 0) {
    // Update the local state and the Firestore
    let newData = props.bar['promptMenu']
    props.bar.prompt_menu = JSON.stringify(newData)
    // return
    updateBarPropertyInUserBars(props.bar, 'promptMenu')
    updateBarPropertyInUserBars(props.bar, 'prompt_menu')

    await editBar(props.bar, {
      prompt_menu: props.bar.prompt_menu,
    })
    await syncBarProperty(props.bar, 'prompt_menu')
    updateMyPromptsBarKeyInLocalStorage('promptMenu', newData)
  }
}

const isValidURL = (url) => {
  // console.log('isValidURL', url)
  // The regular expression to match a valid URL.
  const regex =
    /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi

  // Match the URL against the regular expression.
  const match = regex.exec(url)
  // console.log('match', match)
  // Return true if the URL matches the regular expression, false otherwise.
  return !!match
}

const savePrompt = async () => {
  if (!selectedPromptData.value) {
    return
  }
  // Clone the data object to ensure reactivity
  let newData = JSON.parse(JSON.stringify(props.bar[currentType.value + 'Menu']))
  // console.log('newData: ', newData)
  // Find and update the node in the cloned object
  const nodeToUpdate = findNode(newData, selectedPromptData.value.id)
  if (nodeToUpdate) {
    nodeToUpdate.prompt = promptText.value
    nodeToUpdate.description = promptDesc.value
    nodeToUpdate.premium = isPremium.value
    nodeToUpdate.isBookmark = isBookmark.value
  }
  // console.log(
  //   'savePrompt nodeToUpdate: ',
  //   nodeToUpdate,
  //   currentType.value,
  //   selectedPromptData.value.id,
  // )
  Analytics.fireEvent('prompt_edit', {
    prompt_bar_id: props.bar.id,
    prompt_id: selectedPromptData.value.id,
    field: 'prompt',
  })

  // Update the local state and the Firestore
  props.bar[currentType.value + 'Menu'] = newData
  props.bar.prompt_menu = JSON.stringify(newData)

  updateBarPropertyInUserBars(props.bar, currentType.value + 'Menu')
  updateBarPropertyInUserBars(props.bar, 'prompt_menu')

  await editBar(props.bar, {
    prompt_menu: props.bar.prompt_menu,
  })
  await syncBarProperty(props.bar, 'prompt_menu')
  updateMyPromptsBarKeyInLocalStorage(currentType.value + 'Menu', newData)

  await createSharePageHandler(selectedPromptData.value)

  // console.log('Prompt SAVED: ', nodeToUpdate)
  // console.log('Saved data: ', props.bar[currentType.value + 'Menu'], props.bar.prompt_menu)
}

// Debounced version for real-time saving while typing
const debouncedSavePrompt = useDebounceFn(savePrompt, 2000)
const debouncedSaveLink = useDebounceFn(saveLink, 2000)
const debouncedUpdateDescription = useDebounceFn(updateDescription, 2000)
const debouncedUpdateRawJson = useDebounceFn(updateRawJson, 3000) // Longer delay for JSON
const saveLink = async () => {
  if (!selectedPromptData.value) return
  if (selectedPromptData.value.linkType !== 'modal' && !isValidURL(linkText.value)) {
    if (!isValidURL(linkText.value)) {
      invalidInput.value = true
      return
    }

    if (linkText.value.indexOf('http://') < 0 && linkText.value.indexOf('https://') < 0) {
      linkText.value = 'https://' + linkText.value
    }
  }
  invalidInput.value = false

  // console.log('Link: ', linkText.value)

  // Clone the data object to ensure reactivity
  let newData = JSON.parse(JSON.stringify(props.bar[currentType.value + 'Menu']))
  //  console.log('newData: ', newData)
  // Find and update the node in the cloned object
  const nodeToUpdate = findNode(newData, selectedPromptData.value.id)
  // console.log('nodeToUpdate: ', nodeToUpdate)
  if (nodeToUpdate) {
    nodeToUpdate.link = linkText.value
  }

  // Update the local state and the Firestore
  props.bar[currentType.value + 'Menu'] = newData
  props.bar.links = JSON.stringify(newData)
  // console.log('props.bar.links: ', props.bar.links)

  updateBarPropertyInUserBars(props.bar, currentType.value + 'Menu')
  updateBarPropertyInUserBars(props.bar, 'links')

  await editBar(props.bar, {
    links: props.bar.links,
  })
  await syncBarProperty(props.bar, 'links')
  updateMyPromptsBarKeyInLocalStorage(currentType.value + 'Menu', newData)

  // console.log('Link SAVED!!!')
  // console.log('Saved data: ', props.bar[currentType.value + 'Menu'], props.bar.links)
}

function findNode(tree, id) {
  for (let node of tree) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      let found = findNode(node.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

const updateDescription = async () => {
  // console.log('updateDescription', description.value)
  props.bar.description = description.value

  updateBarPropertyInUserBars(props.bar, 'description')

  await editBar(props.bar, {
    description: props.bar.description,
  })
  await syncBarProperty(props.bar, 'description')
  updateMyPromptsBarKeyInLocalStorage('description', props.bar.description)

  await createSharePageHandler()
}
const invalidRawJson = ref(false)
const updateRawJson = async () => {
  // console.log('updateRawJson', InputRef)
  // console.log('updateRawJson', rawjson.value)

  if (rawjson.value) {
    const isParsable = (dataToParse) => {
      try {
        return JSON.parse(dataToParse)
      } catch (error) {
        return false
      }
    }
    // console.log('isParsable: ', isParsable(rawjson.value))
    if (!isParsable(rawjson.value)) {
      ElNotification({
        title: 'Error',
        message: 'Couldn`t save changes. Invalid value of RawJson!',
        type: 'error',
      })
      invalidRawJson.value = true
      return
    }

    const getType = (d) => {
      if (Array.isArray(d)) return 'array'
      else if (typeof d == 'string') return 'string'
      else if (d != null && typeof d == 'object') return 'object'
      else return 'other'
    }

    // console.log('getType: ', getType(JSON.parse(rawjson.value)))
    if (getType(JSON.parse(rawjson.value)) !== 'array') {
      ElNotification({
        title: 'Error',
        message: 'Couldn`t save changes. RawJson must be an array!',
        type: 'error',
        type: 'error',
      })
      invalidRawJson.value = true
      return
    }
  }

  props.bar.promptMenu = JSON.parse(rawjson.value)
  props.bar.prompt_menu = rawjson.value

  updateBarPropertyInUserBars(props.bar, 'promptMenu')
  updateBarPropertyInUserBars(props.bar, 'prompt_menu')

  await editBar(props.bar, {
    prompt_menu: props.bar.prompt_menu,
  })
  await syncBarProperty(props.bar, 'prompt_menu')

  updateMyPromptsBarKeyInLocalStorage('promptMenu', JSON.parse(rawjson.value))

  invalidRawJson.value = false
}

const saveTags = async (data) => {
  // console.log('saveTags', dynamicTags.value, data)

  // Updating the tags field of the bar object
  props.bar.tags = data

  updateBarPropertyInUserBars(props.bar, 'tags')

  // return
  await editBar(props.bar, {
    tags: props.bar.tags,
  })
  await syncBarProperty(props.bar, 'tags')
  updateMyPromptsBarKeyInLocalStorage('tags', props.bar.tags)

  await createSharePageHandler()
}

const changeIsPublic = async () => {
  // console.log('changeIsPublic', isPublic.value)

  props.bar.isPublic = isPublic.value

  updateBarPropertyInUserBars(props.bar, 'isPublic')

  await editBar(props.bar, {
    isPublic: isPublic.value,
  })
  await syncBarWithPublicBars({
    bar: props.bar,
    isPublic: isPublic.value,
  })
  updateMyPromptsBarKeyInLocalStorage('isPublic', isPublic.value)
  emit('updateBars', props.bar, 'isPublic')

  // if (isPublic.value) {
  //   doSharePageUpdate.value = true
  //   createSharePageHandler()
  // }

  if (isPublic.value) {
    if (props.isPromptBarPublic) return
    doSharePageUpdate.value = true
    createSharePageHandler()
  } else {
    deleteSharedBarWithPrompts()
  }
}

const changeIsListed = async () => {
  // console.log('changeIsListed', isListed.value)
  props.bar.isListed = isListed.value

  updateBarPropertyInUserBars(props.bar, 'isListed')

  await editBar(props.bar, {
    isListed: isListed.value,
  })
  await syncBarWithPublicBars({
    bar: props.bar,
    isListed: isListed.value,
  })
  updateMyPromptsBarKeyInLocalStorage('isListed', isListed.value)
  emit('updateBars', props.bar, 'isListed')
}

const changeIsEditable = async () => {
  // console.log('changeIsEditable', isEditable.value)
  props.bar.isEditable = isEditable.value

  updateBarPropertyInUserBars(props.bar, 'isEditable')

  await editBar(props.bar, {
    isEditable: isEditable.value,
  })
  updateMyPromptsBarKeyInLocalStorage('isEditable', isEditable.value)
  emit('updateBars', props.bar, 'isEditable')
}

const changeIsPopular = async () => {
  // console.log('changeIsPopular', isPopular.value)

  props.bar.isPopular = isPopular.value

  updateBarPropertyInUserBars(props.bar, 'isPopular')

  await Promise.all([
    editBar(props.bar, {
      isPopular: isPopular.value,
    }),
    syncBarWithPopularBars({
      bar: props.bar,
      isPopular: isPopular.value,
      language: promptBarLanguage.value,
    }),
  ])

  message(
    isPopular.value ? t('messages.barAddedToPopular') : t('messages.barRemovedFromPopular'),
    'success',
  )

  updateMyPromptsBarKeyInLocalStorage('isPopular', isPopular.value)
  emit('updateBars', props.bar, 'isPopular')
}

const changeIsInLibrary = async () => {
  props.bar.isInLibrary = isInLibrary.value

  updateBarPropertyInUserBars(props.bar, 'isInLibrary')

  await Promise.all([
    editBar(props.bar, {
      isInLibrary: isInLibrary.value,
    }),
    syncBarWithLibraryBars({
      bar: props.bar,
      isInLibrary: isInLibrary.value,
      language: promptBarLanguage.value,
    }),
  ])

  message(
    isInLibrary.value ? t('messages.barAddedToLibrary') : t('messages.barRemovedFromLibrary'),
    'success',
  )

  updateMyPromptsBarKeyInLocalStorage('isInLibrary', isInLibrary.value)
  emit('updateBars', props.bar, 'isInLibrary')
}

watch(
  () => props.bar.name,
  (newName) => {
    if (promptBarName.value !== newName) {
      promptBarName.value = newName
    }
  },
)

// watch(
//   () => props.bar,
//   (data) => {
//     console.log('watching bar: ', data)
//     rawjson.value = JSON.stringify(data.promptMenu)
//   },
// )
watchEffect(() => {
  if (props.bar?.promptMenu) {
    // console.log('watching bar.promptMenu: ', props.bar.promptMenu)
    rawjson.value = JSON.stringify(props.bar.promptMenu)
  }
})
const changeIsPremium = async () => {
  // console.log('changeIsPremium', isPremium.value)
  savePrompt()
}

const updateBarIcon = async (icon) => {
  // console.log('updateBarIcon: ', props.bar, icon)

  props.bar.icon = icon

  updateBarPropertyInUserBars(props.bar, 'icon')

  await editBar(props.bar, {
    icon: icon,
  })
  await syncBarProperty(props.bar, 'icon')
  updateMyPromptsBarKeyInLocalStorage('icon', icon)
}

// const shareLink = computed(() => {
//   return generatePromptBarShareLink(props.bar)
// })

const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)
}

// console.log('props.isPromptBarPublic: ', props.isPromptBarPublic)
if (props.isPromptBarPublic !== undefined) {
  isPublic.value = props.isPromptBarPublic
  changeIsPublic()
  close()
  // emit('openShare', true)
}

if (props.isPromptBarListed !== undefined) {
  isListed.value = props.isPromptBarListed
  changeIsListed()
  close()
}

if (props.isPromptBarEditable !== undefined) {
  isEditable.value = props.isPromptBarEditable
  changeIsEditable()
  close()
}

function findParentWithChatLink(node) {
  if (node && node.data[chatLinkName]) {
    return node
  } else if (node && node.parent) {
    return findParentWithChatLink(node.parent)
  }
}

const onChatLinkSelectChange = (selectRef, inputRef, val, target) => {
  // console.log('onChatLinkSelectChange: ', selectRef, inputRef, val, target)
  if (val === customChatLink) {
    nextTick(() => {
      getActualURL(inputRef)
    })
    return
  }
  if (val === defaultChatLink) {
    inputRef.input.value = ''
    inputRef.focus()
    nextTick(() => {
      updateChatLink(target)
    })
    return
  }
  inputRef.input.value = val
  updateChatLink(target)

  // console.log('selectRef: ', selectRef, selectRef.value)
  nextTick(() => {
    selectRef.blur()
  })
}

const updateChatLink = async (updateTarget) => {
  // console.log('updateChatLink updateTarget: ', updateTarget)
  if (updateTarget === CollapseName.barChatLink) {
    // console.log('barChatLink value: ', barChatLink.value)
    // console.log('barChatLinkRef: ', barChatLinkRef)
    // console.log('barChatLinkRef value: ', barChatLinkRef.value)
    // console.log('selectedBar: ', selectedBar.value)
    if (barChatLinkRef && barChatLinkRef.value) barChatLinkRef.value.input.blur()
    // selectedBar.value[chatLinkName] = barChatLinkRef.value.input.value
    barChatLink.value = barChatLinkRef.value.input.value
    props.bar[chatLinkName] = barChatLink.value
    folderDefaultChatLink.value = barChatLink.value
    promptDefaultChatLink.value = barChatLink.value
    // console.log('set barChatLink: ', barChatLink.value, props.bar)

    updateBarPropertyInUserBars(props.bar, chatLinkName)

    await editBar(props.bar, {
      [chatLinkName]: props.bar[chatLinkName],
    })
    await syncBarProperty(props.bar, chatLinkName)
    updateMyPromptsBarKeyInLocalStorage(chatLinkName, barChatLink.value)

    await createSharePageHandler()
    return
  }
  if (updateTarget === CollapseName.folderChatLink) {
    folderChatLinkRef.value.input.blur()
    // console.log('folderChatLinkRef: ', folderChatLinkRef, folderChatLinkRef.value.input.value, selectedFolderData.value)
    if (folderChatLinkRef.value.input.value === '') {
      if (findParentWithChatLink(selectedFolderNode.value.parent)) {
        folderDefaultChatLink.value =
          findParentWithChatLink(selectedFolderNode.value.parent).data[chatLinkName] ||
          barChatLink.value
      } else {
        folderDefaultChatLink.value = barChatLink.value
      }
    }
    if (
      folderChatLinkRef.value.input.value === folderDefaultChatLink.value &&
      folderChatLink.value !== ''
    ) {
      folderChatLink.value = ''
      // return
    }
    selectedFolderData.value[chatLinkName] = folderChatLinkRef.value.input.value
    // console.log('set folderChatLink: ', selectedFolderData.value, props.bar)
  }
  if (updateTarget === CollapseName.promptChatLink) {
    promptChatLinkRef.value.input.blur()
    // console.log('promptChatLinkRef: ', promptChatLinkRef, promptChatLinkRef.value.input.value, selectedPromptData.value)
    if (promptChatLinkRef.value.input.value === '') {
      if (findParentWithChatLink(selectedPromptNode.value.parent)) {
        promptDefaultChatLink.value =
          findParentWithChatLink(selectedPromptNode.value.parent).data[chatLinkName] ||
          barChatLink.value
      } else {
        promptDefaultChatLink.value = barChatLink.value
      }
    }

    if (
      promptChatLinkRef.value.input.value === promptDefaultChatLink.value &&
      promptChatLink.value !== ''
    ) {
      promptChatLink.value = ''
      // return
    }

    selectedPromptData.value[chatLinkName] = promptChatLinkRef.value.input.value
    // console.log('set promptChatLink: ', selectedPromptData.value, props.bar)
  }

  // console.log('updated Chat Links: ', props.bar)

  let newData = JSON.parse(JSON.stringify(props.bar['promptMenu']))
  // console.log('newData: ', newData)

  // Update the local state and the Firestore
  props.bar['promptMenu'] = newData
  props.bar.prompt_menu = JSON.stringify(newData)

  updateBarPropertyInUserBars(props.bar, 'promptMenu')
  updateBarPropertyInUserBars(props.bar, 'prompt_menu')

  await editBar(props.bar, {
    prompt_menu: props.bar.prompt_menu,
  })
  await syncBarProperty(props.bar, 'prompt_menu')
  updateMyPromptsBarKeyInLocalStorage('promptMenu', newData)

  await createSharePageHandler()
}

const editDefaultChatLink = (updateTarget) => {
  // console.log('editDefaultChatLink: ', updateTarget)
  if (updateTarget === CollapseName.folderChatLink) {
    if (folderChatLinkRef.value.input.value === '')
      folderChatLink.value = folderDefaultChatLink.value
    folderChatLinkRef.value.input.focus()
  }
  if (updateTarget === CollapseName.promptChatLink) {
    if (promptChatLinkRef.value.input.value === '')
      promptChatLink.value = promptDefaultChatLink.value
    promptChatLinkRef.value.input.focus()
  }
}

const getActualURL = (ref) => {
  // console.log('get URL ref: ', ref)
  // let path = window.location.pathname !== '/' ? window.location.pathname : ''
  let url = window.location.origin + window.location.pathname
  // console.log('get URL url: ', url)
  ref.input.value = url
  nextTick(() => {
    ref.input.focus()
    // ref.input.blur()
  })
}

const clearURL = (ref) => {
  // console.log('clear URL ref: ', ref)
  ref.input.value = ''
  nextTick(() => {
    ref.input.focus()
    //   ref.input.blur()
  })
}

const insertTags = (openTag, closeTag) => {
  const textarea = document.getElementById('promptEditText')
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = promptText.value.substring(start, end)

  const newText =
    promptText.value.substring(0, start) +
    openTag +
    (selectedText || '') +
    closeTag +
    promptText.value.substring(end)

  promptText.value = newText

  nextTick(() => {
    textarea.focus()
    if (selectedText) {
      textarea.setSelectionRange(start + openTag.length, end + openTag.length)
    } else {
      textarea.setSelectionRange(start + openTag.length, start + openTag.length)
    }
  })
}

const changeIsBookmark = async () => {
  // console.log('changeIsBookmark: ', isBookmark.value)
  // console.log('selectedPromptData: ', selectedPromptData.value)
  activeAccordionPromptEdit.value = getActiveAccordionPromptEdit()
  await savePrompt()
}

const actualDomain = ref(window.location.origin)

const isCreatingSharePage = ref(false)
const createSharePageHandler = async (prompt = null) => {
  if (!isPublic.value || isCreatingSharePage.value) return

  const hash = ref(props.bar.id)
  const barName = ref(props.bar.name)
  const barTags = ref(props.bar.tags.join(',') || '')
  const promptId = ref(prompt ? prompt.id : '')
  const promptName = ref(prompt ? prompt.label : '')
  const barDescription = ref(props.bar.description || '')
  const promptDescription = ref(prompt && prompt.description ? prompt.description : '')
  const description = ref(promptDescription.value ? promptDescription.value : barDescription.value)
  const destination = ref(prompt ? prompt.destination ?? '' : props.bar.destination ?? '')

  try {
    const result = await createSharePage(
      hash.value,
      barName.value,
      doSharePageUpdate.value,
      destination.value ?? actualDomain.value,
      promptId.value,
      promptName.value,
      description.value,
      barTags.value,
    )

    if (result.success) {
      // console.log('result: ', result)
      shareImg.value = result.imageUrl
      shareLink.value = result.shareLink
      hasCreatedSharePage.value = true
      isCreatingSharePage.value = false
    } else {
      hasCreatedSharePage.value = false
      isCreatingSharePage.value = false
    }
  } catch (error) {
    hasCreatedSharePage.value = false
    isCreatingSharePage.value = false
  }
}

const openShareLink = () => {
  window.open(shareLink.value, '_blank')
}

const updateSharePage = async () => {
  await createSharePageHandler()
}

const onPromptNameChange = async (prompt) => {
  await createSharePageHandler(prompt)
}

const deleteSharedBarWithPrompts = async () => {
  const findPromptIds = (menu) => {
    let ids = []
    menu.forEach((item) => {
      if (item.children) {
        ids = [...ids, ...findPromptIds(item.children)]
      } else {
        ids.push(item.id)
      }
    })
    return ids
  }

  const promptIds = findPromptIds(props.bar.promptMenu)
  const allIds = [props.bar.id, ...promptIds]

  await deleteShared(allIds)
}

// Synchronizuj lokalne refy z props.bar gdy się zmieniają
watch(
  () => [props.bar.description, props.bar.destination, props.bar.tags],
  ([newDescription, newDestination, newTags]) => {
    description.value = newDescription || ''
    barChatLink.value = newDestination || ''
    dynamicTags.value = newTags || []
  },
)

// Synchronizuj dropdown z destination
watch(barChatLink, (newVal) => {
  if (isChatLinkOnList(newVal)) {
    barChatLinkSelected.value = newVal
  } else if (newVal) {
    barChatLinkSelected.value = customChatLink
  } else {
    barChatLinkSelected.value = defaultChatLink
  }
})
</script>

<template>
  <el-dialog
    :model-value="props.isOpen"
    width="clamp(500px, 90vw, 1000px)"
    @close="close"
    destroy-on-close
  >
    <template #header>
      <div class="flex gap-x-2 items-center justify-start my-header pr-8 text-xl">
        <!-- <CustomIcon :name="promptBarName"/> -->
        <SelectIconPopover v-if="isAdmin" :bar="props.bar" @updateBarIcon="updateBarIcon" />

        <h2 id="prompt-bar-name" class="w-full">
          <div v-show="isPromptBarNameEditModeEnabled" class="flex gap-2">
            <el-input
              ref="promptBarNameInput"
              class="flex-1 text-xl -ml-2 px-0.5"
              :class="promptBarNameBlur ? 'animate-pulse' : ''"
              v-model="promptBarName"
              minlength="2"
              maxlength="60"
              show-word-limit
              @blur="promptBarNameBlur = true"
              @focus="promptBarNameBlur = false"
              @keydown.enter="togglePromptBarNameEditMode(false, false)"
              @keydown.escape="togglePromptBarNameEditMode(false, true)"
            >
              <template #suffix>
                <el-button
                  type="success"
                  text
                  plain
                  data-testid="prompt-bar-title-confirmation-button"
                  @click="togglePromptBarNameEditMode(false, false)"
                  style="padding: 0.2em 0.5em; margin: 0"
                >
                  <el-icon :size="20"><Select /></el-icon>
                </el-button>
                <el-button
                  type="danger"
                  text
                  plain
                  data-testid="prompt-bar-title-reject-button"
                  @click="togglePromptBarNameEditMode(false, true)"
                  style="padding: 0.2em 0.5em; margin: 0"
                >
                  <el-icon :size="20"><Close /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div
            v-show="!isPromptBarNameEditModeEnabled"
            @dblclick="togglePromptBarNameEditMode(true, false)"
            class="flex gap-2 items-center"
          >
            <span class="border border-1 border-transparent px-1">
              {{ promptBarName }}
            </span>
            <!-- Informative loading indicator for AI name generation -->
            <div
              v-if="isGeneratingPromptName && promptBarName === t('messages.newPrompt')"
              class="flex items-center gap-2 text-blue-600 text-sm"
            >
              <el-icon class="animate-spin" :size="14">
                <Loading />
              </el-icon>
              <span class="text-blue-600">{{ t('messages.generatingName') }}</span>
            </div>
            <el-button
              text
              plain
              @click="togglePromptBarNameEditMode(true, false)"
              style="padding: 0.2em 0.5em; margin: 0"
              data-testid="prompt-bar-title-edit-button"
            >
              <el-icon :size="18"><EditPen /></el-icon>
            </el-button>
          </div>
          <div class="w-full text-center">
            <span class="text-red-500 text-sm">{{ promptBarNameError }}</span>
          </div>
          <!-- Enhanced informative message for AI name generation -->
          <div
            v-if="isGeneratingPromptName && promptBarName === t('messages.newPrompt')"
            class="w-full mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md"
          >
            <div class="flex items-center gap-2 text-blue-700 dark:text-blue-300 text-sm">
              <el-icon class="animate-spin" :size="14">
                <Loading />
              </el-icon>
              <div class="flex-1">
                <div class="font-medium">{{ t('messages.generatingName') }}</div>
                <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  {{ t('messages.generatingNameDescription') }}
                </div>
              </div>
            </div>
          </div>
        </h2>
      </div>
    </template>

    <el-tabs v-model="activeEditTab" class="edit-tabs">
      <el-tab-pane :label="EditTABS.promptsTab" :name="clearWhSp(EditTABS.promptsTab)">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-collapse
              v-model="activeAccordionBarPrompts"
              @change="(showPromptEdit = false), (showLinkEdit = false), (showFolderEdit = false)"
            >
              <el-collapse-item
                :title="CollapseName.barPrompts"
                :name="clearWhSp(CollapseName.barPrompts)"
                class="relative"
                data-testid="prompts-dropdown-edit-prompt-bar"
              >
                <EditPromptTree
                  :type="type[0]"
                  :bar="props.bar"
                  :isAdmin="isAdmin"
                  :isPromptBookmark="isPromptBookmark"
                  @openEditTextarea="openEditTextarea"
                  @openEditFolder="openEditFolder"
                  @onPromptNameChange="onPromptNameChange"
                />
              </el-collapse-item>
              <el-collapse-item
                :title="CollapseName.barDesc"
                :name="clearWhSp(CollapseName.barDesc)"
                data-testid="description-dropdown-edit-prompt-bar"
              >
                <el-input
                  v-model="description"
                  :rows="5"
                  type="textarea"
                  :placeholder="t('editPromptBar.description')"
                  @input="debouncedUpdateDescription"
                  @blur="updateDescription"
                  data-testid="main-description-dropdown-edit-prompt-bar"
                />
              </el-collapse-item>
              <el-collapse-item
                v-if="isAdmin"
                :title="CollapseName.barRawJson"
                :name="clearWhSp(CollapseName.barRawJson)"
              >
                <el-input
                  ref="InputRef"
                  v-model="rawjson"
                  :rows="5"
                  type="textarea"
                  placeholder="Raw JSON code"
                  @input="debouncedUpdateRawJson"
                  @blur="updateRawJson"
                />
                <span v-if="invalidRawJson" class="text-red-500">{{
                  t('editPromptBar.errors.invalidJson')
                }}</span>
              </el-collapse-item>
              <el-collapse-item
                :title="CollapseName.barChatLink"
                :name="clearWhSp(CollapseName.barChatLink)"
                data-testid="chat-link-dropdown-edit-prompt-bar"
              >
                <el-select
                  v-model="barChatLinkSelected"
                  ref="barChatLinkSelectRef"
                  @change="
                    onChatLinkSelectChange(
                      barChatLinkSelectRef,
                      barChatLinkRef,
                      barChatLinkSelected,
                      CollapseName.barChatLink,
                    )
                  "
                  placeholder="Select"
                  class="w-full mb-2"
                  data-testid="main-destination-dropdown-edit-prompt-bar"
                >
                  <el-option
                    v-for="link in chatLinkSelect"
                    :key="link.name"
                    :label="link.name"
                    :value="link.url"
                  />
                </el-select>
                <el-input
                  :disabled="barChatLinkSelected !== customChatLink"
                  v-model="barChatLink"
                  ref="barChatLinkRef"
                  data-testid="input-websites-ai-destination"
                  :placeholder="barChatLink"
                  @blur="updateChatLink(CollapseName.barChatLink)"
                  @focus="editDefaultChatLink(CollapseName.barChatLink)"
                  @keydown.enter="updateChatLink(CollapseName.barChatLink)"
                  @keydown.escape="updateChatLink(CollapseName.barChatLink)"
                  :class="{ 'bg-blue-500/5': barChatLink }"
                >
                  <template #prepend>
                    <el-button-group size="small">
                      <el-button
                        :disabled="barChatLinkSelected !== customChatLink"
                        @click="getActualURL(barChatLinkRef)"
                        :icon="'AddLocation'"
                      >
                      </el-button>
                      <el-button
                        :disabled="barChatLinkSelected !== customChatLink"
                        @click="clearURL(barChatLinkRef)"
                        :icon="'Close'"
                      >
                      </el-button>
                    </el-button-group>
                  </template>
                </el-input>
              </el-collapse-item>
            </el-collapse>

            <div class="mt-4 tag-container">
              <EditTags
                :key="dynamicTags.join(',')"
                :dynamicTags="dynamicTags"
                @saveTags="saveTags"
              />
              <el-text class="mt-2 mb-2 font-bold w-full">{{ $t('smallMenu.teams') }}:</el-text>
              <TeamDropdown
                v-model="selectedTeams"
                :multiple="true"
                :useLocalState="true"
                :bar="props.bar"
                :user="user.value"
                :readonly="
                  !isOwner() &&
                  !(userDoc.value?.teams || []).some(
                    (team) =>
                      team.ownerId === user.value?.uid || team.owner?.id === user.value?.uid,
                  )
                "
                class="w-full mb-4"
              />
            </div>

            <div v-if="isOwner() || isAdminOrSuperAdmin(userDoc)" class="flex flex-col gap-2">
              <LanguagePromptBarSelector class="w-1/2 mb-2" />
              <div class="flex gap-4 justify-between">
                <span class="flex gap-2 items-center">
                  <el-switch
                    v-model="isPublic"
                    :active-text="t('editPromptBar.share.public')"
                    :inactive-text="t('editPromptBar.share.private')"
                    @change="changeIsPublic"
                    data-testid="is-public-switch"
                  />
                </span>
                <span class="flex gap-2 items-center" v-if="isAdminOrSuperAdmin(userDoc)"
                  >Popular
                  <el-switch
                    v-model="isPopular"
                    inline-prompt
                    :active-icon="Check"
                    :inactive-icon="Close"
                    @change="changeIsPopular"
                    data-testid="visible-in-popular-switch"
                /></span>
                <span class="flex gap-2 items-center" v-if="isAdminOrSuperAdmin(userDoc)"
                  >Library
                  <el-switch
                    v-model="isInLibrary"
                    inline-prompt
                    :active-icon="Check"
                    :inactive-icon="Close"
                    @change="changeIsInLibrary"
                    data-testid="visible-in-library-switch"
                /></span>
              </div>
            </div>

            <div class="flex flex-wrap mt-2" v-if="isPublic && isOwner()">
              <el-text class="mb-2 w-full text-gray-400">{{
                t('editPromptBar.share.link')
              }}</el-text>
              <el-input
                v-model="shareLink"
                :value="shareLink"
                readonly
                class="flex-1"
                data-testid="share-link-input"
              >
                <template #append>
                  <el-button-group>
                    <Tooltip
                      effect="light"
                      placement="bottom"
                      v-if="isPublic && isAdminOrSuperAdmin(userDoc)"
                    >
                      <template #content>
                        <span>{{ t('editPromptBar.share.update') }}</span>
                      </template>
                      <el-button
                        type="primary"
                        @click="updateSharePage"
                        :loading="isCreatingSharePage"
                      >
                        <el-icon><Refresh /></el-icon>
                      </el-button>
                    </Tooltip>
                    <Tooltip effect="light" placement="bottom">
                      <template #content>
                        <span>{{ t('editPromptBar.share.visit') }}</span>
                      </template>
                      <el-button type="primary" @click="openShareLink" icon="TopRight"></el-button>
                    </Tooltip>
                    <Tooltip
                      effect="light"
                      placement="bottom"
                      :content="t('editPromptBar.share.copy')"
                    >
                      <el-button
                        type="primary"
                        @click="copyShareLink"
                        icon="DocumentCopy"
                      ></el-button>
                    </Tooltip>
                  </el-button-group>
                </template>
              </el-input>
              <div class="flex gap-2 items-center w-full mt-2">
                <el-switch
                  v-model="isListed"
                  inline-prompt
                  :active-icon="Check"
                  :inactive-icon="Close"
                  @change="changeIsListed"
                  data-testid="available-in-public-switch"
                />
                <span v-if="isListed">{{ t('editPromptBar.share.listed') }}</span>
                <span v-else>{{ t('editPromptBar.share.unlisted') }}</span>
              </div>
              <div class="flex gap-2 items-center w-full mt-2">
                <el-switch
                  v-model="isEditable"
                  inline-prompt
                  :active-icon="Check"
                  :inactive-icon="Close"
                  @change="changeIsEditable"
                  data-testid="not-editable-by-others-switch"
                />
                <span v-if="isEditable">{{ t('editPromptBar.share.editable') }}</span>
                <span v-else>{{ t('editPromptBar.share.notEditable') }}</span>
              </div>
              <!-- <el-button type="primary" @click="copyShareLink">Copy</el-button> -->
            </div>
          </el-col>
          <el-col :span="12">
            <el-card v-if="showFolderEdit" shadow="never" class="border-none">
              <el-collapse v-model="activeAccordionFolderEdit" accordion>
                <el-collapse-item
                  :title="CollapseName.folderChatLink"
                  :name="clearWhSp(CollapseName.folderChatLink)"
                >
                  <el-select
                    v-model="folderChatLinkSelected"
                    ref="folderChatLinkSelectRef"
                    @change="
                      onChatLinkSelectChange(
                        folderChatLinkSelectRef,
                        folderChatLinkRef,
                        folderChatLinkSelected,
                        CollapseName.folderChatLink,
                      )
                    "
                    placeholder="Select"
                    class="w-full mb-2"
                  >
                    <el-option
                      v-for="link in nodesChatLinkSelect"
                      :key="link.name"
                      :label="link.name"
                      :value="link.url"
                    />
                  </el-select>
                  <el-input
                    :disabled="folderChatLinkSelected !== customChatLink"
                    v-model="folderChatLink"
                    ref="folderChatLinkRef"
                    :placeholder="folderDefaultChatLink"
                    @blur="updateChatLink(CollapseName.folderChatLink)"
                    @focus="editDefaultChatLink(CollapseName.folderChatLink)"
                    @keydown.enter="updateChatLink(CollapseName.folderChatLink)"
                    @keydown.escape="updateChatLink(CollapseName.folderChatLink)"
                    :class="{ 'bg-blue-500/5': folderChatLink }"
                  >
                    <template #prepend>
                      <el-button-group size="small">
                        <el-button
                          :disabled="folderChatLinkSelected !== customChatLink"
                          @click="getActualURL(folderChatLinkRef)"
                          :icon="'AddLocation'"
                        ></el-button>
                        <el-button
                          :disabled="folderChatLinkSelected !== customChatLink"
                          @click="clearURL(folderChatLinkRef)"
                          :icon="'Close'"
                        ></el-button>
                      </el-button-group>
                    </template>
                  </el-input>
                </el-collapse-item>
              </el-collapse>

              <div v-if="isAdmin">
                <!-- <el-divider></el-divider> -->
                <div class="mt-2">{{ t('editPromptBar.folderOptions.title') }}</div>
                <div class="mt-2">
                  {{ t('editPromptBar.folderOptions.premium') }}
                  <el-button type="primary" @click="setFolderAsPremium" plain class="ml-2">
                    {{ t('editPromptBar.folderOptions.setPremium') }}
                  </el-button>
                </div>
              </div>
            </el-card>

            <el-card v-if="showPromptEdit" shadow="never" class="border-none">
              <el-collapse
                v-model="activeAccordionPromptEdit"
                :accordion="!isBookmark"
                class="flex"
                :class="isBookmark ? 'flex-col-reverse' : 'flex-col'"
              >
                <el-collapse-item
                  v-if="!isBookmark"
                  :title="CollapseName.promptContents"
                  :name="clearWhSp(CollapseName.promptContents)"
                >
                  <el-input
                    v-model="promptText"
                    :rows="15"
                    type="textarea"
                    placeholder=""
                    @input="debouncedSavePrompt"
                    @blur="savePrompt"
                    id="promptEditText"
                    data-testid="prompt-edit-text"
                  /><br />
                  <div class="mb-2 flex gap-2">
                    <Tooltip effect="light" content="Select word - example {{input}}">
                      <el-button @click="insertTags('{{', '}}')">{{
                        t('editPromptBar.buttons.input')
                      }}</el-button>
                    </Tooltip>
                    <Tooltip effect="light" content="Select word - example ((textarea))">
                      <el-button @click="insertTags('((', '))')">{{
                        t('editPromptBar.buttons.textarea')
                      }}</el-button>
                    </Tooltip>
                    <Tooltip effect="light" content="Select word - example [[option1, option2]]">
                      <el-button @click="insertTags('[[', ']]')">{{
                        t('editPromptBar.buttons.select')
                      }}</el-button>
                    </Tooltip>
                  </div>
                </el-collapse-item>
                <el-collapse-item
                  :title="CollapseName.promptDesc"
                  :name="clearWhSp(CollapseName.promptDesc)"
                  data-testid="prompt-edit-desc"
                >
                  <el-input
                    v-model="promptDesc"
                    :rows="15"
                    type="textarea"
                    placeholder=""
                    @input="debouncedSavePrompt"
                    @blur="savePrompt"
                    id="promptEditDesc"
                    data-testid="prompt-edit-desc-text-area"
                  />
                </el-collapse-item>
                <el-collapse-item
                  :title="CollapseName.promptChatLink"
                  :name="clearWhSp(CollapseName.promptChatLink)"
                  data-testid="prompt-edit-chat-link"
                >
                  <el-select
                    v-model="promptChatLinkSelected"
                    ref="promptChatLinkSelectRef"
                    data-testid="prompt-chat-link-select-ref"
                    @change="
                      onChatLinkSelectChange(
                        promptChatLinkSelectRef,
                        promptChatLinkRef,
                        promptChatLinkSelected,
                        CollapseName.promptChatLink,
                      )
                    "
                    placeholder="Select"
                    class="w-full mb-2"
                  >
                    <el-option
                      v-for="link in nodesChatLinkSelect"
                      :key="link.name"
                      :label="link.name"
                      :value="link.url"
                    />
                  </el-select>
                  <el-input
                    :disabled="promptChatLinkSelected !== customChatLink"
                    v-model="promptChatLink"
                    ref="promptChatLinkRef"
                    data-testid="prompt-chat-link-ref"
                    :placeholder="promptDefaultChatLink"
                    @blur="updateChatLink(CollapseName.promptChatLink)"
                    @focus="editDefaultChatLink(CollapseName.promptChatLink)"
                    @keydown.enter="updateChatLink(CollapseName.promptChatLink)"
                    @keydown.escape="updateChatLink(CollapseName.promptChatLink)"
                    :class="{ 'bg-blue-500/5': promptChatLink }"
                  >
                    <template #prepend>
                      <el-button-group size="small">
                        <el-button
                          :disabled="promptChatLinkSelected !== customChatLink"
                          @click="getActualURL(promptChatLinkRef)"
                          :icon="'AddLocation'"
                        >
                        </el-button>
                        <el-button
                          :disabled="promptChatLinkSelected !== customChatLink"
                          @click="clearURL(promptChatLinkRef)"
                          :icon="'Close'"
                        >
                        </el-button>
                      </el-button-group>
                    </template>
                  </el-input>
                </el-collapse-item>
              </el-collapse>

              <div class="flex gap-2 items-center justify-between mt-2">
                <el-switch
                  v-model="isBookmark"
                  :active-text="t('editPromptBar.promptOptions.bookmark')"
                  :inactive-text="t('editPromptBar.promptOptions.prompt')"
                  @change="changeIsBookmark"
                />
                <el-switch
                  v-if="isAdmin"
                  v-model="isPremium"
                  :active-text="t('editPromptBar.promptOptions.premium')"
                  :inactive-text="t('editPromptBar.promptOptions.free')"
                  @change="changeIsPremium"
                />
              </div>

              <!-- <div class="select-container">
                    <el-select v-model="value" class="m-2 my-select" placeholder="Select" size="small">
                      <el-option
                        v-for="item in textPositionDescription"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div> -->
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane :label="EditTABS.linksTab" :name="clearWhSp(EditTABS.linksTab)">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-collapse v-model="activeAccordionBarLinks" accordion>
              <el-collapse-item
                :title="CollapseName.barLinks"
                :name="clearWhSp(CollapseName.barLinks)"
                class="relative"
              >
                <EditPromptTree
                  :type="type[1]"
                  :bar="props.bar"
                  @openEditTextarea="openEditTextarea"
                />
              </el-collapse-item>
            </el-collapse>
          </el-col>

          <el-col v-if="showLinkEdit" :span="12">
            <el-card shadow="never" class="border-none">
              <div class="mt-2">
                <template v-if="linkType === 'url' || linkType === 'video'">
                  <el-input
                    v-model="linkText"
                    placeholder="https://www.sample.com"
                    @input="debouncedSaveLink"
                    @blur="saveLink"
                    @keyup-enter="saveLink"
                    class="shadow-sm"
                    data-testid="link-edit-input-in-bar-links"
                    :class="invalidInput ? 'shadow-red-500' : 'shadow-transparent'"
                  />
                  <div v-if="invalidInput" class="text-red-500 text-center p-2">
                    Url is invalid!
                  </div>
                </template>
                <template v-if="linkType === 'modal'">
                  <el-input
                    v-model="linkText"
                    :rows="15"
                    type="textarea"
                    placeholder=""
                    @input="debouncedSaveLink"
                    @blur="saveLink"
                    data-testid="link-edit-prompt-input-in-bar-links"
                  />
                </template>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane :label="EditTABS.copyTab" :name="clearWhSp(EditTABS.copyTab)">
        <EditPromptCopyTab :bar="props.bar" :user="user" />
      </el-tab-pane>

      <el-tab-pane
        v-if="isAdmin"
        :label="EditTABS.translationTab"
        :name="clearWhSp(EditTABS.translationTab)"
      >
        <PromptBarTranslationTab />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="close">Cancel</el-button> -->
        <el-button type="" @click="close" data-testid="close-edit-prompt-bar-dialog">
          {{ t('editPromptBar.buttons.close') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style>
#prompt-bar-name {
  cursor: pointer;
}

.el-dialog__body {
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}
.select-container {
  display: flex;
  flex-direction: column;
}

.my-select {
  width: 100%;
  margin-top: 10px;
}
.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 10px;
}
.tag-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag-item {
  margin: 5px;
}

.input-container {
  width: 100px;
}
.delete-button i {
  color: white;
  margin-left: auto;
}

.edit-tabs .el-tabs__content {
  overflow: unset;
}

.translation-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
