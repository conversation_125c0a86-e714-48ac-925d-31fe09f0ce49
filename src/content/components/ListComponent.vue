<template>
  <div>
    <h3>{{ title }}</h3>
    <ul style="list-style-type: none; padding: 0">
      <li v-for="(item, index) in items" :key="index" style="margin: 5px 0">
        <button class="btn" @click="() => selectItem(item)" style="width: 100%; text-align: left">
          <slot name="item" :item="item">{{ item }}</slot>
        </button>
      </li>
    </ul>
  </div>
</template>

<script setup>
const props = defineProps({
  items: { type: Array, default: () => [] },
  title: { type: String, default: 'Lista elementów:' },
})
const emit = defineEmits(['item-selected'])

const selectItem = (item) => {
  emit('item-selected', item)
}
</script>

<style scoped>
.btn {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
}
.btn:hover {
  background-color: #45a049;
}
</style>
