<script setup>
import { Check, CreditCard, User } from '@element-plus/icons-vue'
import {
  createCheckoutSession,
  getCurrentUserSubscriptions,
  getProducts,
} from '@invertase/firestore-stripe-payments'
import { storeToRefs } from 'pinia'
import { computed, onBeforeMount, onUpdated, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import { useAppStateStore } from '@/stores/appState'

import { getStripePromotionCode, payments, tryCustomCouponCode } from '../firebase'
import { getFromLocalStorage } from '../localStorage'
import { cc, getMaxCreditsByPlan } from '../utils'
import IconPlanFree from './icons/iconPlanFree.vue'
import IconPlanPremium from './icons/iconPlanPremium.vue'
import IconPlanStandard from './icons/iconPlanStandard.vue'
import IconPlanTeam from './icons/iconPlanTeam.vue'
import SignInSignUpForm from './SignInSignUpForm.vue'
import TeamsManagement from './TeamsManagement.vue'

const props = defineProps(['isOpen', 'selectedTab'])
const emit = defineEmits(['closeDialog'])
const close = () => emit('closeDialog')

const user = useCurrentUser()

const appState = useAppStateStore()
const { userDoc, isLoggedIn, promo } = storeToRefs(appState)

const { t } = useI18n()

const userSubscription = computed(() => {
  const subscription = userDoc?.value?.subscription || { members: [], maxMembers: 0 }
  if (!subscription.members) subscription.members = []
  if (!subscription.maxMembers) subscription.maxMembers = 0
  return subscription
})

const products = ref([])
const currentSubscriptions = ref(null)
const userSubscriptions = ref([])
const isCreatingSession = ref(false)
const activeTab = ref(props.selectedTab || 'Plans')
const subscriptionInterval = ref('month')
const numberOfMembers = ref(2)
const promotionCode = ref(null)
const promoError = ref(null)
const couponCode = ref(promo.value || userDoc.value?.coupon || '')
const intervalNamesByInterval = computed(() => ({
  month: t('subscriptionDialog.intervals.monthly'),
  year: t('subscriptionDialog.intervals.annually'),
}))
const areProductsLoaded = computed(() => products.value.length > 0)

const existingMemberEmails = ref([])

const canViewTeams = computed(() => userDoc?.value?.teams?.length > 0 || false)

onBeforeMount(async () => {
  products.value = await getProducts(payments, {
    includePrices: true,
    activeOnly: true,
  })

  products.value = products.value.map((product) => {
    const priceByInterval = {}
    let isTiered = false
    Object.keys(intervalNamesByInterval.value).forEach((interval) => {
      const priceObject = product.prices.find(
        (price) => price.recurring?.interval === interval && price.active,
      )
      if (priceObject) {
        if (priceObject.tiers?.length) isTiered = true
        priceByInterval[interval] = {
          price: priceObject.unit_amount ? priceObject.unit_amount / 100 : null,
          tiers: (priceObject.tiers || [])
            .filter((tier) => tier.up_to)
            .map((tier) => ({
              upTo: tier.up_to,
              price: tier.unit_amount / 100,
            })),
          priceId: priceObject.id,
        }
      }
    })

    return {
      ...product,
      priceByInterval,
      isTiered,
    }
  })

  products.value.sort(
    (a, b) =>
      a.isTiered - b.isTiered ||
      (Object.values(a.priceByInterval)[0]?.price || Infinity) -
        (Object.values(b.priceByInterval)[0]?.price || Infinity),
  )

  const productById = products.value.reduce((result, product) => {
    result[product.id] = {
      ...product,
      priceById: product.prices.reduce((result, priceObject) => {
        const price = priceObject.unit_amount / 100
        result[priceObject.id] = { ...priceObject, price, priceText: `$${price}` }
        return result
      }, {}),
    }
    return result
  }, {})

  if (!isLoggedIn.value) return
  userSubscriptions.value = ((await getCurrentUserSubscriptions(payments)) || [])
    .sort((a, b) => a.current_period_end - b.current_period_end)
    .map((subscription) => {
      const productItem = productById[subscription.product]
      if (!productItem) return
      subscription.productItem = productItem
      subscription.priceItem = productItem.priceById[subscription.price]
      return subscription
    })
    .filter((subscription) => subscription)
  const subscriptionsByStatus = userSubscriptions.value.reduce((result, subscription) => {
    if (!result[subscription.status]) result[subscription.status] = []
    result[subscription.status].push(subscription)
    return result
  }, {})
  if (subscriptionsByStatus['active']?.length > 0) {
    currentSubscriptions.value = subscriptionsByStatus['active'].sort(
      (a, b) => new Date(b.current_period_end) - new Date(a.current_period_end),
    )
  }

  if (!couponCode.value) {
    return
  }
  await getPromotionCode(couponCode.value)
})

onUpdated(() => {
  if (!props.isOpen) return
  activeTab.value = props.selectedTab || 'Plans'
  couponCode.value = promo.value || userDoc.value?.coupon || ''
  existingMemberEmails.value = [userDoc?.value?.email, ...userSubscription.value?.members]
})

function isUserTransactionRestricted() {
  return (
    promotionCode.value?.restrictions?.first_time_transaction && userSubscriptions.value?.length
  )
}

function isCouponApplicable(productId) {
  return (
    !isUserTransactionRestricted() &&
    promotionCode.value?.coupon &&
    (!promotionCode.value.coupon.applies_to?.products ||
      promotionCode.value.coupon.applies_to?.products?.includes(productId))
  )
}

async function getPromotionCode(code) {
  const currentPromotionCode = await getStripePromotionCode(code)
  if (currentPromotionCode) {
    promotionCode.value = currentPromotionCode
    promoError.value = null
  } else {
    promotionCode.value = null
    promoError.value = t('subscriptionDialog.coupon.errors.invalid')
  }
}

async function applyCoupon() {
  if (!couponCode.value?.trim()) return
  if (userDoc.value?.coupon) {
    promotionCode.value = null
    promoError.value = t('subscriptionDialog.coupon.errors.alreadyUsed')
    return
  }
  const promoCode = await tryCustomCouponCode(couponCode.value, user.value.uid)
  if (promoCode) {
    promotionCode.value = promoCode
    promoError.value = null
  } else {
    await getPromotionCode(couponCode.value)
  }
}

function getSubscriptionHistory() {
  const subscriptionHistory = userSubscriptions.value
    .map((subscription) => {
      if (!subscription.created || !subscription.current_period_start) return null
      const createdDate = new Date(subscription.created)
      const endDate = new Date(subscription.current_period_end)
      const interval = subscription.priceItem.interval
      const intervalCount = subscription.priceItem.interval_count
      const priceText = subscription.priceItem.priceText
      let currentLoopStartDate = createdDate
      let currentLoopEndDate
      const historyItemsForInterval = []
      while (currentLoopStartDate < endDate) {
        currentLoopEndDate = new Date(currentLoopStartDate)
        if (interval === 'day') {
          currentLoopEndDate = new Date(
            currentLoopEndDate.setDate(currentLoopEndDate.getDate() + intervalCount),
          )
        } else if (interval === 'month') {
          currentLoopEndDate = new Date(
            currentLoopEndDate.setMonth(currentLoopEndDate.getMonth() + intervalCount),
          )
        } else if (interval === 'year') {
          currentLoopEndDate = new Date(
            currentLoopEndDate.setFullYear(currentLoopEndDate.getFullYear() + intervalCount),
          )
        }
        historyItemsForInterval.push({
          startDate: currentLoopStartDate,
          endDate: currentLoopEndDate,
          startDateString: currentLoopStartDate.toLocaleString(),
          endDateString: currentLoopEndDate.toLocaleString(),
          priceText,
        })
        currentLoopStartDate = currentLoopEndDate
      }
      return historyItemsForInterval
    })
    .flat(1)
    .sort((a, b) => b.startDate - a.startDate)
  return subscriptionHistory
}

function handleMembersUpdated(updatedEmails) {
  existingMemberEmails.value = updatedEmails
}

function manageStripeSubscription() {
  window.open(import.meta.env.VITE_STRIPE_CUSTOMER_PORTAL, '_blank').focus()
}

async function buySubscription(productId, priceId, isTiered = false, numberOfMembers = 1) {
  isCreatingSession.value = true
  let session
  const affiliateParams = getFromLocalStorage('affiliateParams') || {}
  const { clientReferenceId } = affiliateParams

  try {
    const userEmail = user.value.email
    const urlParamsString = new URLSearchParams({ ...affiliateParams, email: userEmail }).toString()

    const successUrl = new URL('https://ai-promptlab.com/successful-payment/')
    if (urlParamsString) {
      successUrl.search = urlParamsString
    }

    session = await createCheckoutSession(payments, {
      price: priceId,
      allow_promotion_codes: true,
      ...(isTiered ? { quantity: numberOfMembers } : {}),
      ...(isCouponApplicable(productId) ? { promotion_code: promotionCode.value.id } : {}),
      success_url: successUrl.href,
    })
  } catch (e) {
    console.error('createCheckoutSession error:', e)
  }
  isCreatingSession.value = false
  if (!session) {
    return
  }

  const sessionUrl = new URL(session.url)
  if (clientReferenceId) {
    sessionUrl.searchParams.set('client_reference_id', clientReferenceId)
  }

  window.location.assign(sessionUrl.href)
}

function isCurrentProduct(product) {
  if (!currentSubscriptions.value) return false
  return !!currentSubscriptions.value.find(
    (subscription) =>
      product.priceByInterval?.[subscriptionInterval.value]?.priceId === subscription.price,
  )
}

function getProductFinalPrice(product) {
  const currentProduct = product.priceByInterval?.[subscriptionInterval.value]
  if (!currentProduct) return 0

  let price = currentProduct?.price || 0
  if (product.isTiered) {
    const currentTier =
      currentProduct?.tiers.find((tier) => numberOfMembers.value <= tier.upTo) ||
      currentProduct?.tiers[currentProduct?.tiers.length - 1]
    const pricePerUser = currentTier?.price || 0
    price = pricePerUser * numberOfMembers.value
  }
  const isApplicable = isCouponApplicable(product.id)
  const percentOff = isApplicable ? promotionCode.value?.coupon?.percent_off || 0 : 0
  const finalPrice = percentOff ? parseFloat((price * (1 - percentOff / 100)).toFixed(2)) : price

  return {
    price,
    finalPrice,
    percentOff,
  }
}
</script>

<template>
  <el-dialog
    :model-value="props.isOpen"
    width="clamp(320px, 95vw, 1200px)"
    @close="close"
    destroy-on-close
    class="subscription"
  >
    <template #header>
      <p class="text-xl">{{ t('subscriptionDialog.title') }}</p>
    </template>

    <el-tabs v-model="activeTab" class="edit-tabs">
      <el-tab-pane :label="t('subscriptionDialog.tabs.plans')" name="Plans">
        <el-row :gutter="20">
          <el-col
            class="transition-opacity pointer-events-none"
            :class="areProductsLoaded ? 'opacity-0' : 'opacity-100'"
          >
            <div class="absolute inset-0 flex items-center justify-center">
              <el-icon size="3em" class="animate-spin"><Loading /></el-icon>
            </div>
          </el-col>
          <el-col
            class="transition-opacity"
            :class="areProductsLoaded ? 'opacity-100' : 'opacity-0'"
          >
            <div class="flex flex-col gap-4">
              <div class="text-center">
                <el-button
                  v-for="(intervalName, interval) in intervalNamesByInterval"
                  class="text-base"
                  type="primary"
                  :data-testid="`subscription-interval-button-${interval}`"
                  size="large"
                  :plain="subscriptionInterval !== interval"
                  @click="subscriptionInterval = interval"
                  round
                  >{{ intervalName }}</el-button
                >
              </div>

              <div class="flex gap-4 text-center">
                <el-card
                  class="box-card flex-1 bg-blue-600/5 hover:shadow-lg hover:shadow-blue-600/25"
                >
                  <div
                    class="h-full flex flex-col gap-2 items-center justify-between"
                    :data-testid="`subscription-card-Free`"
                  >
                    <div>
                      <el-icon size="5em">
                        <IconPlanFree />
                      </el-icon>
                      <div class="text-3xl">{{ t('subscriptionDialog.plans.free.title') }}</div>
                      <br />
                      <ul class="flex flex-col gap-1 text-left">
                        <li class="flex gap-2">
                          <el-icon size="1.5em" color="var(--el-color-primary)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.promptBars') }}
                        </li>
                        <li class="flex gap-2">
                          <el-icon size="1.5em" color="var(--el-color-primary)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.unlimitedPromptsPerBar') }}
                        </li>
                        <li class="flex gap-2">
                          <el-icon size="1.5em" color="var(--el-color-primary)"
                            ><SuccessFilled
                          /></el-icon>
                          {{
                            t('subscriptionDialog.plans.features.credits', {
                              credits: getMaxCreditsByPlan('Free'),
                            })
                          }}
                        </li>
                        <li class="flex gap-2">
                          <el-icon size="1.5em" color="var(--el-color-primary)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.accessToLibrary') }}
                        </li>
                        <li class="flex gap-2 line-through">
                          <el-icon size="1.5em" color="var(--el-text-color-disabled)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.promptFolders') }}
                        </li>
                        <li class="flex gap-2 line-through">
                          <el-icon size="1.5em" color="var(--el-text-color-disabled)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.copyPrompts') }}
                        </li>
                        <li class="flex gap-2 line-through">
                          <el-icon size="1.5em" color="var(--el-text-color-disabled)"
                            ><SuccessFilled
                          /></el-icon>
                          {{ t('subscriptionDialog.plans.features.premiumAccess') }}
                        </li>
                      </ul>
                    </div>

                    <div>
                      <div>
                        <span class="text-4xl font-thin">$</span>
                        <span class="text-4xl font-bold">0</span>
                        <span class="text-xl font-thin">
                          / {{ t(`subscriptionDialog.interval.${subscriptionInterval}`) }}</span
                        >
                      </div>
                      <br />
                      <el-button
                        v-if="isLoggedIn"
                        class="text-lg mt-auto"
                        type="primary"
                        size="large"
                        plain
                        text
                        :icon="!currentSubscriptions ? Check : ''"
                      >
                        <template v-if="!currentSubscriptions">
                          {{ t('subscriptionDialog.current') }}
                        </template>
                        <template v-else> {{ t('subscriptionDialog.premiumActivated') }} </template>
                      </el-button>
                      <el-popover
                        v-else
                        placement="top"
                        :width="300"
                        trigger="click"
                        :teleported="false"
                      >
                        <template #reference>
                          <div class="el-dropdown">
                            <el-button :icon="User">{{
                              t('subscriptionDialog.signInToUse')
                            }}</el-button>
                          </div>
                        </template>
                        <SignInSignUpForm />
                      </el-popover>
                    </div>
                  </div>
                </el-card>

                <template v-for="product in products">
                  <el-card
                    v-if="product.priceByInterval?.[subscriptionInterval]"
                    class="box-card flex-1 bg-purple-600/5 hover:shadow-lg hover:shadow-purple-600/25"
                  >
                    <div
                      class="h-full flex flex-col gap-2 items-center justify-between"
                      :data-testid="`subscription-card-${product.name}`"
                    >
                      <div>
                        <span v-if="product.name === 'Team'">
                          <el-icon size="5em">
                            <IconPlanTeam />
                          </el-icon>
                        </span>
                        <span v-if="product.name === 'Premium'">
                          <el-icon size="5em">
                            <IconPlanPremium />
                          </el-icon>
                        </span>
                        <span v-if="product.name === 'Standard'">
                          <el-icon size="5em">
                            <IconPlanStandard />
                          </el-icon>
                        </span>
                        <div class="text-3xl">
                          {{ t(`subscriptionDialog.plans.${cc(product.name)}.title`) }}
                        </div>
                        <br />
                        <ul class="flex flex-col gap-1 text-left">
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t(`subscriptionDialog.plans.features.unlimitedBars`) }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.unlimitedPromptsPerBar') }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{
                              t('subscriptionDialog.plans.features.credits', {
                                credits: getMaxCreditsByPlan(product.name),
                              })
                            }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.accessToLibrary') }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.promptFolders') }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.copyPrompts') }}
                          </li>
                          <li class="flex gap-2">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.premiumAccess') }}
                          </li>
                          <li class="flex gap-2" v-if="product.isTiered">
                            <el-icon size="1.5em" color="var(--el-color-primary)"
                              ><SuccessFilled
                            /></el-icon>
                            {{ t('subscriptionDialog.plans.features.manageTeam') }}
                          </li>
                        </ul>
                      </div>
                      <div>
                        <div class="my-4" v-if="product.isTiered">
                          <h4>{{ t('subscriptionDialog.teamSize') }}</h4>
                          <el-input-number v-model="numberOfMembers" :min="2" />
                        </div>
                        <div>
                          <span v-if="isCouponApplicable(product.id)">
                            <span class="text-2xl font-thin strike-through">
                              ${{ getProductFinalPrice(product).price }}
                            </span>
                            &nbsp;
                          </span>
                          <span class="text-4xl font-thin">$</span>
                          <span class="text-4xl font-bold"
                            >{{ getProductFinalPrice(product).finalPrice }}
                          </span>
                          <span class="text-xl font-thin">
                            / {{ t(`subscriptionDialog.interval.${subscriptionInterval}`) }}
                          </span>
                        </div>
                        <br />
                        <el-button
                          v-if="isLoggedIn"
                          :color="isCurrentProduct(product) ? '' : '#9333ea'"
                          class="text-lg mt-auto"
                          :class="
                            isCurrentProduct(product)
                              ? ''
                              : 'hover:shadow-lg hover:shadow-purple-600'
                          "
                          type="default"
                          size="large"
                          :disabled="isCurrentProduct(product) || isCreatingSession"
                          :icon="isCurrentProduct(product) ? Check : CreditCard"
                          @click="
                            buySubscription(
                              product.id,
                              product.priceByInterval[subscriptionInterval].priceId,
                              product.isTiered,
                              numberOfMembers,
                            )
                          "
                          v-loading.fullscreen.lock="isCreatingSession"
                          >{{
                            isCurrentProduct(product)
                              ? t('subscriptionDialog.current')
                              : t('subscriptionDialog.buyNow')
                          }}
                        </el-button>
                        <el-popover
                          v-else
                          placement="top"
                          :width="300"
                          trigger="click"
                          :teleported="false"
                        >
                          <template #reference>
                            <div class="el-dropdown">
                              <el-button :icon="User">{{
                                t('subscriptionDialog.signInToBuy')
                              }}</el-button>
                            </div>
                          </template>
                          <SignInSignUpForm />
                        </el-popover>
                      </div>
                    </div>
                  </el-card>
                </template>
              </div>
            </div>

            <div class="text-center mt-4">
              <div v-if="promotionCode?.coupon?.percent_off" class="text-center">
                <div v-if="isUserTransactionRestricted()" class="text-red-500">
                  {{ t('subscriptionDialog.coupon.firstTimeOnly') }}
                </div>
                <div v-else class="text-green-500">
                  {{
                    t('subscriptionDialog.coupon.discount', {
                      percent: promotionCode.coupon.percent_off,
                      interval: subscriptionInterval,
                      code: promotionCode.code,
                    })
                  }}
                </div>
              </div>
              <div v-else-if="promotionCode?.isValidCustomCode" class="text-green-500">
                {{ t('subscriptionDialog.coupon.applied', { code: promotionCode.code }) }}
              </div>
              <div v-else>{{ t('subscriptionDialog.coupon.note') }}</div>
              <div class="mt-2 w-1/2 mx-auto flex justify-center">
                <el-input
                  v-model="couponCode"
                  :placeholder="t('subscriptionDialog.coupon.placeholder')"
                  :data-testid="`subscription-coupon-input`"
                />
                <el-button type="primary" class="ml-2" @click="applyCoupon">{{
                  t('subscriptionDialog.coupon.apply')
                }}</el-button>
              </div>
              <div v-if="promoError" class="mt-2 text-red-500">
                {{
                  t(
                    `subscriptionDialog.coupon.errors.${
                      promoError === 'Invalid coupon code' ? 'invalid' : 'alreadyUsed'
                    }`,
                  )
                }}
              </div>
            </div>

            <div v-if="currentSubscriptions" class="text-center mt-4">
              {{
                t('subscriptionDialog.subscription.endsOn', {
                  date: currentSubscriptions[0].current_period_end,
                })
              }}
              <p class="mt-2">
                <el-button @click="manageStripeSubscription">{{
                  t('subscriptionDialog.subscription.manage')
                }}</el-button>
              </p>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane v-if="isLoggedIn" :label="t('subscriptionDialog.tabs.history')" name="History">
        <el-row :gutter="20">
          <el-col>
            <el-table
              :data="getSubscriptionHistory()"
              stripe
              :empty-text="t('subscriptionDialog.history.noData')"
            >
              <el-table-column
                prop="startDateString"
                :label="t('subscriptionDialog.history.start')"
              />
              <el-table-column prop="endDateString" :label="t('subscriptionDialog.history.end')" />
              <el-table-column prop="priceText" :label="t('subscriptionDialog.history.price')" />
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane v-if="canViewTeams" :label="t('subscriptionDialog.tabs.teams')" name="Teams">
        <TeamsManagement
          :user-subscription="userSubscription"
          :existing-member-emails="existingMemberEmails"
          @members-updated="handleMembersUpdated"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">{{ t('subscriptionDialog.close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style>
.subscription .el-card__body {
  height: 100%;
  box-sizing: border-box;
}

.strike-through {
  position: relative;
}
.strike-through:before {
  --color: #ff9900;
  --angle: -20deg;

  position: absolute;
  content: '';
  left: 0;
  top: 50%;
  right: 0;
  border-top: 2px solid var(--color);
  -webkit-transform: rotate(var(--angle));
  -moz-transform: rotate(var(--angle));
  -ms-transform: rotate(var(--angle));
  -o-transform: rotate(var(--angle));
  transform: rotate(var(--angle));
}

/* Responsive design for mobile and narrow screens */
@media (max-width: 768px) {
  .subscription :deep(.el-dialog) {
    width: clamp(320px, 95vw, 600px) !important;
    margin: 3vh auto;
  }

  .subscription :deep(.el-dialog__header) {
    padding: 1rem 1rem 0.5rem;
  }

  .subscription :deep(.el-dialog__body) {
    padding: 0.5rem 1rem;
  }

  .subscription :deep(.el-dialog__footer) {
    padding: 0.5rem 1rem 1rem;
  }

  /* Plan cards layout - stack vertically */
  .subscription .flex.gap-4.text-center {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .subscription .el-card.box-card {
    flex: none;
    max-width: none;
    margin: 0;
    width: 100%;
    max-width: 400px; /* Prevent cards from being too wide on tablets */
  }

  /* Center-align content within each card when stacked */
  .subscription .el-card.box-card .el-card__body {
    display: flex;
    justify-content: center;
    align-items: stretch;
  }

  .subscription .el-card.box-card .h-full.flex.flex-col.gap-2.items-center.justify-between {
    width: 100%;
    text-align: center;
    align-items: center;
  }

  /* Interval selection buttons */
  .subscription .text-center .el-button {
    margin: 0.25rem;
    min-height: 44px;
    font-size: 0.9rem;
  }

  /* Coupon input section - using more specific selectors */
  .subscription .text-center .mt-2.flex.justify-center {
    width: 100% !important;
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
    margin-left: auto;
    margin-right: auto;
  }

  .subscription .text-center .mt-2.flex.justify-center .el-input {
    margin-bottom: 0;
  }

  .subscription .text-center .mt-2.flex.justify-center .el-button {
    margin-left: 0 !important;
    align-self: center;
    min-width: 120px;
  }

  /* Typography adjustments */
  .subscription .text-3xl {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .subscription .text-4xl {
    font-size: 2rem;
  }

  .subscription .text-xl {
    font-size: 1.125rem;
  }

  /* Feature list adjustments */
  .subscription .flex.flex-col.gap-1 {
    gap: 0.5rem;
  }

  .subscription .flex.gap-2 {
    gap: 0.5rem;
    align-items: flex-start;
  }

  /* Icon adjustments */
  .subscription .el-icon[size='5em'] {
    font-size: 3.5em !important;
  }

  /* Team size input */
  .subscription .el-input-number {
    width: 100%;
  }

  /* History table responsiveness */
  .subscription :deep(.el-table) {
    font-size: 0.875rem;
  }

  .subscription :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

/* Extra small screens and side panel compatibility */
@media (max-width: 480px) {
  .subscription :deep(.el-dialog) {
    width: clamp(300px, 98vw, 450px) !important;
    margin: 2vh auto;
  }

  .subscription :deep(.el-dialog__header) {
    padding: 0.75rem 0.75rem 0.25rem;
  }

  .subscription :deep(.el-dialog__body) {
    padding: 0.25rem 0.75rem;
  }

  .subscription :deep(.el-dialog__footer) {
    padding: 0.25rem 0.75rem 0.75rem;
  }

  /* Further typography reduction */
  .subscription .text-3xl {
    font-size: 1.5rem;
  }

  .subscription .text-4xl {
    font-size: 1.75rem;
  }

  .subscription .text-xl {
    font-size: 1rem;
  }

  .subscription .text-lg {
    font-size: 0.9rem;
  }

  /* Smaller icons for very small screens */
  .subscription .el-icon[size='5em'] {
    font-size: 3em !important;
  }

  .subscription .el-icon[size='1.5em'] {
    font-size: 1.25em !important;
  }

  /* Enhanced touch targets */
  .subscription .el-button {
    min-height: 48px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  /* Interval buttons stack on very small screens */
  .subscription .text-center .el-button {
    width: 100%;
    margin: 0.25rem 0;
  }

  /* Plan cards padding reduction */
  .subscription .el-card__body {
    padding: 1rem 0.75rem;
  }

  /* Ensure cards remain centered on very small screens */
  .subscription .el-card.box-card {
    max-width: 350px; /* Slightly smaller max-width for very small screens */
  }

  /* Feature list further optimization */
  .subscription .flex.flex-col.gap-1 {
    gap: 0.375rem;
  }

  .subscription .flex.gap-2 {
    gap: 0.375rem;
  }

  /* Team size input optimization */
  .subscription .my-4 {
    margin: 0.75rem 0;
  }

  /* History table for very small screens */
  .subscription :deep(.el-table) {
    font-size: 0.75rem;
  }

  .subscription :deep(.el-table .el-table__cell) {
    padding: 6px 2px;
  }

  .subscription :deep(.el-table__header) {
    font-size: 0.8rem;
  }
}

/* Chrome extension side panel specific adjustments (around 420px) */
@media (max-width: 450px) and (min-width: 400px) {
  .subscription :deep(.el-dialog) {
    width: clamp(380px, 95vw, 420px) !important;
  }

  /* Optimize for side panel narrow width */
  .subscription .flex.gap-4.text-center {
    gap: 0.75rem;
    align-items: center;
  }

  .subscription .el-card.box-card {
    min-height: auto;
    max-width: 380px; /* Optimized for side panel width */
  }

  /* Coupon input adjustments for side panel */
  .subscription .text-center .mt-2.flex.justify-center {
    width: 100% !important;
    flex-direction: row;
    gap: 0.5rem;
  }

  .subscription .text-center .mt-2.flex.justify-center .el-input {
    flex: 1;
  }

  .subscription .text-center .mt-2.flex.justify-center .el-button {
    flex-shrink: 0;
    min-width: 80px;
  }
}
</style>
