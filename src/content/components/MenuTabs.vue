<script setup>
import { ElMessage } from 'element-plus'
import { doc, getDoc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useBarFiltering } from '@/content/composables/useBarFiltering'
import { useBarOptions } from '@/content/composables/useBarOptions'
import { usePromptBarManagement } from '@/content/composables/usePromptBarManagement'
import { useTabNavigation } from '@/content/composables/useTabNavigation.js'
import { barsRef } from '@/content/firebase'
import { removeFromPluginStorageLocal, setToLocalStorage } from '@/content/localStorage'
import { usePromptStore } from '@/content/stores/promptStore'
import { cc, convertIdToString, processPromptFields, TABS } from '@/content/utils.js'
import { useAppStateStore } from '@/stores/appState'

import iconPlanTeam from './icons/iconPlanTeam.vue'
import LoginRequiredMessage from './LoginRequiredMessage.vue'
import PromptBarDropdown from './PromptBarDropdown.vue'
import PromptBarInformation from './PromptBarInformation.vue'
import SkeletonLoader from './SkeletonLoader.vue'
import SubscriptionRequiredMessage from './SubscriptionRequiredMessage.vue'
import TabHeaderActions from './TabHeaderActions.vue'
import TabContent from './tabs/TabContent.vue'
import TeamDropdown from './TeamDropdown.vue'
import Tooltip from './Tooltip.vue'

const props = defineProps({
  menuHideLevel: {
    type: Number,
    required: true,
  },
  sharedBarId: {
    type: [String, null],
    default: null,
  },
  selectedBar: {
    type: Object,
    default: () => ({}),
  },
  formatFirebaseDate: {
    type: Function,
    required: true,
  },
  btnCopyType: {
    type: String,
    default: '',
  },
  onBarChange: {
    type: Function,
    required: true,
  },
})

const emit = defineEmits([
  'openSubscriptionDialog',
  'openSharePromptBarDialog',
  'openInformationDialog',
  'toggleFavorite',
  'changeMenuHideLevel',
])

const message = (setContent, setType = 'info') => {
  ElMessage.closeAll()
  ElMessage({
    message: setContent,
    type: setType,
  })
}

const { t } = useI18n()
const appState = useAppStateStore()
const {
  activeTab,
  isLoggedIn,
  isTabEmpty,
  hasSubscription,
  userDoc,
  areAllBarsLoaded,
  selectedBarId,
  selectedTeamId,
  canAddMoreFreePromptBars,
  popularBars,
  libraryBars,
  publicBars,
  myPromptBars,
  redirectedPrompt,
  isUserLoaded,
} = storeToRefs(appState)

const localActiveTabName = ref(appState.activeTab)
const { barOptions } = useBarFiltering()

watch(localActiveTabName, (newTab) => {
  handleTabChangeLogic(newTab)
})

watch(
  () => appState.activeTab,
  (storeTab) => {
    localActiveTabName.value = storeTab
  },
)

watch(
  () => selectedBarId.value,
  (newBarId) => {
    if (newBarId) {
      props.onBarChange(newBarId)
    }
  },
)

const { availableTabs, atTabChange, handleTabChangeLogic } = useTabNavigation()

const promptStore = usePromptStore()

const { getBarsForCurrentTab } = useBarOptions()

const { filterMethod, onTagClick, getStyle } = useBarFiltering()

const { editPromptBar, openEditPromptBarDialog, removePromptBar } = usePromptBarManagement()

const getBarById = async (barId) => {
  const barDoc = (await getDoc(doc(barsRef, barId))).data() ?? {}
  barDoc.id = barId
  barDoc.promptMenu = barDoc.prompt_menu ? JSON.parse(barDoc.prompt_menu, convertIdToString) : []
  barDoc.linkMenu = barDoc.links ? JSON.parse(barDoc.links, convertIdToString) : []

  return barDoc
}

const setBarFromLocalStorage = async (id, tab, shared = false, promptId = null) => {
  selectedBarId.value = id

  const bar = await getBarById(id)
  if (!selectedBarId.value && !bar?.isPublic) {
    selectedBarId.value = null
    if (typeof appState.selectedBar !== 'undefined') {
      appState.selectedBar = null
    }
    removeFromPluginStorageLocal('redirectedPrompt')
    message(t('messages.privateBarAccessDenied'), 'warning')
    return
  }

  appState.activeTab = tab

  if (tab === TABS.Popular) {
    popularBars.value = [bar]
  } else if (tab === TABS.Library) {
    libraryBars.value = [bar]
  } else if (tab === TABS.Public) {
    publicBars.value = [bar]
  } else if (tab === TABS.MyPrompts) {
    myPromptBars.value = [bar]
  }

  barOptions.value = getBarsForCurrentTab()

  if (promptId && Array.isArray(bar.promptMenu)) {
    const prompt = bar.promptMenu.find((p) => p.id === promptId)
    if (prompt) {
      promptStore.openPromptPopup(
        {
          ...prompt,
          processedPrompt: processPromptFields(prompt),
        },
        tab,
      )
    }
  }

  removeFromPluginStorageLocal('redirectedPrompt')
}

watch(
  () => redirectedPrompt.value,
  (newValue, oldValue) => {
    if (newValue && newValue !== oldValue) {
      setBarFromLocalStorage(
        redirectedPrompt.value.barId,
        redirectedPrompt.value.activeTabName,
        false,
        redirectedPrompt.value.promptId,
      )
    }
  },
)

watch(
  () => appState.activeTab,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      setToLocalStorage('activeTab', newValue)
    }
  },
)

function handleTeamChangeForBars(teamId) {
  appState.areTeamBarsLoaded = false
  appState.teamBars = []
  barOptions.value = []
  appState.selectedBarId = ''
  appState.selectedBar = null
  appState.updateActiveBarIdForTab(TABS.Team, '')
}

function handleOpenTeamManagement() {
  appState.setSelectedTeamIdForDialog(selectedTeamId.value)
  emit('openSubscriptionDialog', 'Teams')
}
</script>

<template>
  <el-tabs
    v-model="localActiveTabName"
    type="border-card"
    class="relative z-0 menu-tabs dark:bg-neutral-900/70"
    :class="menuHideLevel >= 1 ? 'hide-header' : ''"
  >
    <el-tab-pane
      v-for="(tabName, tabId) in availableTabs"
      :key="tabId"
      :label="t(`tabs.${cc(tabName)}`)"
      :name="tabName"
      :lazy="true"
    >
      <template #label>
        <Tooltip
          :content="
            '<div style=\'max-width: 300px; text-align: center;\'>' +
            t(`tabs.description.${cc(tabName)}`) +
            '</div>'
          "
          effect="light"
          placement="bottom"
          :show-after="1000"
          raw-content
        >
          <span>{{ t(`tabs.${cc(tabName)}`) }}</span>
        </Tooltip>
      </template>

      <div class="flex items-start">
        <div v-if="tabName == localActiveTabName" class="flex flex-1 flex-col m-0 p-2">
          <template v-if="!isUserLoaded || sharedBarId">
            <SkeletonLoader />
          </template>

          <template
            v-else-if="
              !isLoggedIn &&
              [TABS.MyPrompts, TABS.Favorites, TABS.Library, TABS.Team].includes(tabName)
            "
          >
            <LoginRequiredMessage />
          </template>

          <template v-else-if="[TABS.Favorites, TABS.Team].includes(tabName) && !hasSubscription">
            <SubscriptionRequiredMessage
              :tabName="t(`tabs.${cc(tabName)}`)"
              @openSubscriptionDialog="$emit('openSubscriptionDialog')"
            />
          </template>

          <template
            v-else-if="[TABS.Team].includes(tabName) && hasSubscription && !userDoc?.teams?.length"
          >
            <div class="flex flex-col justify-center items-center h-full">
              <div class="h-[36px] flex items-center justify-center">
                <p>{{ t('messages.noTeamMembership') }}</p>
              </div>
              <div class="min-h-[32px] flex items-center justify-center">
                <el-button
                  type="info"
                  plain
                  @click="$emit('openSubscriptionDialog')"
                  class="text-purple-400 focus:bg-purple-400 hover:!bg-purple-500 hover:!text-white"
                  >{{ t('subscription.buySubscription') }}
                </el-button>
              </div>
            </div>
          </template>

          <template v-else>
            <div
              class="bar-container flex justify-center flex-wrap gap-y-2 sm:h-[36px] mb-2 items-center elem-to-hide z-10"
              :class="{ 'hide-bar': menuHideLevel === 2 }"
            >
              <div class="w-auto min-w-[2em] flex justify-end">
                <div
                  class="flex gap-1"
                  v-if="!isTabEmpty && selectedBar?.id"
                  data-testid="prompt-bar-info-share-buttons"
                >
                  <PromptBarInformation
                    v-if="!isTabEmpty && selectedBar?.id"
                    :title="selectedBar?.name"
                    :description="selectedBar?.description"
                    :links="selectedBar?.linkMenu"
                    :tags="selectedBar?.tags"
                    @openInformationDialog="$emit('openInformationDialog', $event)"
                  />

                  <div v-if="[TABS.MyPrompts, TABS.Public].includes(tabName)">
                    <Tooltip
                      v-if="selectedBarId"
                      class="box-item"
                      effect="light"
                      :content="t('messages.addBookmarkToSelectedPromptBar')"
                      placement="bottom"
                    >
                      <el-button
                        plain
                        icon="Share"
                        @click="$emit('openSharePromptBarDialog')"
                        class="!px-2"
                        data-testid="prompt-bar-share-button"
                      >
                      </el-button>
                    </Tooltip>
                  </div>

                  <div v-if="userDoc && !isTabEmpty && selectedBar?.id">
                    <Tooltip
                      class="box-item"
                      effect="light"
                      :content="
                        hasSubscription
                          ? t('messages.toggleFavorite')
                          : t('messages.buySubscriptionToUseFavorites')
                      "
                      placement="bottom"
                    >
                      <el-button
                        :disabled="!hasSubscription"
                        link
                        @click="$emit('toggleFavorite')"
                        class="me-1 !bg-yellow-200/20 hover:!bg-yellow-300/10"
                        data-testid="toggle-favorite-button"
                      >
                        <el-icon size="26" class="text-yellow-500 hover:text-yellow-400">
                          <slot name="favorite-icon"></slot>
                        </el-icon>
                      </el-button>
                    </Tooltip>
                  </div>
                </div>
              </div>

              <div class="flex grow min-w-[300px]">
                <div class="flex bg-neutral-500/5 rounded-md mx-1 w-full">
                  <div v-if="tabName === TABS.Team && (userDoc?.teams || []).length > 1">
                    <TeamDropdown :on-team-change="handleTeamChangeForBars" />
                  </div>

                  <div v-if="tabName === TABS.Team" class="mx-1">
                    <Tooltip class="box-item" effect="light" content="Team" placement="bottom">
                      <el-button
                        plain
                        data-testid="manage-team-button"
                        class="no-bg"
                        @click="handleOpenTeamManagement"
                      >
                        <el-icon size="18"><iconPlanTeam /></el-icon>
                        <span class="hidden xl:block">Team</span>
                      </el-button>
                    </Tooltip>
                  </div>

                  <PromptBarDropdown
                    :tabName="tabName"
                    :filterMethod="filterMethod"
                    :formatFirebaseDate="formatFirebaseDate"
                    :getStyle="getStyle"
                    :onTagClick="onTagClick"
                    :onBarChange="onBarChange"
                  />
                </div>
              </div>
              <TabHeaderActions
                :tabName="tabName"
                :selectedBarId="selectedBarId"
                :btnCopyType="btnCopyType"
                :isTabMyPrompts="tabName === TABS.MyPrompts"
                :isUserDoc="!!userDoc"
                :canAddMoreFreePromptBars="canAddMoreFreePromptBars"
                :hasSubscription="hasSubscription"
                :selectedBar="selectedBar"
                :activeTab="activeTab"
                @editPromptBar="editPromptBar"
                @removePromptBar="removePromptBar"
                @openAddPromptBarDialog="appState.openAddPromptBarDialog(false, true)"
                @openEditPromptBarDialog="openEditPromptBarDialog"
              />
            </div>

            <div class="inline-flex justify-center items-center w-full gap-1 min-h-[32px]">
              <TabContent
                :tabName="tabName"
                @editPromptBar="editPromptBar"
                @removePromptBar="removePromptBar"
                @openAddPromptBarDialog="appState.openAddPromptBarDialog(false, true)"
                @openSubscriptionDialog="$emit('openSubscriptionDialog', $event)"
                @openEditPromptBarDialog="openEditPromptBarDialog"
              />
            </div>
          </template>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
