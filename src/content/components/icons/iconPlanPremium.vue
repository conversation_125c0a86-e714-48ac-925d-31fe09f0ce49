<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.0"
    viewBox="0 0 512.000000 512.000000"
    preserveAspectRatio="xMidYMid meet"
    :width="width"
    :height="height"
  >
    <title>{{ title }}</title>
    <g
      transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
      :fill="color"
      stroke="none"
    >
      <path
        d="M2499 4401 c-14 -5 -38 -17 -54 -27 -16 -11 -189 -260 -425 -613 -219 -328 -408 -606 -421 -618 -62 -56 -174 -70 -244 -30 -22 13 -239 173 -483 355 -244 183 -457 342 -475 352 -41 25 -111 26 -157 1 -36 -19 -80 -88 -80 -125 0 -25 288 -1905 295 -1923 6 -19 4204 -19 4210 0 7 18 295 1898 295 1923 0 37 -44 106 -80 125 -45 25 -116 24 -156 0 -18 -11 -238 -174 -491 -363 -252 -190 -474 -350 -492 -356 -73 -26 -166 -8 -220 41 -13 12 -203 290 -421 618 -308 461 -406 601 -434 618 -39 25 -125 36 -167 22z"
      />
      <path
        d="M452 1143 c3 -254 4 -270 25 -310 27 -51 82 -96 132 -112 55 -16 3847 -16 3902 0 50 16 105 61 132 112 21 40 22 56 25 310 l3 267 -2111 0 -2111 0 3 -267z"
      />
    </g>
  </svg>
</template>
<style scoped></style>
