<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg :width="width" :height="height" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
    <title>{{ title }}</title>
    <g id="iconAddBookmark">
      <path
        :fill="color"
        d="M384,92.52c-52.84-5.08-96,12.18-125,57-28.28,43.83-29.21,90.68-2.63,135.5,28.36,47.81,72.64,66.35,127.53,61.36.66,1.25.25,3,.25,4.58,0,40.48-.05,81,.08,121.44,0,8.79-2.66,15.78-10.94,19.53-8.12,3.67-15,1-21.43-4.61Q288,431.14,224,375c-3.06-2.7-4.74-2.89-7.94-.06Q151.91,431.58,87.47,487.91c-6.15,5.41-12.93,7.51-20.63,4.08-7.86-3.5-11-9.94-11-18.44q.09-191.4.08-382.81c0-39.81,30.85-71.87,70.64-72.26q93.51-.91,187,0c39,.4,70.23,32.41,70.56,70.4C384.17,90.2,384,91.5,384,92.52Z"
      />
      <path
        :fill="color"
        d="M366.45,128.05c-50.5-.07-91.88,41-91.76,91,.13,50.88,40.92,91.82,91.49,91.81,50.22,0,91-41.15,90.91-91.79C457,169.15,416.17,128.12,366.45,128.05Zm39.19,109.27c-6.09,1.15-12.26.39-18.39.44-2.32,0-3.21.54-3.12,3,.19,5,.13,10,0,15-.19,10.2-7.54,18-17.22,18.38-10.25.41-18.23-6.47-19.1-16.88a112.49,112.49,0,0,1-.11-15.47c.18-3.41-1.05-4.19-4.19-4-4.64.24-9.3.21-13.94,0-10.93-.47-18.53-8.41-18.31-18.9.22-10.1,8.15-17.58,18.93-17.76,4.82-.08,9.64-.15,14.45,0,2.49.1,3.11-.77,3-3.13-.16-4.65-.12-9.32,0-14,.24-11.54,7.72-19.38,18.34-19.34s18.08,8,18.18,19.47c0,2,0,4,0,6,0,10.93,0,10.89,11,11,3.64,0,7.3-.38,10.92.46a18.36,18.36,0,0,1,14.54,17.86A18.08,18.08,0,0,1,405.64,237.32Z"
      />
    </g>
  </svg>
</template>
<style scoped></style>
