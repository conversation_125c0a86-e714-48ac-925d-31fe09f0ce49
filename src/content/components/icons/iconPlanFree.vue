<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.0"
    viewBox="0 0 512.000000 512.000000"
    preserveAspectRatio="xMidYMid meet"
    :width="width"
    :height="height"
  >
    <title>{{ title }}</title>
    <g
      transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
      :fill="color"
      stroke="none"
    >
      <path
        d="M1532 4950 c-285 -39 -505 -248 -558 -531 -30 -154 1 -330 81 -464 43 -72 138 -171 219 -228 l59 -42 -549 -5 c-488 -4 -553 -7 -586 -22 -91 -41 -160 -120 -184 -212 -10 -37 -14 -125 -14 -326 0 -266 1 -276 23 -314 12 -22 38 -49 57 -60 35 -21 46 -21 1098 -24 l1062 -2 0 320 0 320 320 0 320 0 0 -320 0 -320 1063 2 c1051 3 1062 3 1097 24 19 11 45 38 57 60 22 38 23 48 23 314 0 201 -4 289 -14 326 -24 92 -93 171 -184 212 -33 15 -98 18 -586 22 l-549 5 59 42 c81 57 176 156 219 228 154 257 112 598 -100 810 -125 125 -283 188 -470 188 -258 0 -433 -107 -609 -372 -200 -304 -301 -558 -313 -788 -3 -62 -9 -113 -13 -113 -4 0 -10 48 -12 108 -8 170 -52 313 -170 547 -86 172 -213 358 -300 441 -143 136 -347 201 -546 174z m215 -332 c111 -41 239 -201 353 -441 96 -203 152 -403 130 -462 -34 -89 -407 29 -701 223 -178 119 -243 211 -243 352 -1 93 14 144 60 206 48 64 83 92 152 120 76 31 170 32 249 2z m1838 8 c68 -18 103 -37 148 -83 138 -137 140 -342 6 -488 -92 -99 -373 -260 -569 -326 -158 -52 -263 -58 -280 -14 -22 59 34 259 130 461 177 373 347 509 565 450z"
      />
      <path
        d="M322 1403 l3 -998 24 -53 c29 -64 112 -143 178 -168 45 -18 99 -19 881 -22 l832 -3 0 1121 0 1120 -960 0 -960 0 2 -997z"
      />
      <path
        d="M2880 1280 l0 -1121 833 3 c781 3 835 4 880 22 66 25 149 104 178 168 l24 53 3 998 2 997 -960 0 -960 0 0 -1120z"
      />
    </g>
  </svg>
</template>
<style scoped></style>
