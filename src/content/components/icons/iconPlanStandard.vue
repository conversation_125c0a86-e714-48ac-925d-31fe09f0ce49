<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    version="1.0"
    viewBox="0 0 512.000000 512.000000"
    preserveAspectRatio="xMidYMid meet"
    :width="width"
    :height="height"
  >
    <title>{{ title }}</title>
    <g
      transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
      :fill="color"
      stroke="none"
    >
      <path
        d="M1960 4983 c-325 -51 -589 -208 -779 -463 -107 -143 -187 -336 -212 -509 -16 -116 -7 -344 19 -441 101 -382 360 -674 715 -808 369 -138 799 -75 1107 163 77 60 199 188 249 264 198 293 252 650 150 994 -27 91 -117 267 -178 348 -176 232 -441 394 -725 444 -80 14 -281 19 -346 8z"
      />
      <path
        d="M3532 2530 c-221 -58 -398 -207 -492 -414 -45 -99 -63 -186 -63 -306 0 -127 19 -213 74 -326 188 -386 671 -530 1044 -312 40 24 104 76 150 122 129 130 200 291 212 476 14 224 -68 424 -238 584 -91 86 -212 152 -330 180 -92 22 -263 20 -357 -4z m328 -411 c95 -46 159 -123 186 -228 30 -118 -9 -246 -100 -329 -73 -67 -125 -87 -226 -87 -76 0 -92 3 -152 33 -81 40 -139 102 -169 181 -28 75 -24 205 10 269 44 85 118 150 204 178 67 22 181 13 247 -17z"
      />
      <path
        d="M1870 2309 c-381 -29 -808 -147 -1153 -321 -117 -59 -274 -154 -328 -199 -68 -57 -143 -173 -175 -272 l-27 -82 -1 -370 -1 -370 1073 0 1074 0 238 422 238 422 -14 53 c-22 78 -29 272 -14 365 10 65 47 191 74 254 6 13 -10 20 -95 38 -293 62 -588 82 -889 60z"
      />
      <path
        d="M2897 1279 c-20 -32 -360 -636 -381 -676 l-16 -32 262 5 263 6 129 -226 c72 -124 131 -224 133 -223 1 2 94 166 206 364 l204 362 -36 5 c-20 3 -61 8 -91 12 -212 25 -453 164 -592 342 -28 37 -55 71 -59 76 -4 4 -14 -2 -22 -15z"
      />
      <path
        d="M4475 1244 c-45 -64 -151 -166 -218 -212 -63 -44 -182 -102 -247 -122 -55 -17 -55 -18 -112 -116 -32 -54 -58 -103 -58 -109 0 -6 69 -133 154 -283 l154 -273 55 93 c30 51 88 151 130 223 l75 130 261 -2 c144 -1 261 1 261 4 0 9 -405 723 -410 723 -3 0 -23 -26 -45 -56z"
      />
    </g>
  </svg>
</template>
<style scoped></style>
