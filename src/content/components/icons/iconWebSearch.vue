<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" :width="width" :height="height">
    <title>{{ title }}</title>
    <path
      d="M437.02,74.982C388.667,26.628,324.38,0,256,0S123.333,26.628,74.98,74.982C26.628,123.333,0,187.619,0,256    s26.628,132.667,74.98,181.019C123.333,485.372,187.62,512,256,512s132.667-26.628,181.02-74.981    C485.372,388.667,512,324.381,512,256S485.372,123.333,437.02,74.982z M189.199,30.06c-14.03,12.407-26.905,28.847-38.154,48.989    c-25.137,45.005-39.547,103.888-40.958,166.752H20.633C25.01,143.562,94.844,58.005,189.199,30.06z M20.633,266.199h89.455    c1.409,62.864,15.82,121.747,40.957,166.752c11.249,20.141,24.124,36.581,38.154,48.989    C94.844,453.995,25.01,368.438,20.633,266.199z M245.801,488.222c-28.67-4.38-55.68-27.138-76.948-65.218    c-23.482-42.043-36.975-97.397-38.364-156.805h115.312V488.222z M245.801,245.801H130.489    c1.388-59.407,14.882-114.761,38.364-156.805c21.267-38.08,48.278-60.838,76.948-65.218V245.801z M266.199,23.778    c20.95,3.223,41.176,16.34,59.025,38.435c21.227,26.277,37.516,63.015,47.108,106.244c1.22,5.499,6.667,8.968,12.167,7.748    c5.499-1.22,8.968-6.667,7.748-12.167c-13.234-59.647-38.201-106.455-69.457-133.983    c94.359,27.942,164.199,113.502,168.576,215.745H266.199V23.778z M266.199,488.222V266.199h115.312    c-1.388,59.407-14.882,114.761-38.364,156.805C321.88,461.084,294.869,483.842,266.199,488.222z M322.801,481.94    c14.03-12.407,26.906-28.847,38.154-48.989c25.137-45.005,39.547-103.888,40.958-166.752h89.455    C486.99,368.438,417.156,453.995,322.801,481.94z"
      :fill="color"
    />
  </svg>
</template>
<style scoped></style>
