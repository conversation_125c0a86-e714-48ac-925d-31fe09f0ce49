<script setup>
import { computed } from 'vue'

const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || '- AI Prompt Lab -'

const gradientId = computed(() => `linear-gradient-${Math.random().toString(36).substr(2, 9)}`)
</script>
<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" :width="width" :height="height">
    <defs>
      <linearGradient
        :id="gradientId"
        x1="256"
        y1="91.25"
        gradientTransform="translate(0 -0.82)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#2ce1ff" />
        <stop offset="0.27" stop-color="#31c4fc" />
        <stop offset="0.76" stop-color="#3894f8" />
        <stop offset="1" stop-color="#3b82f6" />
      </linearGradient>
    </defs>
    <title>{{ title }}</title>
    <g id="noBg">
      <path
        :fill="color"
        d="M208.35,76.32c-8-.24-13.45-10-9.93-18,2.72-6.12,7.82-8.28,14.15-8.33,14.56-.1,73.87-.13,89,0,6,0,11.44,4.77,12.89,11.3,1.19,5.38-2.19,12.16-7.37,14.28-2.3.94-4.9.66-7.36.72C291.72,76.5,213.93,76.5,208.35,76.32Z"
      />
      <path
        :fill="color"
        d="M417.35,317c-8.23-16.29-103.83-172.57-113.44-187.67A24.32,24.32,0,0,1,300.12,115V93c.05-2-.37-3-2.64-3h-9.19v68.62a9.56,9.56,0,0,0,1.1,2.65c14.14,22.89,75.94,123.25,92.68,150.54,3.32,5.41,6.69,10.85,8.62,16.95a11.69,11.69,0,0,1-9.76,15c-4.05.38-8.12.63-12.19.69q-12.3.18-24.6.24a98.73,98.73,0,0,1-15.26,22.23q-2.44,2.67-5.07,5.15c15.17.06,44.61.1,61.74-.42,11.49-.35,20.92-4.63,27.82-13.61C423.16,345.32,424.56,331.26,417.35,317Z"
      />
      <path
        :fill="color"
        d="M167.7,344.71q-12.22-.07-24.44-.24c-4.07-.06-8.14-.31-12.19-.69a11.69,11.69,0,0,1-9.76-15c1.93-6.1,5.3-11.54,8.62-16.95,16.74-27.29,78.54-127.65,92.68-150.54a9.66,9.66,0,0,0,1.09-2.62V90h-9.18c-2.15,0-2.69,1-2.64,3v22a24.32,24.32,0,0,1-3.79,14.37C198.48,144.44,102.88,300.72,94.65,317c-7.21,14.25-5.81,28.31,4,41.05,6.9,9,16.33,13.26,27.82,13.61,17,.52,46.26.48,61.5.43A98.81,98.81,0,0,1,167.7,344.71Z"
      />
      <path
        :fill="`url(#${gradientId})`"
        d="M342.79,292.68a85.7,85.7,0,0,0-11-34.45v0l-3.2-5.14-50.3-80.88a8.77,8.77,0,0,1-1.34-4.67V93a3,3,0,0,0-3-3H238a3,3,0,0,0-3,3v74.58a8.77,8.77,0,0,1-1.34,4.67l-50.47,81.12-2.9,4.68,0,0a1.42,1.42,0,0,0-.11.18,86,86,0,0,0-11.43,43c0,46.1,36.1,83.79,81.69,86.63,1.84.11,3.66.17,5.47.17C306.79,388,347.8,343.78,342.79,292.68Zm-86.79,76c-37.52,0-67.94-30.64-67.94-68.45s30.42-68.44,67.94-68.44,67.94,30.64,67.94,68.44S293.52,368.64,256,368.64Z"
      />
      <path
        :fill="color"
        d="M271.7,326.39h-7.24a3.06,3.06,0,0,1-2-.62,4.05,4.05,0,0,1-1.17-1.54l-4.79-12.72H231.7l-4.8,12.72a3.93,3.93,0,0,1-1.12,1.48,3,3,0,0,1-2,.68h-7.24l22.84-57.6h9.48ZM254,304.79l-7.92-21a56.75,56.75,0,0,1-2-6.2c-.3,1.25-.62,2.41-1,3.46s-.67,2-1,2.78l-7.92,21Z"
      />
      <path :fill="color" d="M285.81,326.39h-9.4v-57.6h9.4Z" />
    </g>
  </svg>
</template>
<style scoped></style>
