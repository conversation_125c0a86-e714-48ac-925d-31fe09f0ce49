<script setup>
const props = defineProps(['data', 'width', 'height', 'color', 'title'])
const width = props.width || '1em'
const height = props.height || '1em'
const color = props.color || 'currentColor'
const title = props.title || ''
</script>
<template>
  <svg
    id="iconBookmark"
    :width="width"
    :height="height"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 512 512"
  >
    <title>{{ title }}</title>
    <path
      d="M290.83,61c26.36,0,49.55,0,70.9,0,12.71,0,22,7.45,24.77,19.89a29.64,29.64,0,0,1,.45,6c0,109.42,0,225.57,0,341l-25.59-15.81c-25.32-15.64-51.5-31.81-77.2-47.75a51.74,51.74,0,0,0-56.2-.09C199.06,382.24,169.6,400.41,141.1,418l-15.77,9.73c.2-35.95.36-71,.5-102.56v-.66h1.07v-45q0-28.64,0-57.26c0-44.28,0-90.06.06-135.06,0-16.32,9.92-26.06,26.45-26.06l62.1,0,75.34,0m0-45q-68.71,0-137.44.05c-41.19,0-71.37,29.82-71.45,71q-.19,96.21,0,192.41H80.84c0,15.16.06,30.32,0,45.48q-.35,76.46-.8,152.93c0,7.32,2.44,13,9.18,16.34a14.56,14.56,0,0,0,6.58,1.62c3.71,0,7.25-1.48,10.8-3.68q72.54-44.84,145.1-89.62a8.17,8.17,0,0,1,4.31-1.49,8.48,8.48,0,0,1,4.44,1.58q72.26,44.82,144.63,89.44c3.71,2.3,7.4,3.82,11.19,3.82a15,15,0,0,0,7.53-2.11c6.34-3.63,8.19-9.46,8.19-16.49Q431.88,282.14,432,87A72.16,72.16,0,0,0,430.4,71.1c-7.37-32.81-34.85-55-68.61-55q-35.48,0-71,0Z"
      :fill="color"
    />
  </svg>
</template>
<style scoped></style>
