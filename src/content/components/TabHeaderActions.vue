<script setup>
import { storeToRefs } from 'pinia'

import { useAppStateStore } from '../../stores/appState'
import CopyPromptButton from './CopyPromptButton.vue'
import PromptBarActions from './PromptBarActions.vue'

const props = defineProps({
  tabName: String,
  selectedBarId: [String, Number],
  btnCopyType: String,
  isTabMyPrompts: Boolean,
  isUserDoc: Boolean,
  canAddMoreFreePromptBars: Boolean,
  hasSubscription: Boolean,
  selectedBar: Object,
  activeTab: String,
})

const appState = useAppStateStore()
const { activeTab, isTabEmpty, hasSubscription, selectedBarId } = storeToRefs(appState)

defineEmits([
  'editPromptBar',
  'removePromptBar',
  'openAddPromptBarDialog',
  'openEditPromptBarDialog',
])
</script>

<template>
  <div class="w-auto min-w-[2em] flex gap-1">
    <template v-if="isUserDoc && !isTabMyPrompts">
      <CopyPromptButton
        :btnCopyType="btnCopyType"
        @copy="$emit('openEditPromptBarDialog', selectedBarId, 'copyTab')"
        @mouseenter="$emit('update:btnCopyType', 'primary')"
        @mouseleave="$emit('update:btnCopyType', '')"
      />
    </template>
    <PromptBarActions
      v-if="isUserDoc"
      :tab-name="activeTab"
      :is-tab-empty="isTabEmpty"
      :selected-bar-id="selectedBarId"
      :selected-bar="selectedBar"
      :can-add-more-free-prompt-bars="canAddMoreFreePromptBars"
      :has-subscription="hasSubscription"
      @edit-prompt-bar="$emit('editPromptBar', ...arguments)"
      @remove-prompt-bar="$emit('removePromptBar', ...arguments)"
      @open-add-prompt-bar-dialog="$emit('openAddPromptBarDialog')"
    />
  </div>
</template>
