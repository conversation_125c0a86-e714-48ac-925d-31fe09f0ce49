<script setup>
import { onMounted, ref, watch } from 'vue'

import { useDebounce } from '../composables/useDebounce'

const props = defineProps(['chatSite'])

const emits = defineEmits(['sendScrapsAndSiteName'])

const question = ref()
const answer = useDebounce('', 3000)
const questionANDanswer = ref()

const site = ref()
const siteID = ref()
const prevSiteID = ref()

onMounted(() => {
  let currentPage = ''
  // listen for changes
  setInterval(function () {
    if (currentPage != location.href) {
      currentPage = location.href
      siteID.value = currentPage.split('/').pop()
      if (siteID.value.indexOf('?model=text') > -1) {
        // gpt - set empty id on new chat
        siteID.value = ''
      }
      if (siteID.value.indexOf('chat') > -1) {
        // bard - set empty id on new chat
        siteID.value = ''
      }
      if (siteID.value.indexOf('sid=') > -1 || siteID.value.indexOf('cvid=') > -1) {
        // extract id from bing
        siteID.value = siteID.value.split('sid=').pop()
        siteID.value = siteID.value.split('cvid=').pop()
        siteID.value = siteID.value.split('&')[0]
      }
      if (siteID.value.indexOf('?s=') > -1) {
        // extract id from perplexity
        siteID.value = siteID.value.split('?s=')[0]
      }
      // console.log('siteID: ', siteID.value)
    }
  }, 500)
})

const getOutsideSelectorFromChatSite = (sel) => {
  let outsideSelector = document.querySelector(props.chatSite.selector[sel])
  if (!outsideSelector && props.chatSite.selectorAlters && props.chatSite.selectorAlters[sel]) {
    for (let i = 0; i < props.chatSite.selectorAlters[sel].length; i++) {
      outsideSelector = document.querySelector(props.chatSite.selectorAlters[sel][i])
    }
  }
  if (!outsideSelector && props.chatSite.selectorShadows && props.chatSite.selectorShadows[sel]) {
    for (let i = 0; i < props.chatSite.selectorShadows[sel].length; i++) {
      outsideSelector = outsideSelector.shadowRoot.querySelector(
        props.chatSite.selectorShadows[sel][i],
      )
    }
  }
  return outsideSelector
}

const getScrapsOnAction = (textarea, button) => {
  // console.log('getScrapsOnAction: ', textarea, button)
  button.addEventListener('click', (event) => {
    // console.log('Search button pressed')
    getScrapsForSite()
  })

  if (site.value === 'bard') return // enter triggering button click
  textarea.addEventListener('keyup', function (e) {
    if ((e.key === 'Enter' || e.keyCode === 13) && !e.shiftKey) {
      // console.log('Enter pressed')
      getScrapsForSite()
    }
  })
}
const getScrapsForSite = () => {
  setTimeout(() => {
    if (site.value === 'bing') {
      getScrapsBing()
    } else {
      getScraps()
    }
  }, 1000)
}

const getScraps = () => {
  let checkQuestion = setInterval(function () {
    let checkElem = getOutsideSelectorFromChatSite('lastQuestion')
    // console.log('appElem: ', checkElem)
    if (checkElem) {
      clearInterval(checkQuestion)
      setTimeout(() => {
        question.value = checkElem.innerHTML
      }, 500)
    } else {
      // console.log('... generating question ', checkElem)
    }
  }, 500)

  let checkAnswer = setInterval(function () {
    let checkElem = getOutsideSelectorFromChatSite('isAnswerGenerated')
    // console.log('appElem: ', checkElem)
    if (checkElem) {
      if (props.chatSite.name === 'Bing' && !checkElem.disabled) return
      clearInterval(checkAnswer)
      // console.log('Answer generated ', checkElem)
      setTimeout(() => {
        // console.log('answer: ', getOutsideSelectorFromChatSite('lastAnswer'))
        answer.value = getOutsideSelectorFromChatSite('lastAnswer').innerHTML
      }, 1000)
      return
    }
    // console.log('... generating answer ...')
  }, 1000)
}

watch(
  () => siteID.value,
  (newVal, oldVal) => {
    let countDown = 100
    let counter = 0
    // console.log('watch site: ', newVal, oldVal)
    // if (site.value === 'bing') {
    let checkingInputs = setInterval(function () {
      let checkTextarea = getOutsideSelectorFromChatSite('textarea')
      let checkButton = getOutsideSelectorFromChatSite('button')
      // console.log('Inputs ready: ', checkTextarea, checkButton)
      if (checkTextarea && checkButton) {
        clearInterval(checkingInputs)
        getScrapsOnAction(checkTextarea, checkButton)
      }
      counter++
      if (counter > countDown) {
        clearInterval(checkingInputs)
      }
    }, 500)

    prevSiteID.value = oldVal
    // console.log('watch siteID: ', siteID.value, prevSiteID.value)
  },
)

watch(
  () => answer.value,
  (newVal, oldVal) => {
    // console.log('watch answer: ', newVal, oldVal)
    questionANDanswer.value = {
      question: question.value,
      answer: answer.value,
    }
    // console.log('questionANDanswer: ', questionANDanswer.value, siteID.value, prevSiteID.value)
    emits('sendScrapsAndSiteName', questionANDanswer.value, siteID.value)
  },
)

const button = () => {
  // console.log('chat: ', getOutsideSelectorFromChatSite('chat'))
  // console.log('question: ', getOutsideSelectorFromChatSite('lastQuestion'))
  // console.log('answer: ', getOutsideSelectorFromChatSite('lastAnswer'))
  // console.log('textarea: ', getOutsideSelectorFromChatSite('textarea'))
  // console.log('button: ', getOutsideSelectorFromChatSite('button'))
  // console.log('isAnswerGenerated: ', getOutsideSelectorFromChatSite('isAnswerGenerated'))
  // console.log('question: ', questionANDanswer.value?.question)
  // console.log('answer: ', questionANDanswer.value?.answer)
}
</script>
<template>
  <!-- <el-button @click="button">check</el-button> -->
</template>
<style scoped></style>
