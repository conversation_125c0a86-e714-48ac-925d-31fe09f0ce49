<script setup>
import { Arrow<PERSON>ownBold, ArrowUpBold } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

const { t } = useI18n()
const appState = useAppStateStore()
const { barSortFieldName, barSortDirection } = storeToRefs(appState)

const props = defineProps(['fieldName', 'fieldLabel'])

const fieldLabel = computed(() =>
  props.fieldLabel?.length ? t(`sort.${props.fieldLabel}`) : t(`sort.${props.fieldName}`),
)
</script>

<template>
  <el-button @click="appState.sortBarsByField(props.fieldName)">
    {{ fieldLabel }}
    <el-icon v-if="barSortFieldName === props.fieldName" class="ml-1">
      <ArrowUpBold v-if="barSortDirection === 1" /><ArrowDownBold v-else />
    </el-icon>
  </el-button>
</template>
