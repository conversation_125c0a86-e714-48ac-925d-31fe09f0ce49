<template>
  <div class="sites-grid-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <div class="tab-navigation-content">
        <TabNavigation
          :active-tab="activeTab"
          :ordered-tabs="orderedTabs"
          :is-reordering="isReordering"
          :dragged-tab-id="draggedTabId"
          @update:active-tab="activeTab = $event"
          @cycle-tab-order="cycleTabOrder"
          @tab-drag-start="handleTabDragStart"
          @tab-drag-over="handleTabDragOver"
          @tab-drop="handleTabDrop"
          @tab-drag-enter="handleTabDragEnter"
          @tab-drag-leave="handleTabDragLeave"
          @tab-drag-end="handleTabDragEnd"
          @tab-keydown="handleTabKeydown"
        />
      </div>
    </div>

    <!-- Search AI Tab Content -->
    <div
      v-if="activeTab === 'search'"
      class="tab-content"
      role="tabpanel"
      id="search-panel"
      aria-labelledby="search-tab"
    >
      <div class="search-container">
        <div class="search-input-wrapper">
          <SearchInput
            v-model="searchQuery"
            :isDarkTheme="isDarkTheme"
            :selectedTags="selectedTags"
            :availableTags="allAvailableTags"
            @tag-selected="handleTagSelected"
            @search-change="handleSearchChange"
          />

          <el-tooltip
            :content="
              viewMode === 'card'
                ? t('sitesGrid.viewToggle.switchToList')
                : t('sitesGrid.viewToggle.switchToCard')
            "
            placement="top"
          >
            <el-button
              @click="toggleViewMode"
              class="view-toggle-btn"
              size="large"
              :icon="viewMode === 'card' ? List : Grid"
              circle
            />
          </el-tooltip>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingSites" class="loading-container">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">{{ t('sitesGrid.loading.sites') }}</div>
      </div>

      <!-- Fallback Data Status - Removed for cleaner UI -->
      <!-- Technical status messages are now only logged to console for debugging -->

      <!-- Error State - Only show actual errors, not technical status -->
      <el-alert
        v-if="sitesLoadError && !isLoadingSites && !usingFallbackData"
        :title="t('sitesGrid.error.loadFailed')"
        :description="sitesLoadError"
        type="warning"
        :closable="false"
        class="error-alert"
        show-icon
      />

      <!-- Sites Content -->
      <template v-if="!isLoadingSites">
        <TagFilter
          v-model:selectedTags="selectedTags"
          :sites="effectiveSitesWithTags"
          :allTags="effectiveAllTags"
          :isDarkTheme="isDarkTheme"
        />

        <el-alert
          v-if="selectedTags.length > 0 || searchQuery"
          :title="
            t('sitesGrid.results.found', {
              count: filteredSites.length,
              total: effectiveSitesWithTags.length,
            })
          "
          type="info"
          :closable="false"
          class="results-info"
        />
      </template>

      <!-- Sites Content -->
      <template v-if="!isLoadingSites">
        <!-- Card View -->
        <div v-if="viewMode === 'card'" class="sites-grid" :class="gridColsClass">
          <SiteCard
            v-for="site in filteredSites"
            :key="site.url"
            :site="site"
            :isDarkTheme="isDarkTheme"
            :selectedTags="selectedTags"
            :isSearchTab="true"
            @click="openLink"
            @favorite-changed="(wasAdded) => onFavoriteChanged(wasAdded, true)"
            @tag-selected="handleTagSelected"
          />
        </div>

        <!-- List View -->
        <div v-else class="sites-list">
          <SiteList
            v-for="site in filteredSites"
            :key="site.url"
            :site="site"
            :isDarkTheme="isDarkTheme"
            :selectedTags="selectedTags"
            :isSearchTab="true"
            @click="openLink"
            @favorite-changed="(wasAdded) => onFavoriteChanged(wasAdded, true)"
            @tag-selected="handleTagSelected"
          />
        </div>

        <el-empty
          v-if="filteredSites.length === 0 && (selectedTags.length > 0 || searchQuery)"
          :description="t('sitesGrid.empty.noResults')"
          class="no-results"
        >
          <template #image>
            <el-icon :size="64" color="var(--el-color-info)">
              <Search />
            </el-icon>
          </template>
          <el-button type="primary" @click="clearFilters">{{
            t('sitesGrid.actions.clearFilters')
          }}</el-button>
        </el-empty>
      </template>
    </div>

    <!-- My AI Tab Content -->
    <div
      v-if="activeTab === 'myai'"
      class="tab-content"
      role="tabpanel"
      id="myai-panel"
      aria-labelledby="myai-tab"
    >
      <MyAIFavorites
        :isDarkTheme="isDarkTheme"
        :viewMode="viewMode"
        :selectedTags="selectedTags"
        :gridColsClass="gridColsClass"
        @item-click="handleCardClick"
        @favorite-changed="onFavoriteChanged"
        @browse-tools="activeTab = 'search'"
        @tag-selected="handleTagSelected"
        @clear-filters="clearFilters"
        @toggle-view-mode="toggleViewMode"
      />
    </div>
  </div>
</template>

<script setup>
import { Grid, List, Search } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAISitesManagement } from '../composables/useAISitesManagement.js'
import { useMenuControlState } from '../composables/useMenuControlState.js'
import MyAIFavorites from './MyAIFavorites.vue'
import SearchInput from './SearchInput.vue'
import SiteCard from './SiteCard.vue'
import SiteList from './SiteList.vue'
import TabNavigation from './TabNavigation.vue'
import TagFilter from './TagFilter.vue'

const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
})

// Get side panel visibility state
const { sidePanelVisible, initializeState } = useMenuControlState()

const { t } = useI18n()

// Use the AI Sites Management composable for Firebase integration
const { sitesWithTags, allTags, isLoading: isLoadingSites } = useAISitesManagement()

// Error state for fallback handling
const sitesLoadError = ref(null)

// Local fallback data for when Firebase is not available
const fallbackSitesWithTags = ref([])
const fallbackAllTags = ref([])
const usingFallbackData = ref(false)

// Enhanced loading with fallback to static data
const loadSitesDataWithFallback = async () => {
  try {
    sitesLoadError.value = null
    usingFallbackData.value = false

    // The useAISitesManagement composable handles Firebase loading automatically
    // We just need to wait a bit and check if data is available
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Check if we have data from the composable, if not try static fallback
    if (sitesWithTags.value.length === 0 && !isLoadingSites.value) {
      console.warn('⚠️ No sites data available from Firebase, loading static fallback')
      try {
        const { sitesWithTags: staticSites, allTags: staticTags } = await import(
          '../sitesWithTags.js'
        )
        fallbackSitesWithTags.value = staticSites
        fallbackAllTags.value = staticTags
        usingFallbackData.value = true
        // Clear any previous error since fallback data loaded successfully
        sitesLoadError.value = null

        console.info('ℹ️ Using static fallback data - Firebase data not available')
      } catch (fallbackError) {
        console.error('❌ Failed to load fallback data:', fallbackError)
        sitesLoadError.value = 'Unable to load AI tools data. Please refresh the page.'
      }
    } else if (sitesWithTags.value.length > 0) {
    }
  } catch (error) {
    console.error('❌ Error in loadSitesDataWithFallback:', error)
    sitesLoadError.value = error.message
  }
}

// Tab order management - make reactive to language changes
const tabsConfig = computed(() => [
  { id: 'search', label: t('sitesGrid.tabs.searchAI') },
  { id: 'myai', label: t('sitesGrid.tabs.myAI') },
])

// Default order: Search AI first
const tabsOrder = ref(['search', 'myai'])

const orderedTabs = computed(() => {
  return tabsOrder.value.map((id) => tabsConfig.value.find((tab) => tab.id === id)).filter(Boolean)
})

const activeTab = ref('search')
const selectedTags = ref([])
const searchQuery = ref('')

// Tab drag-and-drop state
const draggedTabId = ref(null)
const draggedTabIndex = ref(null)

// Reorder button state
const isReordering = ref(false)

// View toggle state
const viewMode = ref('card') // 'card' or 'list'

// Load preferences from localStorage and sites data
onMounted(async () => {
  // Initialize side panel state
  initializeState()

  // Load sites data with fallback handling
  // The useAISitesManagement composable automatically handles Firebase loading
  // but we can provide additional fallback logic
  await loadSitesDataWithFallback()

  // Load tab order preference
  const savedTabOrder = localStorage.getItem('aiPromptManager_tabOrder')
  if (savedTabOrder) {
    try {
      const parsedOrder = JSON.parse(savedTabOrder)
      if (Array.isArray(parsedOrder) && parsedOrder.length === 2) {
        tabsOrder.value = parsedOrder
      } else {
        // Handle legacy boolean format
        const isMyAIFirst = savedTabOrder === 'myai-first'
        tabsOrder.value = isMyAIFirst ? ['myai', 'search'] : ['search', 'myai']
      }
    } catch {
      // Handle legacy boolean format
      const isMyAIFirst = savedTabOrder === 'myai-first'
      tabsOrder.value = isMyAIFirst ? ['myai', 'search'] : ['search', 'myai']
    }
  }

  // Load view mode preference
  const savedViewMode = localStorage.getItem('aiPromptManager_viewMode')
  if (savedViewMode && (savedViewMode === 'card' || savedViewMode === 'list')) {
    viewMode.value = savedViewMode
  }
})

// No cleanup needed - removed unused debounceTimer reference

// Save preferences to localStorage
watch(
  tabsOrder,
  (newOrder) => {
    localStorage.setItem('aiPromptManager_tabOrder', JSON.stringify(newOrder))
  },
  { deep: true },
)

watch(viewMode, (newViewMode) => {
  localStorage.setItem('aiPromptManager_viewMode', newViewMode)
})

// Watch for Firebase data becoming available
watch(
  () => sitesWithTags.value.length,
  (newLength) => {
    if (newLength > 0 && usingFallbackData.value) {
      usingFallbackData.value = false
      sitesLoadError.value = null
    }
  },
)

// Cycle through tab orders
const cycleTabOrder = async () => {
  if (isReordering.value) return

  isReordering.value = true

  try {
    // Define all possible tab arrangements
    const possibleOrders = [
      ['search', 'myai'], // Search AI first
      ['myai', 'search'], // My AI first
    ]

    // Find current order index
    const currentOrderString = JSON.stringify(tabsOrder.value)
    let currentIndex = possibleOrders.findIndex(
      (order) => JSON.stringify(order) === currentOrderString,
    )

    // If current order is not found, default to first arrangement
    if (currentIndex === -1) {
      currentIndex = 0
    }

    // Move to next arrangement (cycle back to start if at end)
    const nextIndex = (currentIndex + 1) % possibleOrders.length
    tabsOrder.value = [...possibleOrders[nextIndex]]

    // Brief delay for visual feedback
    await new Promise((resolve) => setTimeout(resolve, 300))
  } finally {
    isReordering.value = false
  }
}

// Dynamic grid layout based on side panel visibility
const gridColsClass = computed(() => {
  return sidePanelVisible.value ? 'side-panel-visible' : 'side-panel-hidden'
})

// Computed properties that use either Firebase data or fallback data
const effectiveSitesWithTags = computed(() =>
  usingFallbackData.value ? fallbackSitesWithTags.value : sitesWithTags.value,
)

const effectiveAllTags = computed(() =>
  usingFallbackData.value ? fallbackAllTags.value : allTags.value,
)

// Simply return all available sites without ID-based filtering
const orderedSites = computed(() => effectiveSitesWithTags.value)

// Get all available tags for autocomplete
const allAvailableTags = computed(() => {
  const tags = new Set()
  effectiveSitesWithTags.value.forEach((site) => {
    if (Array.isArray(site.tags)) {
      site.tags.forEach((tag) => {
        if (tag) tags.add(tag)
      })
    }
  })
  return Array.from(tags)
})

const filteredSites = computed(() => {
  let filtered = orderedSites.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (site) =>
        site.name?.toLowerCase().includes(query) ||
        site.description?.toLowerCase().includes(query) ||
        (Array.isArray(site.tags) && site.tags.some((tag) => tag.toLowerCase().includes(query))),
    )
  }

  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(
      (site) =>
        Array.isArray(site.tags) && selectedTags.value.some((tag) => site.tags.includes(tag)),
    )
  }

  // Sort by selected tags priority
  if (selectedTags.value.length > 0) {
    filtered.sort((a, b) => {
      // Calculate priority score for each site
      const getTagScore = (site) => {
        let score = 0
        if (Array.isArray(site.tags)) {
          selectedTags.value.forEach((selectedTag, index) => {
            const tagPosition = site.tags.indexOf(selectedTag)
            if (tagPosition !== -1) {
              // Higher score for selected tags that appear earlier in site's tag list
              // Also higher score for tags selected earlier (lower index)
              score += (10 - tagPosition) * (selectedTags.value.length - index)
            }
          })
        }
        return score
      }

      return getTagScore(b) - getTagScore(a) // Sort descending by score
    })
  }

  return filtered
})

const clearFilters = () => {
  selectedTags.value = []
  searchQuery.value = ''
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'card' ? 'list' : 'card'
}

// Event handlers for SearchInput component and tag clicks
const handleTagSelected = (tag) => {
  // Toggle tag selection - same behavior as TagFilter.vue
  const newSelectedTags = [...selectedTags.value]
  const index = newSelectedTags.indexOf(tag)

  if (index > -1) {
    // Tag is already selected, remove it
    newSelectedTags.splice(index, 1)
  } else {
    // Tag is not selected, add it
    newSelectedTags.push(tag)
  }

  selectedTags.value = newSelectedTags
}

const handleSearchChange = () => {
  // Handle search query changes if needed
  // Currently, the v-model binding handles the searchQuery update
}

const openLink = (url) => {
  window.open(url, '_blank')
}

const handleCardClick = (url) => {
  openLink(url)
}

const onFavoriteChanged = (wasAdded = false, isFromSearchTab = false) => {
  // Po dodaniu z Search AI przełącz na My AI
  if (wasAdded && isFromSearchTab) {
    activeTab.value = 'myai'
  }
}

// Tab drag-and-drop handlers
const handleTabDragStart = ({ tabId, index }) => {
  draggedTabId.value = tabId
  draggedTabIndex.value = index
}

const handleTabDragOver = () => {
  // Event handling is now done in TabNavigation component
}

const handleTabDragEnter = () => {
  // Event handling is now done in TabNavigation component
}

const handleTabDragLeave = () => {
  // Event handling is now done in TabNavigation component
}

const handleTabDrop = ({ targetTabId, targetIndex, draggedTabIndex: draggedIndex }) => {
  if (draggedTabId.value && draggedTabId.value !== targetTabId) {
    const newOrder = [...tabsOrder.value]
    const draggedTab = newOrder[draggedIndex]

    // Remove dragged tab from its current position
    newOrder.splice(draggedIndex, 1)

    // Insert at new position
    newOrder.splice(targetIndex, 0, draggedTab)

    // Update the order
    tabsOrder.value = newOrder
    localStorage.setItem('aiPromptManager_tabOrder', JSON.stringify(newOrder))
  }

  draggedTabId.value = null
  draggedTabIndex.value = null
}

const handleTabDragEnd = () => {
  draggedTabId.value = null
  draggedTabIndex.value = null
}

// Keyboard navigation for tabs
const handleTabKeydown = () => {
  // Tab switching is now handled in TabNavigation component
  // This function is called after the tab switch has been processed
}
</script>

<style scoped>
.sites-grid-container {
  width: 100%;
}

.tab-navigation {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  gap: 0.75rem;
}

.tab-navigation-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.tab-content {
  width: 100%;
}

.search-container {
  margin-bottom: 1rem;
  width: 100%;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.view-toggle-btn {
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.view-toggle-btn:hover {
  transform: scale(1.05);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1rem;
}

.loading-text {
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.loading-subtext {
  color: var(--el-text-color-placeholder);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.error-alert {
  margin-bottom: 1rem;
}

.results-info {
  margin-bottom: 1rem;
}

.no-results {
  padding: 2rem 1rem;
}

.sites-grid {
  @apply grid gap-2 w-full;
}

/* Default grid layout (4 columns) */
.sites-grid {
  @apply grid-cols-4;
}

/* Side panel visible - use fewer columns for better fit */
.sites-grid.side-panel-visible {
  @apply grid-cols-2;
}

/* Side panel hidden - use more columns to utilize space */
.sites-grid.side-panel-hidden {
  @apply grid-cols-4;
}

.sites-grid.drag-enabled {
  user-select: none;
}

/* List View Styles */
.sites-list {
  @apply flex flex-col gap-2 w-full;
}

/* Element Plus dialog footer spacing */
.dialog-footer {
  @apply flex gap-2 justify-end;
}

/* Responsive grid layout */
@media (max-width: 1024px) {
  .sites-grid.side-panel-visible {
    @apply grid-cols-2;
  }

  .sites-grid.side-panel-hidden {
    @apply grid-cols-3;
  }
}

@media (max-width: 768px) {
  .sites-grid.side-panel-visible {
    @apply grid-cols-1;
  }

  .sites-grid.side-panel-hidden {
    @apply grid-cols-2;
  }

  .tab-navigation-content {
    @apply flex flex-col gap-2 items-stretch w-full;
  }

  .search-container {
    width: 100%;
    margin-bottom: 1rem;
  }

  .search-input-wrapper {
    flex-direction: row;
    gap: 0.75rem;
    width: 100%;
    align-items: center;
  }

  .view-toggle-btn {
    flex-shrink: 0;
    min-height: 44px;
    min-width: 44px;
  }
}

@media (max-width: 480px) {
  .sites-grid.side-panel-visible,
  .sites-grid.side-panel-hidden {
    @apply grid-cols-1;
  }

  /* Enhanced mobile layout for very small screens */

  .search-input-wrapper {
    gap: 0.5rem;
    flex-wrap: nowrap;
  }

  .view-toggle-btn {
    min-height: 48px;
    min-width: 48px;
    flex-shrink: 0;
  }
}
</style>
