<template>
  <div class="localStorage-standalone" :class="{ 'dark-theme': isDarkTheme }">
    <!-- Search and Actions Bar -->
    <div class="actions-bar">
      <el-input
        v-model="searchQuery"
        :placeholder="t('localStoragePanel.search.placeholder')"
        clearable
        class="search-input"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>

      <div class="action-buttons">
        <!-- Language Switcher -->
        <el-tooltip :content="t('localStoragePanel.controls.language.tooltip')" placement="top">
          <el-dropdown @command="switchLanguage" trigger="click">
            <el-button size="default" :icon="Switch">
              {{ currentLanguageLabel }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="en" :disabled="currentLocale === 'en'">
                  {{ t('localStoragePanel.controls.language.english') }}
                </el-dropdown-item>
                <el-dropdown-item command="pl" :disabled="currentLocale === 'pl'">
                  {{ t('localStoragePanel.controls.language.polish') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>

        <!-- Theme Toggle -->
        <el-tooltip :content="t('localStoragePanel.controls.theme.tooltip')" placement="top">
          <el-button @click="toggleTheme" size="default" :icon="isDarkTheme ? Sunny : Moon">
            {{
              isDarkTheme
                ? t('localStoragePanel.controls.theme.light')
                : t('localStoragePanel.controls.theme.dark')
            }}
          </el-button>
        </el-tooltip>

        <!-- Divider -->
        <el-divider direction="vertical" />

        <el-button type="primary" @click="exportData" :icon="Download" size="default">
          {{ t('localStoragePanel.actions.export') }}
        </el-button>

        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="false"
          accept=".json"
          @change="handleFileSelect"
          class="import-upload"
        >
          <el-button type="success" :icon="Upload" size="default">
            {{ t('localStoragePanel.actions.import') }}
          </el-button>
        </el-upload>

        <el-popconfirm
          :title="t('localStoragePanel.confirmations.clearAll')"
          @confirm="clearAllData"
          :width="300"
        >
          <template #reference>
            <el-button type="danger" :icon="Delete" size="default">
              {{ t('localStoragePanel.actions.clearAll') }}
            </el-button>
          </template>
        </el-popconfirm>
      </div>
    </div>

    <!-- Tabbed Interface -->
    <div class="tabs-container">
      <el-tabs
        v-model="activeTab"
        type="border-card"
        class="localStorage-tabs"
        :class="{ 'dark-theme': isDarkTheme }"
      >
        <el-tab-pane :label="t('localStoragePanel.tabs.applicationState')" name="applicationState">
          <LocalStorageTable
            :data="filteredApplicationStateData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.userPreferences')" name="userPreferences">
          <LocalStorageTable
            :data="filteredUserPreferencesData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.userData')" name="userData">
          <LocalStorageTable
            :data="filteredUserData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.componentState')" name="componentState">
          <LocalStorageTable
            :data="filteredComponentStateData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.byComponents')" name="byComponents">
          <ComponentGroupedTable
            :data="filteredData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.allItems')" name="allItems">
          <LocalStorageTable
            :data="filteredData"
            :isDarkTheme="isDarkTheme"
            @view-item="viewItem"
            @delete-item="deleteItem"
          />
        </el-tab-pane>

        <el-tab-pane :label="t('localStoragePanel.tabs.aiManagement')" name="aiManagement">
          <AIManagement :isDarkTheme="isDarkTheme" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- View Item Dialog -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="t('localStoragePanel.viewDialog.title')"
      width="clamp(500px, 90vw, 800px)"
      destroy-on-close
      class="view-item-dialog"
      :class="{ 'dark-theme': isDarkTheme }"
    >
      <div v-if="selectedItem" class="view-item-content">
        <div class="item-info">
          <p>
            <strong>{{ t('localStoragePanel.viewDialog.key') }}</strong> {{ selectedItem.key }}
          </p>
          <p>
            <strong>{{ t('localStoragePanel.viewDialog.type') }}</strong>
            {{ t(`localStoragePanel.types.${selectedItem.type}`) }}
          </p>
          <p>
            <strong>{{ t('localStoragePanel.viewDialog.size') }}</strong>
            {{ formatSize(selectedItem.size) }}
          </p>
        </div>

        <div class="item-value">
          <p>
            <strong>{{ t('localStoragePanel.viewDialog.value') }}</strong>
          </p>
          <el-input
            v-model="selectedItem.formattedValue"
            type="textarea"
            :rows="15"
            readonly
            class="value-textarea"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ArrowDown,
  Delete,
  Download,
  Moon,
  Search,
  Sunny,
  Switch,
  Upload,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import AIManagement from './AIManagement.vue'
import ComponentGroupedTable from './ComponentGroupedTable.vue'
import LocalStorageTable from './LocalStorageTable.vue'

const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const { t, locale } = useI18n()

// Theme management
const isDarkTheme = ref(props.isDarkTheme)
const currentLocale = ref(locale.value)

// Initialize theme from localStorage or system preference
const initializeTheme = () => {
  const savedTheme = localStorage.getItem('localStorage-panel-theme')
  if (savedTheme !== null) {
    const isThemeDark = savedTheme === 'dark'
    isDarkTheme.value = isThemeDark
    applyThemeToDocument(isThemeDark)
  } else {
    // Use system preference
    const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    isDarkTheme.value = systemDark
    applyThemeToDocument(systemDark)
  }
}

// Apply theme to document for Element Plus
const applyThemeToDocument = (dark) => {
  const htmlElement = document.documentElement
  if (dark) {
    htmlElement.classList.add('dark')
  } else {
    htmlElement.classList.remove('dark')
  }
}

// Initialize language from localStorage
const initializeLanguage = () => {
  const savedLang = localStorage.getItem('lang')
  if (savedLang && (savedLang === 'en' || savedLang === 'pl')) {
    currentLocale.value = savedLang
    locale.value = savedLang
  }
}

// Computed properties for UI
const currentLanguageLabel = computed(() => {
  return currentLocale.value === 'en' ? 'EN' : 'PL'
})

// Theme toggle function
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  const themeValue = isDarkTheme.value ? 'dark' : 'light'
  localStorage.setItem('localStorage-panel-theme', themeValue)
  applyThemeToDocument(isDarkTheme.value)
}

// Language switch function
const switchLanguage = (lang) => {
  currentLocale.value = lang
  locale.value = lang
  localStorage.setItem('lang', lang)
}

// Watch for prop changes from parent component
watch(
  () => props.isDarkTheme,
  (newValue) => {
    if (newValue !== isDarkTheme.value) {
      isDarkTheme.value = newValue
      applyThemeToDocument(newValue)
    }
  },
)

// Reactive data
const activeTab = ref('applicationState')
const searchQuery = ref('')
const localStorageData = ref([])
const viewDialogVisible = ref(false)
const selectedItem = ref(null)
const uploadRef = ref(null)

// Data categorization functions
const isApplicationStateKey = (key) => {
  const appStateKeys = [
    'activeTab',
    'All',
    'Popular',
    'Public',
    'My Prompts',
    'Library',
    'Favorites',
  ]
  return appStateKeys.includes(key)
}

const isUserPreferencesKey = (key) => {
  const prefKeys = [
    'lang',
    'barLangPopular',
    'barLangLibrary',
    'barSortFieldName',
    'barSortDirection',
    'aiPromptManager_viewMode',
    'aiPromptManager_tabOrder',
  ]
  // Also include any keys that start with known preference patterns
  const prefPatterns = ['theme', 'view', 'sort', 'lang', 'locale']
  return (
    prefKeys.some((prefKey) => key === prefKey || key.startsWith(prefKey)) ||
    prefPatterns.some((pattern) => key.toLowerCase().includes(pattern))
  )
}

const isUserDataKey = (key) => {
  const userDataKeys = ['ai-tools-favorites', 'ai-tools-myai']
  return userDataKeys.some((userKey) => key === userKey || key.startsWith(userKey))
}

const isComponentStateKey = (key) => {
  return (
    key.startsWith('component_state_') ||
    key.startsWith('menuControlState_') ||
    key.includes('_version')
  )
}

// Computed properties for filtered data
const filteredData = computed(() => {
  return filterDataBySearch(localStorageData.value)
})

const filteredApplicationStateData = computed(() => {
  const appStateData = localStorageData.value.filter((item) => isApplicationStateKey(item.key))
  return filterDataBySearch(appStateData)
})

const filteredUserPreferencesData = computed(() => {
  const userPrefData = localStorageData.value.filter((item) => isUserPreferencesKey(item.key))
  return filterDataBySearch(userPrefData)
})

const filteredUserData = computed(() => {
  const userData = localStorageData.value.filter((item) => isUserDataKey(item.key))
  return filterDataBySearch(userData)
})

const filteredComponentStateData = computed(() => {
  const componentData = localStorageData.value.filter((item) => isComponentStateKey(item.key))
  return filterDataBySearch(componentData)
})

// Helper function for search filtering
const filterDataBySearch = (data) => {
  if (!searchQuery.value) return data

  const query = searchQuery.value.toLowerCase()
  return data.filter(
    (item) => item.key.toLowerCase().includes(query) || item.preview.toLowerCase().includes(query),
  )
}

// Methods
const loadLocalStorageData = () => {
  const data = []

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      const value = localStorage.getItem(key)
      const item = processLocalStorageItem(key, value)
      data.push(item)
    }
  }

  localStorageData.value = data.sort((a, b) => a.key.localeCompare(b.key))
}

const processLocalStorageItem = (key, value) => {
  let type = 'string'
  let formattedValue = value
  let preview = value

  try {
    const parsed = JSON.parse(value)
    if (Array.isArray(parsed)) {
      type = 'array'
      formattedValue = JSON.stringify(parsed, null, 2)
      preview = `[${parsed.length} items]`
    } else if (typeof parsed === 'object' && parsed !== null) {
      type = 'object'
      formattedValue = JSON.stringify(parsed, null, 2)
      const keys = Object.keys(parsed)
      preview = `{${keys.length} keys}`
    } else if (typeof parsed === 'number') {
      type = 'number'
      formattedValue = value
      preview = value
    } else if (typeof parsed === 'boolean') {
      type = 'boolean'
      formattedValue = value
      preview = value
    }
  } catch (e) {
    // Keep as string if not valid JSON
    if (value.length > 50) {
      preview = value.substring(0, 50) + '...'
    }
  }

  return {
    key,
    value,
    formattedValue,
    preview,
    type,
    size: new Blob([value]).size,
  }
}

const viewItem = (item) => {
  selectedItem.value = item
  viewDialogVisible.value = true
}

const deleteItem = (key) => {
  localStorage.removeItem(key)
  loadLocalStorageData()
  ElMessage.success(t('localStoragePanel.messages.itemDeleted'))
}

// Utility function for formatting file sizes
const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const clearAllData = () => {
  localStorage.clear()
  loadLocalStorageData()
  ElMessage.success(t('localStoragePanel.messages.allCleared'))
}

const exportData = () => {
  const data = {}
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      data[key] = localStorage.getItem(key)
    }
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `localStorage-backup-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success(t('localStoragePanel.messages.exportSuccess'))
}

const handleFileSelect = (file) => {
  if (!file || !file.raw) {
    ElMessage.error(t('localStoragePanel.messages.importError'))
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target.result)
      if (typeof data !== 'object' || data === null) {
        ElMessage.error(t('localStoragePanel.messages.invalidJson'))
        return
      }
      importData(data)
    } catch (error) {
      ElMessage.error(t('localStoragePanel.messages.invalidJson'))
    }
  }
  reader.onerror = () => {
    ElMessage.error(t('localStoragePanel.messages.importError'))
  }
  reader.readAsText(file.raw)
}

const importData = (data) => {
  try {
    let importedCount = 0
    Object.entries(data).forEach(([key, value]) => {
      if (typeof key === 'string' && typeof value === 'string') {
        localStorage.setItem(key, value)
        importedCount++
      }
    })

    if (importedCount > 0) {
      loadLocalStorageData()
      ElMessage.success(t('localStoragePanel.messages.importSuccess'))
    } else {
      ElMessage.warning(t('localStoragePanel.messages.noData'))
    }
  } catch (error) {
    console.error('Import error:', error)
    ElMessage.error(t('localStoragePanel.messages.importError'))
  }
}

// Lifecycle
onMounted(() => {
  initializeTheme()
  initializeLanguage()
  loadLocalStorageData()
})
</script>

<style scoped>
.localStorage-standalone {
  padding: 20px;
  max-width: 100%;
  background: var(--el-bg-color-page);
}

.actions-bar {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
}

.search-input {
  flex: 1;
  min-width: 250px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons .el-divider--vertical {
  height: 20px;
  margin: 0 8px;
}

.import-upload {
  display: inline-block;
}

.tabs-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  overflow: hidden;
}

.localStorage-tabs {
  border: none;
}

.localStorage-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color);
}

.localStorage-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 16px;
}

.localStorage-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.localStorage-tabs :deep(.el-tab-pane) {
  padding: 0;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.view-item-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item-info {
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border: 1px solid var(--el-border-color);
}

.item-info p {
  margin: 4px 0;
  font-size: 14px;
}

.item-value {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* Dark theme styles */
.localStorage-standalone.dark-theme {
  background: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

.localStorage-standalone.dark-theme .actions-bar {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

.localStorage-standalone.dark-theme .tabs-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

.localStorage-tabs.dark-theme :deep(.el-tabs__header) {
  background: var(--el-fill-color-darker);
  border-bottom: 1px solid var(--el-border-color);
}

.localStorage-tabs.dark-theme :deep(.el-tabs__item) {
  color: var(--el-text-color-regular);
}

.localStorage-tabs.dark-theme :deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

.localStorage-tabs.dark-theme :deep(.el-tabs__item:hover) {
  color: var(--el-color-primary-light-3);
}

.view-item-dialog.dark-theme :deep(.el-dialog) {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
}

.view-item-dialog.dark-theme .item-info {
  background: var(--el-fill-color-darker);
  border: 1px solid var(--el-border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .localStorage-standalone {
    padding: 10px;
  }

  .actions-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-buttons .el-divider--vertical {
    display: none;
  }

  .search-input {
    min-width: auto;
  }

  .localStorage-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 8px;
  }

  .localStorage-tabs :deep(.el-tabs__item) {
    padding: 0 12px;
    font-size: 14px;
  }
}

/* Medium screen adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
  .action-buttons {
    gap: 8px;
  }

  .action-buttons .el-button {
    font-size: 13px;
    padding: 8px 12px;
  }
}

/* Table responsive adjustments */
@media (max-width: 1024px) {
  .localStorage-tabs :deep(.el-table__cell) {
    padding: 8px 6px;
  }
}

/* Loading and error states */
.localStorage-standalone :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

.localStorage-standalone.dark-theme :deep(.el-loading-mask) {
  background-color: rgba(0, 0, 0, 0.8);
}
</style>
