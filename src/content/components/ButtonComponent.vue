<template>
  <div>
    <button class="btn" @click="handleClick"><slot>Przycisk</slot> ({{ localCounter }})</button>
    <p v-if="showGlobalCounter">Licznik globalny: {{ counter }}</p>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  counter: { type: Number, default: 0 },
  showGlobalCounter: { type: Boolean, default: true },
})
const emit = defineEmits(['button-clicked'])
const localCounter = ref(0)

const handleClick = () => {
  localCounter.value++
  emit('button-clicked', localCounter.value)
}
</script>

<style scoped>
.btn {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
}
.btn:hover {
  background-color: #45a049;
}
</style>
