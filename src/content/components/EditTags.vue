<script setup>
import { Close, Plus, Rank } from '@element-plus/icons-vue'
import { computed, nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps(['dynamicTags'])
const emits = defineEmits(['saveTags'])

const data = ref(props.dynamicTags)
// console.log('tags data: ', data.value)

const { t } = useI18n()

const addNewTagName = 'New tag'

const dataTags = computed(() => {
  let d = []
  data.value.forEach((item) => {
    d.push({ label: item })
  })
  d = [...d, { label: addNewTagName, type: 'add' }]
  return d
})

const tagBtnRef = ref('')
const inputRef = ref('')
const input = ref('')
const isNewTag = ref(false)

const allowDrag = (draggingNode) => {
  return !draggingNode.data.type
}

const allowDrop = (draggingNode, dropNode, dropType) => {
  // console.log('allowDrop: ', draggingNode, dropNode, dropType)
  if (dropNode.data.type) return false
  return dropType !== 'inner'
}

const handleDrop = () => {
  saveTags()
}

const openInput = () => {
  isNewTag.value = true
  nextTick(() => {
    inputRef.value.input.focus()
  })
}

const confirmNewTag = (enterNext) => {
  if (input.value === '') {
    isNewTag.value = false
    return
  }
  //   console.log(input.value)
  add(input.value.replace(/\s/g, ''))
  isNewTag.value = false
  input.value = ''

  if (!enterNext) return
  nextTick(() => {
    tagBtnRef.value.ref.focus()
  })
}
const cancelInput = () => {
  isNewTag.value = false
  input.value = ''
}

const add = (name) => {
  data.value = [...data.value, name]
  saveTags()
}

const remove = (n, d) => {
  // console.log('remove: ', n, n.id, d, data.value, dataTags.value)
  let index = dataTags.value.findIndex((item) => item.$treeNodeId === n.id)
  if (index < 0) return
  dataTags.value.splice(index, 1)
  saveTags()
}

const saveTags = () => {
  // console.log('saveTags: ', data.value, dataTags.value)
  let index = dataTags.value.length - 1
  dataTags.value.splice(index, 1)
  let d = []
  dataTags.value.forEach((item, index) => {
    d = [...d, item.label]
  })
  data.value = d
  // console.log('Save data: ', data.value)
  emits('saveTags', data.value)
}
</script>

<template>
  <el-text class="mb-2 font-bold w-full">{{ t('bar.tags') }}:</el-text>
  <el-tree
    style="max-width: 800px"
    :allow-drop="allowDrop"
    :allow-drag="allowDrag"
    :data="dataTags"
    draggable
    @node-drop="handleDrop"
    node-key="id"
    data-testid="editTagsTree"
  >
    <template #default="{ node, data }">
      <span v-if="data.type" class="custom-tree-node">
        <el-input
          v-if="isNewTag"
          ref="inputRef"
          v-model="input"
          size="small"
          @keydown.enter="confirmNewTag('enterNext')"
          style="max-width: 100px"
          @blur="confirmNewTag"
          @keydown.escape="cancelInput"
          @keydown.space.prevent
          :placeholder="t('btn.enterTab')"
        />
        <el-button
          v-else
          ref="tagBtnRef"
          type="success"
          size="small"
          :icon="Plus"
          plain
          @click="openInput"
          >{{ t('btn.newTag') }}</el-button
        >
      </span>
      <span
        v-else
        class="custom-tree-node flex gap-2 items-center p-[.222rem] rounded bg-blue-500/5 border border-blue-500/20 group"
      >
        <el-icon class="opacity-0 group-hover:opacity-100"><Rank /></el-icon>
        <el-text class="text-xs">{{ node.label }}</el-text>
        <el-button
          type="danger"
          :icon="Close"
          circle
          text
          size="small"
          @click="remove(node, data)"
          class="opacity-0 group-hover:opacity-100 hover:!bg-red-500 hover:text-white !h-[16px] !w-[16px]"
        />
      </span>
      <!-- {{ node.label }} -->
    </template>
  </el-tree>
</template>

<style scoped>
.el-tree {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
}
:deep(.el-tree__drop-indicator) {
  display: none;
}
:deep(.el-icon.is-leaf) {
  display: none;
}
:deep(.el-tree-node > .el-tree-node__content) {
  background-color: transparent;
  height: auto;
}
</style>
