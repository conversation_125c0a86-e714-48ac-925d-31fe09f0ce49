<script setup>
import { storeToRefs } from 'pinia'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useBarFiltering } from '@/content/composables/useBarFiltering'
import { TABS } from '@/content/utils'
import { useAppStateStore } from '@/stores/appState'

const props = defineProps({
  onTeamChange: {
    type: Function,
    required: false,
    default: null,
  },
  modelValue: {
    type: [String, Array],
    required: false,
    default: null,
  },
  useLocalState: {
    type: Boolean,
    required: false,
    default: false,
  },
  multiple: {
    type: Boolean,
    required: false,
    default: false,
  },
  bar: {
    type: Object,
    required: false,
    default: null,
  },
  user: {
    type: Object,
    required: false,
    default: null,
  },
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()
const appState = useAppStateStore()
const { barOptions } = useBarFiltering()
const { userDoc, selectedTeamId, areTeamBarsLoaded, teamBars, selectedBarId, selectedBar } =
  storeToRefs(appState)

// Use local state or global state based on props
const localSelectedTeamId = ref(props.useLocalState ? props.modelValue : selectedTeamId.value)

// Keep localSelectedTeamId in sync with global selectedTeamId only if not using local state
watch(
  selectedTeamId,
  (newValue) => {
    if (!props.useLocalState) {
      localSelectedTeamId.value = newValue
    }
  },
  { immediate: true },
)

// Watch for modelValue changes when using local state
watch(
  () => props.modelValue,
  (newValue) => {
    if (props.useLocalState && newValue !== localSelectedTeamId.value) {
      localSelectedTeamId.value = newValue
    }
  },
  { immediate: true },
)

const teams = computed(() => userDoc.value?.teams || [])

// Default bar management behavior
function defaultHandleChange(value) {
  selectedTeamId.value = value
  areTeamBarsLoaded.value = false
  teamBars.value = []
  barOptions.value = []
  selectedBarId.value = ''
  selectedBar.value = null
  appState.updateActiveBarIdForTab(TABS.Team, selectedBarId.value)
}

function handleChange(value) {
  // Update local state
  localSelectedTeamId.value = value

  if (props.useLocalState) {
    // Emit the change for v-model when using local state
    emit('update:modelValue', value)

    // Call custom handler if provided
    if (props.onTeamChange && typeof props.onTeamChange === 'function') {
      props.onTeamChange(value)
    }
  } else {
    appState.setSelectedTeamId(value, teamBars.value)
    if (props.onTeamChange && typeof props.onTeamChange === 'function') {
      props.onTeamChange(value)
    } else {
      defaultHandleChange(value)
    }
  }
}

const canRemoveTeam = (team) => {
  if (!props.bar || !props.user) return true
  const isBarOwner = props.bar.owner?.id === props.user.uid
  const isTeamOwner = team.ownerId === props.user.uid || team.owner?.id === props.user.uid
  return isBarOwner || isTeamOwner
}

// Determine if dropdown should be readonly
const isReadOnly = computed(() => {
  if (props.readonly) return true
  if (!props.bar || !props.user) return false
  const isBarOwner = props.bar.owner?.id === props.user.uid
  if (props.multiple) {
    return !(
      isBarOwner ||
      (Array.isArray(localSelectedTeamId.value)
        ? teams.value.some(
            (team) =>
              (team.ownerId === props.user.uid || team.owner?.id === props.user.uid) &&
              localSelectedTeamId.value.includes(team.id),
          )
        : false)
    )
  }
  const selectedTeam = teams.value.find((team) => team.id === localSelectedTeamId.value)
  const isTeamOwner =
    selectedTeam &&
    (selectedTeam.ownerId === props.user.uid || selectedTeam.owner?.id === props.user.uid)
  return !(isBarOwner || isTeamOwner)
})
</script>

<template>
  <el-select
    class="text-lg font-medium w-full"
    v-model="localSelectedTeamId"
    filterable
    :multiple="props.multiple"
    :placeholder="t('team.select')"
    :teleported="false"
    autocomplete="one-time-code"
    @change="handleChange"
    data-testid="team-dropdown"
    :disabled="isReadOnly"
  >
    <el-option
      v-for="team in teams"
      :key="team.id"
      :label="team.name"
      :value="team.id"
      class="border-b border-opacity-25 border-gray-300 last-of-type:border-b-0 w-full max-w-[99dvw] group"
    >
      {{ team.name }}
    </el-option>
  </el-select>
</template>
