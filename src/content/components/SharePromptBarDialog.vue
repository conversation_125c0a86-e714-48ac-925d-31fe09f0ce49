<script setup>
import { Check, Close } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { computed, inject, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import { useAppStateStore } from '../../stores/appState'
import { getFromLocalStorage } from '../localStorage'
import { createSharePage, TABS } from '../utils'
import SocialMediaIcon from './icons/social_media.vue'
import Tooltip from './Tooltip.vue'

const { t } = useI18n()

const appState = useAppStateStore()
const { selectedBar } = storeToRefs(appState)

const props = defineProps(['isOpen', 'isPrompt'])
const emit = defineEmits(['closeDialog', 'setPublic', 'changeIsListed', 'changeIsEditable'])

const activeTabName = ref(getFromLocalStorage('activeTab'))
const isMyPrompts = ref(activeTabName.value === TABS.MyPrompts)

const user = useCurrentUser()

const bar = ref(selectedBar.value)
// console.log('shareDialog bar: ', bar.value)
const sharePrompt = ref(inject('sharePrompt', ref(null)))
// console.log('shareDialog prompt: ', sharePrompt.value)
const prompt = ref(sharePrompt.value ? sharePrompt.value.prompt : null)

const showShare = ref(bar.value.isPublic)
const showSocialShareButtons = ref(false)

const isBarPublic = computed(() => bar.value.isPublic)
// console.log('isBarPublic: ', isBarPublic.value)

const isPrompt = computed(() => {
  return props.isPrompt || sharePrompt.value ? true : false
})
// console.log('isPrompt: ', isPrompt.value)

const shareLink = ref('')
const shareImg = ref('')
const hash = bar.value.id
const barName = bar.value.name
const barTags = bar.value.tags.join(',') || ''
const promptId = isPrompt.value ? prompt.value.id : ''
const promptName = isPrompt.value ? prompt.value.label : ''
const barDescription = bar.value.description || ''
const promptDescription = isPrompt.value ? prompt.value.description : ''
const description = isPrompt.value ? promptDescription : barDescription
const barDestination = bar.value.destination || ''
const promptDestination = isPrompt.value ? prompt.value.destination : ''
const destination = promptDestination ? promptDestination : barDestination

const actualDomain = window.location.hostname
const doSharePageUpdate = ref(true)
const hasCreatedSharePage = ref(false)

const createSharePageHandler = async () => {
  try {
    const result = await createSharePage(
      hash,
      barName,
      doSharePageUpdate.value,
      destination || actualDomain,
      promptId,
      promptName,
      description,
      barTags,
    )

    if (result.success) {
      hasCreatedSharePage.value = true
      shareImg.value = result.imageUrl
      shareLink.value = result.shareLink
      showSocialShareButtons.value = true
    } else {
      hasCreatedSharePage.value = false
    }
    // console.log('result: ', result)
  } catch (error) {
    hasCreatedSharePage.value = false
  }
}

onMounted(() => {
  if (bar.value.isPublic) {
    createSharePageHandler()
  }
})

watch(hasCreatedSharePage, (newVal) => {
  // console.log('Status generowania strony share zmieniony na:', newVal)
})

// console.log('shareDialog: ', bar.value, props)
// const shareLink = ref(generatePromptBarShareLink(bar.value))

// if (sharePrompt && sharePrompt.value) {
//   // console.log('share sharePrompt: ', sharePrompt.value)
//   shareLink.value = generatePromptShareLink(
//     bar.value,
//     sharePrompt.value.prompt,
//     sharePrompt.value.destination,
//   )
// }
// console.log('shareLink: ', shareLink.value)

const isCopied = ref(false)
const isListed = ref(bar.value.isListed ?? false)
const isEditable = ref(bar.value.isEditable ?? false)

const shareName = ref(sharePrompt.value ? sharePrompt.value.label : bar.value.name)

const isOwner = computed(() => bar.value.owner.id === user?.value?.uid)

const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)

  isCopied.value = true

  setTimeout(() => {
    isCopied.value = false
  }, 1000)
}

const openShareLink = () => {
  window.open(shareLink.value, '_blank')
}

const close = () => {
  return emit('closeDialog')
}

const changeIsPublic = async () => {
  // console.log('changeIsPublic: ', bar.value.isPublic)
  if (!bar.value.isPublic) {
    // bar.value.isPublic = true
    showShare.value = true
    await createSharePageHandler()
    emit('changeIsPublic', true)
  } else {
    // bar.value.isPublic = false
    shareImg.value = ''
    shareLink.value = ''
    showSocialShareButtons.value = false
    showShare.value = false
    emit('changeIsPublic', false)
  }
}

const changeIsListed = () => {
  return emit('changeIsListed', isListed.value)
}

const changeIsEditable = () => {
  return emit('changeIsEditable', isEditable.value)
}

const sharingInfo = {
  quote: t('app.discoverThePowerOf'),
}
const networks = [
  // { network: 'baidu', name: 'Baidu', icon: 'fas fah fa-lg fa-paw', color: '#2529d8' },
  // { network: 'buffer', name: 'Buffer', icon: 'fab fah fa-lg fa-buffer', color: '#323b43' },
  // { network: 'email', name: 'Email', icon: 'far fah fa-lg fa-envelope', color: '#333333' },
  // { network: 'evernote', name: 'Evernote', icon: 'fab fah fa-lg fa-evernote', color: '#2dbe60' },
  { network: 'facebook', name: 'Facebook', icon: 'fab fah fa-lg fa-facebook-f', color: '#1877f2' },
  // { network: 'flipboard', name: 'Flipboard', icon: 'fab fah fa-lg fa-flipboard', color: '#e12828' },
  // { network: 'hackernews', name: 'HackerNews', icon: 'fab fah fa-lg fa-hacker-news', color: '#ff4000' },
  // { network: 'instapaper', name: 'Instapaper', icon: 'fas fah fa-lg fa-italic', color: '#428bca' },
  // { network: 'line', name: 'Line', icon: 'fab fah fa-lg fa-line', color: '#00c300' },
  { network: 'linkedin', name: 'LinkedIn', icon: 'fab fah fa-lg fa-linkedin', color: '#007bb5' },
  // { network: 'messenger', name: 'Messenger', icon: 'fab fah fa-lg fa-facebook-messenger', color: '#0084ff' },
  // { network: 'odnoklassniki', name: 'Odnoklassniki', icon: 'fab fah fa-lg fa-odnoklassniki', color: '#ed812b' },
  // { network: 'pinterest', name: 'Pinterest', icon: 'fab fah fa-lg fa-pinterest', color: '#bd081c' },
  // { network: 'pocket', name: 'Pocket', icon: 'fab fah fa-lg fa-get-pocket', color: '#ef4056' },
  // { network: 'quora', name: 'Quora', icon: 'fab fah fa-lg fa-quora', color: '#a82400' },
  // { network: 'reddit', name: 'Reddit', icon: 'fab fah fa-lg fa-reddit-alien', color: '#ff4500' },
  // { network: 'skype', name: 'Skype', icon: 'fab fah fa-lg fa-skype', color: '#00aff0' },
  // { network: 'sms', name: 'SMS', icon: 'far fah fa-lg fa-comment-dots', color: '#333333' },
  // { network: 'stumbleupon', name: 'StumbleUpon', icon: 'fab fah fa-lg fa-stumbleupon', color: '#eb4924' },
  {
    network: 'telegram',
    name: 'Telegram',
    icon: 'fab fah fa-lg fa-telegram-plane',
    color: '#0088cc',
  },
  // { network: 'tumblr', name: 'Tumblr', icon: 'fab fah fa-lg fa-tumblr', color: '#35465c' },
  // { network: 'twitter', name: 'Twitter', icon: 'fab fah fa-lg fa-twitter', color: '#1da1f2' },
  { network: 'x', name: 'X', icon: 'fab fah fa-lg fa-x-twitter', color: '#1da1f2' },
  // { network: 'viber', name: 'Viber', icon: 'fab fah fa-lg fa-viber', color: '#59267c' },
  // { network: 'vk', name: 'Vk', icon: 'fab fah fa-lg fa-vk', color: '#4a76a8' },
  // { network: 'weibo', name: 'Weibo', icon: 'fab fah fa-lg fa-weibo', color: '#e9152d' },
  { network: 'whatsapp', name: 'Whatsapp', icon: 'fab fah fa-lg fa-whatsapp', color: '#25d366' },
  // { network: 'wordpress', name: 'Wordpress', icon: 'fab fah fa-lg fa-wordpress', color: '#21759b' },
  // { network: 'xing', name: 'Xing', icon: 'fab fah fa-lg fa-xing', color: '#026466' },
  // { network: 'yammer', name: 'Yammer', icon: 'fab fah fa-lg fa-yammer', color: '#0072c6' },
  // { network: 'fakeblock', name: 'Custom Network', icon: 'fab fah fa-lg fa-vuejs', color: '#41b883' }
]
</script>

<template>
  <el-dialog
    :model-value="props.isOpen"
    width="clamp(300px, 90vw, 600px)"
    @close="close"
    destroy-on-close
    data-testid="share-prompt-bar-dialog"
  >
    <template #header>
      <h2 class="text-lg">
        {{ t(isPrompt.value ? 'dialog.sharePrompt' : 'dialog.sharePromptBar') }}
      </h2>
    </template>

    <div v-if="showShare" class="w-full flex flex-col gap-4">
      <div>
        <el-skeleton style="width: 100%" :loading="!shareImg" animated>
          <template #template>
            <el-skeleton-item variant="image" style="width: 100%; height: 300px" />
          </template>
          <template #default>
            <img :src="shareImg" :alt="t('dialog.shareImage')" class="w-full h-auto aipl-image" />
          </template>
        </el-skeleton>
      </div>

      <div
        class="flex gap-2 flex-wrap justify-end"
        data-testid="share-prompt-bar-targets"
        v-if="showSocialShareButtons"
      >
        <share-network
          v-for="network in networks"
          :key="network.network"
          v-slot="{ share }"
          :network="network.network"
          :title="shareName"
          :url="shareLink"
          :description="description"
          :quote="sharingInfo.quote"
          :hashtags="barTags"
          :media="shareImg"
        >
          <Tooltip effect="light" placement="bottom">
            <template #content>
              <span>{{ t('dialog.shareOn') }} {{ network.name }}</span>
            </template>
            <div
              @click="share"
              :style="{
                border: '1px solid ' + network.color + '66',
                backgroundColor: network.color + '20',
              }"
              class="cursor-pointer rounded-md p-1 hover:opacity-75"
            >
              <SocialMediaIcon :name="network.network" :color="network.color" :size="1.8" />
              <!-- <span style="color: {{ network.color }};">{{ network.name }}</span> -->
            </div>
          </Tooltip>
        </share-network>
      </div>

      <div>
        <el-text>
          <span class="font-bold">{{ shareName }}</span>
          {{ t('dialog.shareLink') }}:
        </el-text>
      </div>
      <div>
        <el-input
          v-model="shareLink"
          :value="shareLink"
          readonly
          data-testid="share-prompt-bar-readonly-input"
        >
          <template #append>
            <el-button-group>
              <Tooltip effect="light" placement="bottom">
                <template #content>
                  <span>{{ t('btn.openUrl') }}</span>
                </template>
                <el-button type="primary" @click="openShareLink" icon="TopRight"></el-button>
              </Tooltip>
              <Tooltip effect="light" placement="bottom">
                <template #content>
                  <span v-if="!isCopied">{{ t('btn.copy') }}</span>
                  <span v-else class="text-green-500">{{ t('dialog.copied') + '!' }}</span>
                </template>
                <el-button type="primary" @click="copyShareLink" icon="DocumentCopy"></el-button>
              </Tooltip>
            </el-button-group>
          </template>
        </el-input>
        <div class="flex gap-2 items-center w-full mt-2" v-if="isOwner">
          <el-switch
            v-model="isListed"
            inline-prompt
            :active-icon="Check"
            :inactive-icon="Close"
            @change="changeIsListed"
          />
          <span v-if="isListed">{{ t('dialog.availableInPublicBars') }}</span>
          <span v-else>{{ t('dialog.linkAccessOnly') }}</span>
        </div>
        <div class="flex gap-2 items-center w-full mt-2" v-if="isOwner">
          <el-switch
            v-model="isEditable"
            inline-prompt
            :active-icon="Check"
            :inactive-icon="Close"
            @change="changeIsEditable"
          />
          <span v-if="isEditable">{{ t('dialog.allowOthersToEdit') }}</span>
          <span v-else>{{ t('dialog.notEditableByOthers') }}</span>
        </div>
      </div>
    </div>

    <div v-else class="w-full">
      <p>{{ t('dialog.setToPublicToShare') }}</p>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="!bar.isPublic" size="" type="warning" plain @click="changeIsPublic">
          {{ t('btn.setToPublic') }}
        </el-button>
        <el-button v-else-if="isMyPrompts" size="" type="warning" plain @click="changeIsPublic">
          {{ t('btn.setToPrivate') }}
        </el-button>
        <el-button @click="close" :icon="Close">{{ t('btn.close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped></style>
