<template>
  <div class="tag-filter">
    <div class="filter-header">
      <el-button
        v-if="selectedTags.length > 0"
        @click="clearAllFilters"
        type="info"
        size="small"
        plain
        :icon="Delete"
      >
        {{ t('tagFilter.actions.clearAll') }}
      </el-button>
    </div>

    <div class="tags-container">
      <el-check-tag
        v-for="tag in props.allTags"
        :key="tag"
        :checked="selectedTags.includes(tag)"
        @change="(checked) => handleTagChange(tag, checked)"
        :style="getTagStyleForFilter(tag)"
        size="large"
      >
        {{ tag }}
        <el-text type="info" size="small" class="tag-count"> ({{ getTagCount(tag) }}) </el-text>
      </el-check-tag>
    </div>
  </div>
</template>

<script setup>
import { Delete } from '@element-plus/icons-vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useTagColors } from '../composables/useTagColors.js'

const props = defineProps({
  selectedTags: {
    type: Array,
    default: () => [],
  },
  sites: {
    type: Array,
    required: true,
  },
  allTags: {
    type: Array,
    default: () => [],
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:selectedTags'])

const { t } = useI18n()

const toggleTag = (tag) => {
  const newSelectedTags = [...props.selectedTags]
  const index = newSelectedTags.indexOf(tag)

  if (index > -1) {
    newSelectedTags.splice(index, 1)
  } else {
    newSelectedTags.push(tag)
  }

  emit('update:selectedTags', newSelectedTags)
}

const handleTagChange = (tag, checked) => {
  const newSelectedTags = [...props.selectedTags]

  if (checked) {
    if (!newSelectedTags.includes(tag)) {
      newSelectedTags.push(tag)
    }
  } else {
    const index = newSelectedTags.indexOf(tag)
    if (index > -1) {
      newSelectedTags.splice(index, 1)
    }
  }

  emit('update:selectedTags', newSelectedTags)
}

const clearAllFilters = () => {
  emit('update:selectedTags', [])
}

const getTagCount = (tag) => {
  return props.sites.filter((site) => site.tags.includes(tag)).length
}

const getTagStyleForFilter = (tag) => {
  const { getTagStyle } = useTagColors(props.isDarkTheme)
  const isSelected = props.selectedTags.includes(tag)
  return getTagStyle(tag, isSelected)
}
</script>

<style scoped>
.tag-filter {
  margin-bottom: 1.5rem;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-count {
  opacity: 0.8;
}

@media (max-width: 768px) {
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .tags-container {
    gap: 0.25rem;
    justify-content: center;
  }
}
</style>
