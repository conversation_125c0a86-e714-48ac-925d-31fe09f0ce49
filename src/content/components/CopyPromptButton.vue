<script setup>
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '../../stores/appState'
import Tooltip from './Tooltip.vue'

defineProps({
  btnCopyType: {
    type: String,
    default: '',
  },
})

const { t } = useI18n()

const appState = useAppStateStore()
const { selectedBarId, isTabEmpty } = storeToRefs(appState)

const emit = defineEmits(['copy', 'mouseenter', 'mouseleave'])

function handleClick() {
  emit('copy')
}
function handleMouseEnter() {
  emit('mouseenter')
}
function handleMouseLeave() {
  emit('mouseleave')
}

const buttonDisabled = computed(() => {
  return !selectedBarId.value
})
</script>

<template>
  <Tooltip
    class="box-item"
    effect="light"
    :content="
      selectedBarId ? t('messages.copyToYourBar') : t('messages.selectPromptBarToCopyToYourBar')
    "
    placement="bottom"
  >
    <el-button
      v-if="!buttonDisabled"
      :type="btnCopyType"
      plain
      class="no-bg"
      icon="CopyDocument"
      data-testid="copy-prompt-button"
      @click="handleClick"
      :class="{ 'z-10': isTabEmpty }"
      :disabled="!selectedBarId"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <span class="hidden xl:block">{{ t('btn.copy') }}</span>
    </el-button>
  </Tooltip>
</template>
