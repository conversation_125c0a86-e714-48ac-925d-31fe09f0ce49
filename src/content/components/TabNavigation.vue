<template>
  <div class="tab-group-container">
    <el-button-group class="sites-tab-group" role="tablist" aria-label="Navigation tabs">
      <el-button
        v-for="(tab, index) in orderedTabs"
        :key="tab.id"
        :type="activeTab === tab.id ? 'primary' : 'default'"
        class="tab-button"
        :class="{ dragging: draggedTabId === tab.id }"
        :draggable="true"
        role="tab"
        :aria-selected="activeTab === tab.id"
        :aria-controls="`${tab.id}-panel`"
        :aria-label="`${tab.label} tab${activeTab === tab.id ? ' (active)' : ''}, draggable`"
        :tabindex="activeTab === tab.id ? 0 : -1"
        @click="handleTabClick(tab.id)"
        @dragstart="handleTabDragStart($event, tab.id, index)"
        @dragover="handleTabDragOver($event)"
        @drop="handleTabDrop($event, tab.id, index)"
        @dragenter="handleTabDragEnter($event)"
        @dragleave="handleTabDragLeave($event)"
        @dragend="handleTabDragEnd($event)"
        @keydown="handleTabKeydown($event, tab.id, index)"
      >
        <el-icon class="drag-handle" :size="14" aria-hidden="true">
          <Grid />
        </el-icon>
        <span class="tab-label">{{ tab.label }}</span>
      </el-button>
    </el-button-group>

    <el-tooltip :content="t('tabNavigation.reorder.tooltip')" placement="top">
      <el-button
        @click="handleCycleTabOrder"
        class="reorder-btn"
        :class="{ 'reorder-active': isReordering }"
        size="small"
        :icon="Sort"
        circle
        :loading="isReordering"
      />
    </el-tooltip>
  </div>
</template>

<script setup>
import { Grid, Sort } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

// Props
const props = defineProps({
  activeTab: {
    type: String,
    required: true,
  },
  orderedTabs: {
    type: Array,
    required: true,
  },
  isReordering: {
    type: Boolean,
    default: false,
  },
  draggedTabId: {
    type: String,
    default: null,
  },
})

// Emits
const emit = defineEmits([
  'update:activeTab',
  'cycle-tab-order',
  'tab-drag-start',
  'tab-drag-over',
  'tab-drop',
  'tab-drag-enter',
  'tab-drag-leave',
  'tab-drag-end',
  'tab-keydown',
])

const { t } = useI18n()

// Local state for drag operations
const draggedTabIndex = ref(null)

// Event handlers
const handleTabClick = (tabId) => {
  emit('update:activeTab', tabId)
}

const handleCycleTabOrder = () => {
  emit('cycle-tab-order')
}

const handleTabDragStart = (event, tabId, index) => {
  draggedTabIndex.value = index
  event.dataTransfer.effectAllowed = 'move'
  event.target.style.opacity = '0.5'
  emit('tab-drag-start', { event, tabId, index })
}

const handleTabDragOver = (event) => {
  event.preventDefault()
  if (!event.target.classList.contains('tab-button')) return
  event.target.classList.add('drag-over')
  emit('tab-drag-over', { event })
}

const handleTabDrop = (event, targetTabId, targetIndex) => {
  event.preventDefault()
  event.target.classList.remove('drag-over')
  emit('tab-drop', { event, targetTabId, targetIndex, draggedTabIndex: draggedTabIndex.value })
}

const handleTabDragEnter = (event) => {
  event.preventDefault()
  emit('tab-drag-enter', { event })
}

const handleTabDragLeave = (event) => {
  event.preventDefault()
  if (!event.target.classList.contains('tab-button')) return
  event.target.classList.remove('drag-over')
  emit('tab-drag-leave', { event })
}

const handleTabDragEnd = (event) => {
  event.target.style.opacity = '1'
  draggedTabIndex.value = null

  // Clean up any remaining drag-over classes
  document.querySelectorAll('.tab-button').forEach((el) => {
    el.classList.remove('drag-over')
  })

  emit('tab-drag-end', { event })
}

const handleTabKeydown = (event, tabId, index) => {
  const tabs = props.orderedTabs
  let newIndex = index

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      newIndex = index > 0 ? index - 1 : tabs.length - 1
      break
    case 'ArrowRight':
      event.preventDefault()
      newIndex = index < tabs.length - 1 ? index + 1 : 0
      break
    case 'Home':
      event.preventDefault()
      newIndex = 0
      break
    case 'End':
      event.preventDefault()
      newIndex = tabs.length - 1
      break
    case 'Enter':
    case ' ':
      event.preventDefault()
      emit('update:activeTab', tabId)
      return
    default:
      return
  }

  // Focus the new tab
  const newTab = tabs[newIndex]
  if (newTab) {
    emit('update:activeTab', newTab.id)
    // Focus the button element
    const buttonElement = event.target.closest('.sites-tab-group').children[newIndex]
    if (buttonElement) {
      buttonElement.focus()
    }
  }

  emit('tab-keydown', { event, tabId, index, newIndex })
}
</script>

<style scoped>
.tab-group-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sites-tab-group {
  display: flex;
}

.tab-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: grab;
  transition: all 0.3s ease;
  border-radius: 6px;
  position: relative;
  flex: 1;
  min-height: 40px;
}

.tab-button:active {
  cursor: grabbing;
}

.tab-button.dragging {
  opacity: 0.5;
  transform: scale(0.95);
}

.tab-button.drag-over {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-7);
}

.drag-handle {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.tab-button:hover .drag-handle {
  opacity: 1;
}

.tab-label {
  font-weight: 500;
}

.reorder-btn {
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.reorder-btn:hover {
  transform: scale(1.05);
}

.reorder-btn.reorder-active {
  animation: reorderPulse 0.3s ease-in-out;
}

@keyframes reorderPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .tab-group-container {
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
    width: 100%;
    justify-content: center;
  }

  .sites-tab-group {
    flex: 1;
    max-width: none;
  }

  .tab-button {
    flex: 1;
    justify-content: center;
    min-height: 44px;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
  }

  .reorder-btn {
    flex-shrink: 0;
    min-height: 44px;
    min-width: 44px;
  }
}

@media (max-width: 480px) {
  .tab-group-container {
    gap: 0.5rem;
    padding: 0.25rem;
    flex-wrap: nowrap;
  }

  .tab-button {
    min-height: 48px;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    flex: 1;
    min-width: 0;
  }

  .reorder-btn {
    min-height: 48px;
    min-width: 48px;
    flex-shrink: 0;
  }
}
</style>
