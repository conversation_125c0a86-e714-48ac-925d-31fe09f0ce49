<template>
  <div class="profile-selector">
    <el-text class="profile-label" size="small">{{
      t('componentControlBar.profile.label')
    }}</el-text>
    <el-select
      :model-value="selectedProfileId"
      @change="handleProfileChange"
      class="profile-select"
      :disabled="isLoading"
      size="small"
      style="flex: 1"
    >
      <el-option value="default" :label="t('componentControlBar.profile.default')" />
      <el-option value="prompt" :label="t('profileSwitcher.prompt')" />
      <el-option value="search" :label="t('profileSwitcher.search')" />
      <el-option
        v-for="profile in savedProfiles"
        :key="profile.id"
        :value="profile.id"
        :label="profile.name"
      />
    </el-select>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const props = defineProps({
  selectedProfileId: {
    type: String,
    required: true,
  },
  savedProfiles: {
    type: Array,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:selectedProfileId', 'profile-change'])

const { t } = useI18n()

const handleProfileChange = (profileId) => {
  emit('update:selectedProfileId', profileId)
  emit('profile-change', profileId)
}
</script>

<style scoped>
.profile-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.profile-label {
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
  color: var(--el-text-color-regular);
}

.profile-select {
  flex: 1;
}

/* Theme-aware styling */
.profile-selector--dark .profile-label {
  color: var(--el-text-color-regular);
}

.profile-selector--light .profile-label {
  color: var(--el-text-color-regular);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-selector {
    min-width: 180px;
    gap: 6px;
  }

  .profile-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .profile-selector {
    min-width: 150px;
    gap: 4px;
  }

  .profile-label {
    font-size: 0.8rem;
  }
}
</style>
