<script setup>
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser, useDocument } from 'vuefire'

import { useAppStateStore } from '../../stores/appState'
import { useBarFiltering } from '../composables/useBarFiltering'
import { useBarOptions } from '../composables/useBarOptions'
import {
  getAllBars,
  getLibraryBarsRefForLang,
  getPopularBarsRefForLang,
  getTeamBars,
  getTeamBarsRef,
  getUserBars,
  publicBarsRef,
} from '../firebase'
import { getFromLocalStorage } from '../localStorage'
import { TABS } from '../utils.js'
import BarSelectMenuItem from './BarSelectMenuItem.vue'
import BarSortHeader from './BarSortHeader.vue'
import CustomIcon from './CustomIcon.vue'

const props = defineProps({
  tabName: String,
  formatFirebaseDate: Function,
  getStyle: Function,
  onTagClick: Function,
  onBarChange: Function,
})

const appState = useAppStateStore()
const {
  barSortFieldName,
  areMyPromptBarsLoaded,
  myPromptBars,
  areAllBarsLoaded,
  allBars,
  arePopularBarsLoaded,
  areLibraryBarsLoaded,
  arePublicBarsLoaded,
  areTeamBarsLoaded,
  publicBars,
  selectedBarId,
  selectedTeamId,
  activeTab,
  popularBars,
  libraryBars,
  teamBars,
  isTabEmpty,
  selectedBar,
  selectRef,
  selectedLangInPopularDropdown,
  selectedLangInLibraryDropdown,
} = storeToRefs(appState)

if (selectRef.value && selectRef.value[0]) {
  selectRef.value[0].blur()
}
const {
  getBarsForCurrentTab,
  barSortFunctionsByField,
  areBarsLoadedForCurrentTab,
  getLabelForBar,
} = useBarOptions()

const { barOptions, filterMethod } = useBarFiltering()

const user = useCurrentUser()
const { t } = useI18n()

const updateActiveBarId = (tabName) => {
  if (getFromLocalStorage(tabName)) {
    appState.activeBarIdsByTab[tabName] = selectedBarId.value
  }
}

const loadLibraryBars = async () => {
  const libraryRef = getLibraryBarsRefForLang(selectedLangInLibraryDropdown.value)

  const doc = useDocument(libraryRef)
  const libraryBarsById = await doc.promise.value

  libraryBars.value = Object.entries(libraryBarsById).map(([barId, bar]) => ({
    ...bar,
    id: barId,
  }))
}

const loadPopularBars = async () => {
  const popularRef = getPopularBarsRefForLang(selectedLangInPopularDropdown.value)
  const doc = useDocument(popularRef)
  const popularBarsById = await doc.promise.value

  popularBars.value = Object.entries(popularBarsById).map(([barId, bar]) => ({
    ...bar,
    id: barId,
  }))
}

// Team bars real-time listener
let teamBarsUnwatch = null

const setupTeamBarsListener = () => {
  if (!selectedTeamId.value) return

  // Clean up previous listener
  if (teamBarsUnwatch) {
    teamBarsUnwatch()
    teamBarsUnwatch = null
  }

  const teamRef = getTeamBarsRef(selectedTeamId.value)
  const doc = useDocument(teamRef)

  // Set up real-time listener for team bars
  teamBarsUnwatch = watch(
    doc,
    (teamBarsById) => {
      if (teamBarsById) {
        teamBars.value = Object.entries(teamBarsById || {})
          .map(([barId, bar]) => ({
            ...bar,
            id: barId,
          }))
          .filter((bar) => bar.name)
          .sort((a, b) =>
            a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }),
          )
      }
    },
    { immediate: true },
  )
}

// Function to refresh team bars for a specific team
const refreshTeamBars = async (teamId) => {
  if (!teamId) return

  // If this is the currently selected team and we're in Team tab,
  // the real-time listener should handle updates automatically
  if (teamId === selectedTeamId.value && activeTab.value === TABS.Team && teamBarsUnwatch) {
    // Force a refresh by temporarily clearing and reloading
    areTeamBarsLoaded.value = false
    const freshTeamBars = await getTeamBars(teamId)
    teamBars.value = freshTeamBars
    areTeamBarsLoaded.value = true
    return
  }

  // If this is the currently selected team but we're not in Team tab,
  // or if it's a different team, update the data for when user switches to Team tab
  if (teamId === selectedTeamId.value) {
    const freshTeamBars = await getTeamBars(teamId)
    teamBars.value = freshTeamBars
  }
}

const loadTeamBars = async () => {
  setupTeamBarsListener()
}

watch(selectedLangInPopularDropdown, async (newVal) => {
  if (appState.activeTab === TABS.Popular) {
    loadPopularBars()
  }
})

watch(selectedLangInLibraryDropdown, async (newVal) => {
  if (appState.activeTab === TABS.Library) {
    loadLibraryBars()
  }
})

watch(selectedTeamId, async (newVal) => {
  if (appState.activeTab === TABS.Team) {
    areTeamBarsLoaded.value = false
    setupTeamBarsListener()
    areTeamBarsLoaded.value = true
  }
})

// Expose refreshTeamBars function globally for use by other components
appState.refreshTeamBars.value = refreshTeamBars

const onBarSelectorFocus = async (ev) => {
  ev.preventDefault()
  ev.stopPropagation()
  if ([TABS.All, TABS.Favorites].includes(activeTab.value) && !areAllBarsLoaded.value) {
    allBars.value = await getAllBars()
    areAllBarsLoaded.value = true
  } else if (activeTab.value === TABS.Popular && !arePopularBarsLoaded.value) {
    await loadPopularBars()
    // console.log(new Date().toUTCString() + ' Read-> popularBarsRef: popularBars')
    arePopularBarsLoaded.value = true
  } else if (activeTab.value === TABS.Public && !arePublicBarsLoaded.value) {
    const doc = useDocument(publicBarsRef)
    const publicBarsById = await doc.promise.value
    publicBars.value = Object.entries(publicBarsById).map(([barId, bar]) => ({ ...bar, id: barId }))
    // console.log(new Date().toUTCString() + ' Read-> publicBarsRef: publicBars')
    arePublicBarsLoaded.value = true
  } else if (activeTab.value === TABS.MyPrompts && !areMyPromptBarsLoaded.value) {
    myPromptBars.value = await getUserBars(user.value.uid)
    areMyPromptBarsLoaded.value = true
  } else if (activeTab.value === TABS.Library && !areLibraryBarsLoaded.value) {
    await loadLibraryBars()
    // console.log(new Date().toUTCString() + ' Read-> libraryBarsRef: libraryBars')
    areLibraryBarsLoaded.value = true
  } else if (activeTab.value === TABS.Team && !areTeamBarsLoaded.value) {
    await loadTeamBars()
    // console.log(new Date().toUTCString() + ' Read-> teamBarsRef: teamBars')
    areTeamBarsLoaded.value = true
  }

  barOptions.value = getBarsForCurrentTab()

  appState.updateActiveBarIdForTab(activeTab.value, selectedBarId.value)

  if (activeTab.value === TABS.Favorites) {
    appState.updateActiveBarIdForTab(TABS.All, selectedBarId.value)
  } else if (activeTab.value === TABS.All) {
    appState.updateActiveBarIdForTab(TABS.Favorites, selectedBarId.value)
  }
}

const sortedBarOptions = computed(() => {
  return barOptions.value.sort(barSortFunctionsByField[barSortFieldName.value])
})

const localSelectedBarId = ref(selectedBarId.value)

watch(
  () => selectedBarId.value,
  (val) => {
    localSelectedBarId.value = val
  },
)

watch(
  () => [
    activeTab.value,
    allBars.value,
    popularBars.value,
    publicBars.value,
    myPromptBars.value,
    libraryBars.value,
    teamBars.value,
  ],
  () => {
    barOptions.value = getBarsForCurrentTab()
  },
  { immediate: true },
)

watch(
  () => getBarsForCurrentTab(),
  () => {
    const areBarsLoadedForCurrentTabValue = areBarsLoadedForCurrentTab()

    if (
      appState.isMyPromptsTabActiveAndEmpty ||
      (areBarsLoadedForCurrentTabValue && getBarsForCurrentTab().length === 0)
    ) {
      isTabEmpty.value = true
    } else {
      isTabEmpty.value = false
    }

    barOptions.value = getBarsForCurrentTab()
  },
)

function handleChange(value) {
  selectedBarId.value = value
  if (props.onBarChange) props.onBarChange(value)
}

const isDropdownEmpty = computed(() => {
  return getBarsForCurrentTab().length === 0
})

// Cleanup team bars listener on unmount
onUnmounted(() => {
  if (teamBarsUnwatch) {
    teamBarsUnwatch()
    teamBarsUnwatch = null
  }
})
</script>

<template>
  <el-select
    ref="selectRef"
    class="text-lg font-medium w-full"
    v-model="localSelectedBarId"
    filterable
    :filter-method="filterMethod"
    :placeholder="t('bar.select')"
    :teleported="false"
    autocomplete="one-time-code"
    @change="handleChange"
    @focus="onBarSelectorFocus"
    data-testid="my-prompt-bar-dropdown"
  >
    <template #header>
      <BarSortHeader />
    </template>
    <template #prefix>
      <CustomIcon :name="selectedBar?.icon" :size="'22'" />
    </template>
    <el-option v-if="isDropdownEmpty" value="" label="" disabled class="text-center">
      {{ t('bar.noPromptBars') }}
    </el-option>
    <el-option
      v-else
      v-for="(bar, index) in sortedBarOptions"
      :key="bar.id"
      :label="getLabelForBar(bar, props.tabName === TABS.MyPrompts)"
      :value="bar.id"
      class="border-b border-opacity-25 border-gray-300 last-of-type:border-b-0 w-full max-w-[99dvw] group"
    >
      <BarSelectMenuItem
        :bar="bar"
        :tabName="props.tabName"
        :formatFirebaseDate="props.formatFirebaseDate"
        :getStyle="props.getStyle"
        :onTagClick="props.onTagClick"
      />
    </el-option>
  </el-select>
</template>
