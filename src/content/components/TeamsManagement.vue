<script setup>
import { storeToRefs } from 'pinia'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import { useAppStateStore } from '@/stores/appState'

import { inviteWithSubscription, removeMember } from '../firebase'
import TeamDropdown from './TeamDropdown.vue'

const props = defineProps({
  userSubscription: {
    type: Object,
    required: false,
    default: () => ({ members: [], maxMembers: 0 }),
  },
  existingMemberEmails: {
    type: Array,
    required: false,
    default: () => [],
  },
})

const emit = defineEmits(['membersUpdated'])

const { t } = useI18n()
const user = useCurrentUser()

const appState = useAppStateStore()
const { userDoc, selectedTeamIdForDialog } = storeToRefs(appState)

const localSelectedTeamId = ref(selectedTeamIdForDialog.value)

const isLoading = ref(false)
const invitations = ref([])
const inviteSelectOptions = ref([])

const teams = computed(() => {
  return userDoc.value?.teams || []
})

const selectedTeam = computed(() => {
  if (!localSelectedTeamId.value) return null
  return teams.value.find((team) => team.id === localSelectedTeamId.value)
})

const currentTeamMembers = computed(() => {
  if (!selectedTeam.value) return []

  const teamSubscriptionMembers = selectedTeam.value.subscription?.members || []
  let members = [...teamSubscriptionMembers]

  const ownerEmail = selectedTeam.value.email
  if (ownerEmail && !members.includes(ownerEmail)) {
    members.unshift(ownerEmail)
  }

  return members
})

const currentMaxMembers = computed(() => {
  if (!selectedTeam.value) return 0

  const teamSubscriptionMaxMembers = selectedTeam.value.subscription?.maxMembers
  if (teamSubscriptionMaxMembers && teamSubscriptionMaxMembers > 0) {
    return teamSubscriptionMaxMembers
  }

  const globalSubscriptionMaxMembers = userDoc.value?.subscription?.maxMembers || 0
  const propMaxMembers = props.userSubscription?.maxMembers || 0

  return globalSubscriptionMaxMembers > 0 ? globalSubscriptionMaxMembers : propMaxMembers
})

watch(
  teams,
  (newTeams) => {
    if (newTeams.length > 0) {
      // Check if currently selected team exists in the teams list
      const isCurrentTeamValid =
        localSelectedTeamId.value && newTeams.some((team) => team.id === localSelectedTeamId.value)

      if (!isCurrentTeamValid) {
        localSelectedTeamId.value = newTeams[0].id
        appState.setSelectedTeamIdForDialog(newTeams[0].id)
      }
    }
  },
  { immediate: true },
)

watch(
  selectedTeamIdForDialog,
  (newTeamId) => {
    if (newTeamId !== localSelectedTeamId.value) {
      localSelectedTeamId.value = newTeamId
    }
  },
  { immediate: true },
)

function handleTeamChangeForMembers(teamId) {
  localSelectedTeamId.value = teamId
  appState.setSelectedTeamIdForDialog(teamId)
  invitations.value = []
  inviteSelectOptions.value = []
  emit('membersUpdated', currentTeamMembers.value)
}

watch(
  selectedTeam,
  (newTeam, oldTeam) => {
    if (newTeam !== oldTeam) {
      emit('membersUpdated', currentTeamMembers.value)
    }
  },
  { immediate: true, deep: true },
)

const emails = [].filter((email) => !currentTeamMembers.value.includes(email))

const emailOptions = emails.map((email) => {
  return { value: email, label: email }
})

const fetchInviteSelectOptions = (query) => {
  if (query.length > 2) {
    isLoading.value = true
    setTimeout(() => {
      isLoading.value = false
      inviteSelectOptions.value = emailOptions.filter((emailOption) => {
        return emailOption.label.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)
  } else {
    inviteSelectOptions.value = []
  }
}

const isTeamOwner = computed(() => {
  return selectedTeam.value && user.value && selectedTeam.value.email === user.value.email
})

async function handleInvite() {
  if (!selectedTeam.value) return

  try {
    await inviteWithSubscription({
      user: user.value,
      emails: invitations.value,
      teamId: selectedTeam.value.id,
    })

    const updatedEmails = [...currentTeamMembers.value, ...invitations.value]
    emit('membersUpdated', updatedEmails)
    invitations.value = []
  } catch (error) {
    console.error('Error inviting team members:', error)
  }
}

async function handleRemove(email) {
  if (!selectedTeam.value) return

  try {
    await removeMember(user.value.uid, email, selectedTeam.value.id)

    const updatedEmails = currentTeamMembers.value.filter((memberEmail) => memberEmail !== email)
    emit('membersUpdated', updatedEmails)
  } catch (error) {
    console.error('Error removing team member:', error)
  }
}
</script>

<template>
  <div class="space-y-4">
    <div v-if="teams.length > 1" class="mb-4">
      <label class="block text-sm font-medium mb-2">{{ t('team.select') }}</label>
      <TeamDropdown
        v-model="localSelectedTeamId"
        :use-local-state="true"
        :on-team-change="handleTeamChangeForMembers"
      />
    </div>

    <el-card class="box-card" :key="localSelectedTeamId">
      <template #header>
        <div class="text-lg">
          <span v-if="selectedTeam">{{ selectedTeam.name }} - </span>
          {{
            t('subscriptionDialog.team.members', {
              current: currentTeamMembers.length,
              max: currentMaxMembers,
            })
          }}
        </div>
      </template>

      <div class="flex" v-if="isTeamOwner">
        <el-select-v2
          class="flex-grow"
          v-model="invitations"
          allow-create
          multiple
          :multiple-limit="currentMaxMembers - currentTeamMembers.length"
          filterable
          remote
          :remote-method="fetchInviteSelectOptions"
          clearable
          :reserve-keyword="false"
          :options="inviteSelectOptions"
          :loading="isLoading"
          :placeholder="t('subscriptionDialog.team.invitePlaceholder')"
        />
        <el-button
          class="flex-grow-0 ml-2"
          type="success"
          :disabled="!invitations.length"
          @click="handleInvite"
        >
          {{ t('subscriptionDialog.team.invite') }}
        </el-button>
      </div>

      <el-divider v-if="isTeamOwner" />

      <el-table :data="currentTeamMembers">
        <el-table-column>
          <template #header>{{ t('subscriptionDialog.team.email') }}</template>
          <template #default="scope">
            <span>{{ scope.row }}</span>
            <el-tag v-if="scope.row === selectedTeam.email" type="info" size="small" class="ml-2">
              {{ t('subscriptionDialog.team.owner') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="isTeamOwner" align="right">
          <template #header>{{ t('subscriptionDialog.team.actions') }}</template>
          <template #default="scope">
            <el-button
              v-if="scope.row !== selectedTeam.email"
              size="small"
              type="danger"
              @click="handleRemove(scope.row)"
            >
              {{ t('subscriptionDialog.team.remove') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
