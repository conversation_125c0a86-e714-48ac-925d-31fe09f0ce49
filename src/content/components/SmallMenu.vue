<script setup>
import { User } from '@element-plus/icons-vue'
// const SubscriptionDialog = defineAsyncComponent(() => import('./SubscriptionDialog.vue'))
import { version } from '/package'
import { signOut } from 'firebase/auth'
import { doc, getDoc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { computed, onBeforeMount, provide, ref } from 'vue'
import { useCurrentUser, useFirebaseAuth } from 'vuefire'

import Analytics from '../../Analytics'
// console.log('version', version)
import { useAppStateStore } from '../../stores/appState'
import { settingsRef } from '../firebase'
import { removeFromLocalStorage, setToPluginStorageLocal } from '../localStorage'
import IconPlanPremium from './icons/iconPlanPremium.vue'
import LanguageSelector from './LanguageSelector.vue'
import SignInSignUpForm from './SignInSignUpForm.vue'
import SubscriptionDialog from './SubscriptionDialog.vue'

defineProps(['isAdmin', 'menuHideLevel'])
const versionDoc = ref(null)
const hasNewVersion = ref(false)

const auth = useFirebaseAuth()
const user = useCurrentUser()

const appState = useAppStateStore()
const {
  userDoc,
  hasSubscription,
  isSubscriptionDialogOpen,
  subscriptionDialogSelectedTab,
  creditsOwnedByUser,
  loginPopoverVisible,
  isLoggedIn,
  isLoggingIn,
  isSigningUp,
} = storeToRefs(appState)

const canManageTeam = computed(() => {
  if (!userDoc?.value) {
    return false
  }
  return userDoc.value?.teams?.length > 0
})

const hasTeamSubscription = computed(() => {
  if (!userDoc?.value) {
    return false
  }
  return userDoc.value?.subscription?.plan === 'Team' || userDoc.value?.hasTeamSubscription
})

onBeforeMount(async () => {
  const versionRef = doc(settingsRef, 'version')
  versionDoc.value = (await getDoc(versionRef)).data()
  hasNewVersion.value = versionDoc?.value && versionDoc.value.version !== version
  // console.log('versionDoc: ', versionDoc.value)
})

async function logOut() {
  removeFromLocalStorage('activeTab')
  removeFromLocalStorage('All')
  removeFromLocalStorage('Popular')
  removeFromLocalStorage('Public')
  removeFromLocalStorage('My Prompts')
  removeFromLocalStorage('Library')
  removeFromLocalStorage('Favorites')
  removeFromLocalStorage('Team')
  await signOut(auth)

  setToPluginStorageLocal('firebaseLocalStorage', [])

  location.reload()
}

function downloadNewVersion() {
  if (!versionDoc?.value?.link) {
    return
  }
  window.open(versionDoc.value.link, '_blank')
}

function goToFeedbackDoc() {
  window.open(
    'https://docs.google.com/spreadsheets/d/15BXAvCRpFzneCOFpSQ-i2oB_W6HxfblpXSnMgzbre3M/edit#gid=0',
    '_blank',
  )
}

function openSubscriptionDialog(type = '') {
  Analytics.fireEvent('subscription_dialog', { type })
  appState.openSubscriptionDialog()
}

function closeSubscriptionDialog() {
  appState.closeSubscriptionDialog()
}

function openManageTeam() {
  appState.openSubscriptionDialog('Teams')
}
</script>

<template>
  <div
    class="small-menu relative flex justify-end md:absolute right-0 top-0 p-2 z-10 w-full md:w-auto"
    :class="{ 'transition-all duration-300 opacity-0 pointer-events-none': menuHideLevel >= 1 }"
  >
    <ul class="flex gap-1">
      <el-text
        v-if="isLoggedIn"
        class="flex items-center gap-2 mx-2 transition-opacity duration-300"
        :class="{ 'opacity-0': !userDoc, 'opacity-100': userDoc }"
      >
        <span class="hidden clg:block">{{ $t('smallMenu.credits') }}:</span>
        <span data-testid="credits-balance" class="inline-flex items-center gap-1 min-w-[50px]">
          <el-icon size="1.2em"><Coin /></el-icon>
          {{ creditsOwnedByUser }}
        </span>
      </el-text>

      <el-button
        size="small"
        type="info"
        plain
        @click="openSubscriptionDialog('default')"
        class="text-purple-400 hover:!bg-purple-500 hover:!text-white animate-pulse hover:animate-none"
      >
        <el-icon><IconPlanPremium /></el-icon>
        <span class="hidden clg:block">{{
          hasSubscription ? $t('smallMenu.subscription') : $t('smallMenu.buySubscription')
        }}</span>
      </el-button>
      <li>
        <el-popover
          placement="bottom-end"
          :width="300"
          trigger="click"
          :teleported="false"
          v-model:visible="loginPopoverVisible"
        >
          <template #reference>
            <div class="el-dropdown">
              <el-button v-if="user" size="small" :icon="User">
                <span class="hidden clg:block">{{ $t('smallMenu.account') }}</span>
              </el-button>
              <el-button v-else size="small" :icon="User">
                <span class="hidden clg:block">{{ $t('smallMenu.signInSignUp') }}</span>
              </el-button>
            </div>
          </template>
          <div class="mb-3">
            <LanguageSelector />
          </div>
          <div class="mb-3 text-center">
            <el-button
              v-if="hasNewVersion && isAdmin"
              size="small"
              type="warning"
              @click="downloadNewVersion"
              >{{ $t('smallMenu.newVersion') }} {{ versionDoc?.version }}
            </el-button>

            <template v-else>
              <el-button
                v-if="isAdmin"
                size="small"
                type="info"
                plain
                :text="!isAdmin"
                @click="goToFeedbackDoc"
                ><span class="hidden clg:block">{{ $t('smallMenu.version') }}</span>
                {{ versionDoc?.version }}
              </el-button>
              <el-text v-else class="text-xs mx-2 opacity-50">
                {{ $t('smallMenu.version') }} {{ versionDoc?.version }}
              </el-text>
            </template>
          </div>
          <div v-if="user?.emailVerified" class="text-center">
            <div>{{ $t('smallMenu.hello') }} {{ user.displayName || userDoc?.name || 'user' }}</div>
            <div class="text-xs opacity-50">{{ user.email }}</div>

            <hr class="mt-3 border-gray-700" />

            <el-menu v-if="canManageTeam" class="w-full border-none">
              <el-menu-item @click="openManageTeam" class="justify-center">{{
                $t('smallMenu.teams')
              }}</el-menu-item>
            </el-menu>

            <hr class="mb-3 border-gray-700" />

            <div>
              <el-button type="primary" plain @click="logOut">{{
                $t('smallMenu.signOut')
              }}</el-button>
            </div>
          </div>
          <SignInSignUpForm v-else />
        </el-popover>
      </li>
    </ul>
  </div>

  <SubscriptionDialog
    :isOpen="isSubscriptionDialogOpen"
    @close-dialog="closeSubscriptionDialog"
    :selectedTab="subscriptionDialogSelectedTab"
  />
</template>

<style scoped>
.top-navigation-bar .small-menu {
  position: relative !important;
  width: auto !important;
  z-index: 200 !important;
}
</style>
