<script setup>
import { User } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

import SignInSignUpForm from './SignInSignUpForm.vue'

const { t } = useI18n()
</script>

<template>
  <div class="h-[36px] flex items-center justify-center">
    <p>{{ t('messages.loginRequiredTabText') }}</p>
  </div>
  <div class="min-h-[32px] flex items-center justify-center">
    <el-popover placement="top" :width="300" trigger="click" :teleported="false">
      <template #reference>
        <div class="el-dropdown">
          <el-button :icon="User">{{ t('messages.signIn') }}</el-button>
        </div>
      </template>
      <SignInSignUpForm />
    </el-popover>
  </div>
</template>
