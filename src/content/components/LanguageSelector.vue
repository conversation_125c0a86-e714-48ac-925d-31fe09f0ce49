<script setup>
import { storeToRefs } from 'pinia'
import { onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '../../stores/appState'
import { useLanguageSync } from '../composables/useLanguageSync'

const appState = useAppStateStore()
const { supportedLanguages } = storeToRefs(appState)

const { locale } = useI18n()

const { setLanguage, listenForLanguageChange } = useLanguageSync()

const handleLocaleChange = (value) => {
  setLanguage(value)
}

onMounted(() => {
  listenForLanguageChange()
  if (window.chrome?.runtime?.onMessage) {
    chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
      if (msg?.type === 'LANGUAGE_CHANGED_BROADCAST' && msg.lang) {
        locale.value = msg.lang
        localStorage.setItem('lang', msg.lang)
      }
    })
  }
  if (localStorage.getItem('barLangPopular') === null) {
    localStorage.setItem('barLangPopular', locale.value)
  }
  if (localStorage.getItem('barLangLibrary') === null) {
    localStorage.setItem('barLangLibrary', locale.value)
  }
})

watch(locale, (newLocale) => {
  if (localStorage.getItem('barLangPopular') === null) {
    localStorage.setItem('barLangPopular', newLocale)
  }
  if (localStorage.getItem('barLangLibrary') === null) {
    localStorage.setItem('barLangLibrary', newLocale)
  }
})
</script>

<template>
  <el-select
    v-model="locale"
    class="language-select"
    size="small"
    @change="handleLocaleChange"
    style="min-width: 80px"
    popper-class="language-select-dropdown"
    data-testid="interface-language-selector"
  >
    <el-option
      v-for="lang in supportedLanguages"
      :key="lang.value"
      :label="lang.label"
      :value="lang.value"
      data-testid="interface-language-selector-option"
    />
  </el-select>
</template>

<style scoped>
.language-select :deep(.el-input__wrapper) {
  padding: 0 8px;
}
.language-select :deep(.el-select__caret) {
  margin-left: 4px;
}
</style>
