<template>
  <div class="text-field-component" :class="themeClasses">
    <div class="text-field-wrapper">
      <!-- Label -->
      <label v-if="label" :for="inputId" class="text-field-label" :class="{ required: required }">
        {{ label }}
        <span v-if="required" class="required-indicator" aria-label="required">*</span>
      </label>

      <!-- Textarea Field with Action Buttons -->
      <div class="text-field-input-container">
        <el-input
          :id="inputId"
          ref="inputRef"
          v-model="inputValue"
          type="textarea"
          :placeholder="placeholder || t('textField.placeholder')"
          :rows="rows"
          :autosize="autosize"
          :resize="resize"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          :clearable="clearable"
          :maxlength="maxlength"
          :minlength="minlength"
          :show-word-limit="showWordLimit"
          :validate-event="validateEvent"
          :input-style="inputStyle"
          class="text-field-textarea ai-prompt-textarea"
          :class="inputClasses"
          :data-ai-component="aiComponentId"
          :aria-label="ariaLabel || label || placeholder"
          :aria-describedby="helpText ? `${inputId}-help` : undefined"
          :aria-required="required"
          :aria-invalid="hasError"
          @input="handleInput"
          @change="handleChange"
          @focus="handleFocus"
          @blur="handleBlur"
          @clear="handleClear"
          @keydown="handleKeydown"
        />

        <!-- Action Buttons -->
        <div
          v-if="showActionButtons"
          class="text-field-actions"
          :class="{ 'text-field-actions--disabled': disabled || readonly }"
        >
          <!-- Insert Button -->
          <el-tooltip
            :content="t('textField.insertTooltip')"
            placement="top"
            effect="light"
            :hide-after="50"
            :enterable="false"
          >
            <el-button
              type="default"
              size="small"
              circle
              :disabled="disabled || readonly"
              class="text-field-action-button text-field-insert-button"
              :aria-label="t('textField.insertAriaLabel')"
              @click="handleInsert"
              @mouseenter="highlightTargetElement"
              @mouseleave="removeHighlight"
            >
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
          </el-tooltip>

          <!-- Copy Button -->
          <el-tooltip
            :content="copyTooltipText"
            placement="top"
            effect="light"
            :hide-after="50"
            :enterable="false"
          >
            <el-button
              type="default"
              size="small"
              circle
              :disabled="!inputValue || copyLoading"
              class="text-field-action-button text-field-copy-button"
              :class="{ 'text-field-copy-button--success': copySuccess }"
              :aria-label="t('textField.copyAriaLabel')"
              @click="handleCopy"
            >
              <el-icon v-if="!copyLoading && !copySuccess">
                <CopyDocument />
              </el-icon>
              <el-icon v-else-if="copySuccess">
                <Check />
              </el-icon>
              <el-icon v-else class="is-loading">
                <Loading />
              </el-icon>
            </el-button>
          </el-tooltip>

          <!-- Input Field Icons -->
          <div class="text-field-format-actions">
            <el-tooltip
              content="Select word - example {{input}}"
              placement="top"
              effect="light"
              :hide-after="50"
              :enterable="false"
            >
              <el-button
                type="default"
                size="small"
                circle
                :disabled="disabled || readonly"
                class="text-field-action-button text-field-format-button"
                aria-label="Insert Input Field"
                @click="insertTags('{{', '}}')"
              >
                <el-icon>
                  <Edit />
                </el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip
              content="Select word - example ((textarea))"
              placement="top"
              effect="light"
              :hide-after="50"
              :enterable="false"
            >
              <el-button
                type="default"
                size="small"
                circle
                :disabled="disabled || readonly"
                class="text-field-action-button text-field-format-button"
                aria-label="Insert Textarea Field"
                @click="insertTags('((', '))')"
              >
                <el-icon>
                  <Document />
                </el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip
              content="Select word - example [[option1, option2]]"
              placement="top"
              effect="light"
              :hide-after="50"
              :enterable="false"
            >
              <el-button
                type="default"
                size="small"
                circle
                :disabled="disabled || readonly"
                class="text-field-action-button text-field-format-button"
                aria-label="Insert Select Field"
                @click="insertTags('[[', ']]')"
              >
                <el-icon>
                  <List />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- Help Text -->
      <div
        v-if="helpText"
        :id="`${inputId}-help`"
        class="text-field-help"
        :class="{ error: hasError }"
      >
        {{ helpText }}
      </div>

      <!-- Error Message -->
      <div
        v-if="errorMessage"
        class="text-field-error"
        role="alert"
        :aria-live="hasError ? 'polite' : 'off'"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { Check, CopyDocument, Document, Edit, List, Loading, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useChromeExtension } from '../composables/useChromeExtension.js'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  rows: {
    type: Number,
    default: 3,
  },
  autosize: {
    type: [Boolean, Object],
    default: false,
  },
  resize: {
    type: String,
    default: 'vertical',
    validator: (value) => ['none', 'both', 'horizontal', 'vertical'].includes(value),
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value),
  },
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  maxlength: {
    type: Number,
    default: undefined,
  },
  minlength: {
    type: Number,
    default: undefined,
  },
  showWordLimit: {
    type: Boolean,
    default: false,
  },
  validateEvent: {
    type: Boolean,
    default: true,
  },
  helpText: {
    type: String,
    default: '',
  },
  errorMessage: {
    type: String,
    default: '',
  },
  ariaLabel: {
    type: String,
    default: '',
  },
  enableAIIntegration: {
    type: Boolean,
    default: true,
  },
  insertContent: {
    type: String,
    default: '',
  },
  showActionButtons: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits([
  'update:modelValue',
  'input',
  'change',
  'focus',
  'blur',
  'clear',
  'keydown',
  'ai-content-change',
  'insert',
  'copy-success',
  'copy-error',
])

const { t } = useI18n()

// Chrome extension integration for external text field operations
const { sendMessage, isChromeExtension } = useChromeExtension()

// Refs
const inputRef = ref(null)
const inputId = ref('ai-prompt-lab-text-field')
const aiComponentId = ref(`ai-text-field-${Math.random().toString(36).substring(2, 11)}`)

// Internal state
const inputValue = ref(props.modelValue)
const isAIFocused = ref(false)

// Copy functionality state
const copyLoading = ref(false)
const copySuccess = ref(false)
const copySuccessTimeout = ref(null)

// Target element highlighting state
const isHighlighting = ref(false)
const currentHighlightId = ref(null)

// Computed properties
const hasError = computed(() => !!props.errorMessage)

const themeClasses = computed(() => ({
  'text-field--dark': props.isDarkTheme,
  'text-field--light': !props.isDarkTheme,
  'text-field--disabled': props.disabled,
  'text-field--readonly': props.readonly,
  'text-field--error': hasError.value,
}))

const inputClasses = computed(() => ({
  'text-field-input--error': hasError.value,
}))

const inputStyle = computed(() => ({
  '--text-field-border-color': hasError.value ? 'var(--el-color-danger)' : 'var(--el-border-color)',
}))

const copyTooltipText = computed(() => {
  if (copySuccess.value) {
    return t('textField.copySuccess')
  }
  if (!inputValue.value) {
    return t('textField.copyDisabled')
  }
  return t('textField.copyTooltip')
})

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== inputValue.value) {
      inputValue.value = newValue
    }
  },
)

// Event handlers
const handleInput = (value) => {
  inputValue.value = value
  emit('update:modelValue', value)
  emit('input', value)

  // Emit AI content change for integration
  if (props.enableAIIntegration) {
    emit('ai-content-change', value)
  }
}

const handleChange = (value) => {
  emit('change', value)
}

const handleFocus = (event) => {
  isAIFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isAIFocused.value = false
  emit('blur', event)
}

const handleClear = () => {
  inputValue.value = ''
  emit('update:modelValue', '')
  emit('clear')

  // Emit AI content change for integration
  if (props.enableAIIntegration) {
    emit('ai-content-change', '')
  }
}

const handleKeydown = (event) => {
  emit('keydown', event)
}

// Insert tags function for input field icons
const insertTags = (openTag, closeTag) => {
  const textarea = getTextareaElement()
  if (!textarea) {
    console.warn('Textarea element not found')
    return
  }

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = inputValue.value.substring(start, end)

  const newText =
    inputValue.value.substring(0, start) +
    openTag +
    (selectedText || '') +
    closeTag +
    inputValue.value.substring(end)

  inputValue.value = newText
  emit('update:modelValue', newText)
  emit('input', newText)

  // Emit AI content change for integration
  if (props.enableAIIntegration) {
    emit('ai-content-change', newText)
  }

  nextTick(() => {
    textarea.focus()
    if (selectedText) {
      textarea.setSelectionRange(start + openTag.length, end + openTag.length)
    } else {
      textarea.setSelectionRange(start + openTag.length, start + openTag.length)
    }
  })
}

// Target element highlighting functions
const highlightTargetElement = async () => {
  if (!isChromeExtension() || isHighlighting.value) {
    return
  }

  try {
    isHighlighting.value = true
    const result = await sendMessage({ action: 'highlightTargetElement' })

    if (result.success && result.data?.success) {
      currentHighlightId.value = result.data.highlightId
    }
  } catch (error) {
    console.error('TextFieldComponent: Error highlighting target element:', error)
  }
}

const removeHighlight = async () => {
  if (!isChromeExtension() || !isHighlighting.value) {
    return
  }

  try {
    const result = await sendMessage({ action: 'removeHighlight' })

    currentHighlightId.value = null
    isHighlighting.value = false
  } catch (error) {
    console.error('TextFieldComponent: Error removing highlight:', error)
  }
}

// Action button handlers
const handleInsert = async () => {
  if (props.disabled || props.readonly) return

  // Get current content from the text field
  const contentToInsert = inputValue.value || ''

  if (!contentToInsert.trim()) {
    console.warn('No content to insert - text field is empty')
    ElMessage.warning(t('textField.insertEmptyWarning'))
    return
  }

  // Check if Chrome extension API is available
  if (!isChromeExtension()) {
    insertIntoInternalTextarea()
    return
  }

  let insertionSuccessful = false
  let externalElementInfo = null

  try {
    // Use the same target element logic as highlighting for consistency
    const targetResult = await sendMessage({
      action: 'insertTextIntoTargetElement',
      text: contentToInsert,
    })

    if (targetResult.success && targetResult.data?.success) {
      insertionSuccessful = true
      externalElementInfo = targetResult.data.elementInfo
      ElMessage.success(t('textField.insertSuccessExternal'))
    } else {
      // Fallback: try the old method for backward compatibility
      const focusedResult = await sendMessage({
        action: 'insertTextIntoFocusedField',
        text: contentToInsert,
      })

      if (focusedResult.success && focusedResult.data?.success) {
        insertionSuccessful = true
        externalElementInfo = focusedResult.data.elementInfo
        ElMessage.success(t('textField.insertSuccessExternal'))
      } else {
        ElMessage.error(t('textField.insertErrorExternal'))
      }
    }
  } catch (error) {
    console.error('Error communicating with content script:', error)
  }

  // Emit insert event with details
  emit('insert', {
    currentValue: inputValue.value,
    insertContent: contentToInsert,
    insertedIntoExternal: insertionSuccessful,
    externalElement: externalElementInfo,
  })
}

// Helper function to insert into internal textarea (not used when inserting current content)
const insertIntoInternalTextarea = () => {
  ElMessage.info(t('textField.insertCurrentContentInfo'))
}

const handleCopy = async () => {
  if (!inputValue.value || copyLoading.value) return

  copyLoading.value = true

  try {
    await navigator.clipboard.writeText(inputValue.value)

    // Show success state
    copySuccess.value = true
    emit('copy-success', inputValue.value)

    // Show success message
    ElMessage.success(t('textField.copySuccessMessage'))

    // Reset success state after 2 seconds
    if (copySuccessTimeout.value) {
      clearTimeout(copySuccessTimeout.value)
    }
    copySuccessTimeout.value = setTimeout(() => {
      copySuccess.value = false
      copySuccessTimeout.value = null
    }, 2000)
  } catch (error) {
    console.error('Failed to copy text:', error)
    emit('copy-error', error)
    ElMessage.error(t('textField.copyErrorMessage'))
  } finally {
    copyLoading.value = false
  }
}

// Public methods
const focus = () => {
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus()
    }
  })
}

const blur = () => {
  if (inputRef.value) {
    inputRef.value.blur()
  }
}

const select = () => {
  if (inputRef.value) {
    inputRef.value.select()
  }
}

// AI Integration Methods
const getAIContent = () => {
  return inputValue.value || ''
}

const setAIContent = (content) => {
  inputValue.value = content
  emit('update:modelValue', content)
  emit('ai-content-change', content)

  // Focus the textarea after setting content
  nextTick(() => {
    focus()
  })
}

const focusForAI = () => {
  isAIFocused.value = true
  focus()
}

const getTextareaElement = () => {
  if (inputRef.value && inputRef.value.$el) {
    // For Element Plus el-input with type="textarea", find the actual textarea element
    return (
      inputRef.value.$el.querySelector('textarea') ||
      inputRef.value.$el.querySelector('.el-textarea__inner')
    )
  }
  return null
}

const isAIActive = () => {
  // Allow AI integration when enabled, even if not currently focused
  // This enables prompt insertion from dialogs/menus when component loses focus
  return props.enableAIIntegration
}

// Expose methods for parent components and AI integration
defineExpose({
  focus,
  blur,
  select,
  inputRef,
  // AI Integration methods
  getAIContent,
  setAIContent,
  focusForAI,
  getTextareaElement,
  isAIActive,
  aiComponentId: aiComponentId.value,
})
</script>

<style scoped>
.text-field-component {
  width: 100%;
}

.text-field-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Input Container with Action Buttons */
.text-field-input-container {
  @apply relative flex flex-row items-start gap-2 w-full m-0 box-border;
}

.text-field-input-container .text-field-textarea {
  flex: 1;
}

/* Action Buttons */
.text-field-actions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.125rem;
  transition: opacity 0.2s ease;
}

.text-field-actions--disabled {
  opacity: 0.5;
  pointer-events: none;
}

.text-field-action-button {
  width: 32px;
  height: 32px;
  min-width: 32px;
  min-height: 32px;
  border: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
  color: var(--el-text-color-regular);
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
}

.text-field-action-button:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-field-action-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.text-field-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Icon centering within action buttons */
.text-field-action-button .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-width: 16px;
  min-height: 16px;
  flex-shrink: 0;
}

/* Element Plus button internal structure centering */
.text-field-action-button > span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  box-sizing: border-box;
}

/* SVG icon centering within Element Plus buttons */
.text-field-action-button .el-icon svg {
  display: block;
  margin: auto;
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  flex-shrink: 0;
}

/* Additional centering for nested icon elements */
.text-field-action-button .el-icon > * {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
}

/* Ensure Element Plus button content is centered */
.text-field-action-button.el-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 4px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  width: 32px !important;
  height: 32px !important;
  box-sizing: border-box !important;
  flex-shrink: 0 !important;
}

/* Override Element Plus default button content alignment */
.text-field-action-button.el-button > span {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Insert Button Specific Styles */
.text-field-insert-button:hover {
  border-color: #43a047;
  color: #43a047;
  background: rgba(67, 160, 71, 0.1);
}

/* Copy Button Specific Styles */
.text-field-copy-button:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.text-field-copy-button--success {
  border-color: var(--el-color-success);
  color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.text-field-copy-button--success:hover {
  border-color: var(--el-color-success);
  color: var(--el-color-success);
  background: var(--el-color-success-light-8);
}

/* Format Actions Container */
.text-field-format-actions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* Format Button Specific Styles */
.text-field-format-button:hover {
  border-color: var(--el-color-warning);
  color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

/* Focus and active state alignment preservation */
.text-field-action-button:focus,
.text-field-action-button:active,
.text-field-action-button.el-button:focus,
.text-field-action-button.el-button:active {
  outline: none;
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Prevent Element Plus focus/active state from affecting alignment */
.text-field-action-button.el-button:focus > span,
.text-field-action-button.el-button:active > span {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Label Styles */
.text-field-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.text-field-label.required {
  color: var(--el-text-color-primary);
}

.required-indicator {
  color: var(--el-color-danger);
  font-weight: bold;
}

/* Textarea Styles */
.text-field-textarea {
  width: 95%;
  margin: 0 auto;
  transition: all 0.2s ease;
}

.text-field-textarea--error :deep(.el-textarea__inner) {
  border-color: var(--el-color-danger) !important;
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.text-field-textarea--error :deep(.el-textarea__inner):hover {
  border-color: var(--el-color-danger) !important;
}

.text-field-textarea--error :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-danger) !important;
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

/* Help Text Styles */
.text-field-help {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.text-field-help.error {
  color: var(--el-color-danger);
}

/* Error Message Styles */
.text-field-error {
  font-size: 0.75rem;
  color: var(--el-color-danger);
  line-height: 1.4;
  margin-top: 0.25rem;
}

/* Theme-specific styles */
.text-field--dark .text-field-label {
  color: var(--el-text-color-primary);
}

.text-field--light .text-field-label {
  color: var(--el-text-color-primary);
}

/* Disabled state */
.text-field--disabled .text-field-label {
  color: var(--el-text-color-disabled);
}

/* Responsive Design */
@media (max-width: 768px) {
  .text-field-textarea {
    width: 95%;
    margin: 0 auto;
    font-size: 1rem;
  }

  .text-field-label {
    font-size: 0.9rem;
  }

  .text-field-help,
  .text-field-error {
    font-size: 0.8rem;
  }

  /* Mobile responsive action buttons */
  .text-field-input-container {
    gap: 0.375rem;
  }

  .text-field-action-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
  }

  /* Ensure mobile button centering */
  .text-field-action-button.el-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 36px !important;
    height: 36px !important;
    min-width: 36px !important;
    min-height: 36px !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
  }

  .text-field-action-button.el-button > span {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .text-field-actions {
    gap: 0.375rem;
  }
}

@media (max-width: 480px) {
  .text-field-textarea {
    width: 95%;
    margin: 0 auto;
    font-size: 1rem;
  }

  .text-field-textarea :deep(.el-textarea__inner) {
    height: 500px;
    min-height: 500px;
    font-size: 1rem;
  }

  .text-field-label {
    font-size: 1rem;
  }

  .text-field-help,
  .text-field-error {
    font-size: 0.875rem;
  }

  /* Touch-friendly button sizing for mobile */
  .text-field-action-button {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
  }

  /* Ensure touch-friendly button centering */
  .text-field-action-button.el-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
  }

  .text-field-action-button.el-button > span {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .text-field-input-container {
    gap: 0.5rem;
  }

  .text-field-actions {
    gap: 0.5rem;
  }
}

/* Focus and interaction improvements */
.text-field-textarea :deep(.el-textarea__inner) {
  transition: all 0.2s ease;
  resize: vertical;
}

.text-field-textarea :deep(.el-textarea__inner):hover {
  border-color: var(--el-border-color-hover);
}

.text-field-textarea :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* Textarea-specific styling */
.text-field-textarea :deep(.el-textarea__inner) {
  height: 240px;
  min-height: 240px;
  line-height: 1.5;
  font-family: inherit;
}

/* Ensure proper spacing and alignment */
.text-field-textarea :deep(.el-input__count) {
  background: var(--el-bg-color);
  color: var(--el-text-color-secondary);
}
</style>
