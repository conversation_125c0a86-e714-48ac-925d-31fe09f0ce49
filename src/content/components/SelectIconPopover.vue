<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import CustomIcon from './CustomIcon.vue'
import Tooltip from './Tooltip.vue'

const { t } = useI18n()

const props = defineProps(['name', 'bar'])
const emits = defineEmits(['updateBarIcon'])
const showIcons = ref(false)
const allIcons = ref([])
const defaultIcon = props.bar.icon ? ref(props.bar.icon) : ref('')

const getAllIcons = async () => {
  showIcons.value = true
  if (allIcons.value.length > 0) return
  let icons = await import.meta.glob('../../../public/icons/*.svg')
  // console.log('icons', Object.values(icons), Object.values(icons).length)
  Object.values(icons).forEach((icon) => {
    // console.log('each', icon, icon.name)
    var iconName = icon.name.split('icons/').pop()
    if (iconName.indexOf('logo.svg') > -1) return
    allIcons.value.push(iconName)
  })
}
// console.log('getAllIcons', getAllIcons())

const setIcon = (icon) => {
  // console.log('setIcon: ', icon)
  defaultIcon.value = icon
  emits('updateBarIcon', icon)
}
</script>
<template>
  <el-popover
    placement="bottom"
    :width="'min(550px , 95dvw)'"
    trigger="click"
    @hide="showIcons = false"
    :teleported="false"
  >
    <template #reference>
      <div>
        <el-button class="p-2" link @click="getAllIcons()">
          <Tooltip :content="t('promptConfig.selectIcon')" placement="bottom" effect="light">
            <CustomIcon v-if="defaultIcon" :name="defaultIcon" :size="'30'" />
            <el-icon v-else :size="'30'"><Picture /></el-icon>
          </Tooltip>
        </el-button>
      </div>
    </template>
    <div class="flex flex-col overflow-hidden" style="height: min(50dvh, 300px)">
      <div class="flex justify-between mx-2">
        <el-text>{{ t('promptConfig.selectIcon') }}:</el-text>
        <!-- <el-button text plain @click="setIcon('')"> Reset icon </el-button> -->
      </div>

      <div class="grid grid-cols-5 overflow-y-auto">
        <template v-if="showIcons">
          <el-link
            link
            @click="setIcon('')"
            class="m-2 p-2 rounded-md"
            :class="defaultIcon === '' ? 'bg-neutral-500/20' : 'bg-neutral-500/5'"
          >
            <Tooltip :content="t('promptConfig.emptyIcon')" placement="bottom" effect="light">
              <el-button class="m-2" link>
                <el-icon :size="'50'"><CircleCloseFilled /></el-icon>
              </el-button>
            </Tooltip>
          </el-link>
          <template v-for="icon in allIcons">
            <el-link
              link
              @click="setIcon(icon)"
              class="m-2 p-2 rounded-md"
              :class="icon === defaultIcon ? 'bg-neutral-500/20' : 'bg-neutral-500/5'"
            >
              <Tooltip :content="icon" placement="bottom" effect="light">
                <el-button class="m-2" link>
                  <CustomIcon :name="icon" :size="'50'" />
                </el-button>
              </Tooltip>
            </el-link>
          </template>
        </template>
      </div>
    </div>
  </el-popover>
</template>
<style scoped></style>
