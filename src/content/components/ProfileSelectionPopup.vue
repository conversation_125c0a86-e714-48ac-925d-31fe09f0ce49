<template>
  <Teleport to="body">
    <Transition name="popup-fade">
      <div v-if="visible" class="profile-popup-overlay" @click="handleOverlayClick">
        <div
          class="profile-popup"
          @click.stop
          role="dialog"
          aria-modal="true"
          :aria-label="t('profilePopup.title')"
        >
          <div class="popup-header">
            <h3 class="popup-title">{{ t('profilePopup.title') }}</h3>
            <el-button
              type="text"
              size="small"
              class="close-button"
              @click="closePopup"
              :aria-label="t('profilePopup.close')"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <div class="popup-content">
            <div class="profile-list" role="listbox" :aria-label="t('profilePopup.profileList')">
              <!-- Default Profile -->
              <div
                class="profile-item"
                :class="{
                  'profile-item--active': selectedProfileId === 'default',
                  'profile-item--focused': focusedIndex === 0,
                }"
                role="option"
                :aria-selected="selectedProfileId === 'default'"
                tabindex="0"
                @click="selectProfile('default')"
                @keydown="handleItemKeydown($event, 'default')"
                ref="defaultProfileRef"
              >
                <el-icon class="profile-item-icon">
                  <House />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.default') }}</span>
                <el-icon v-if="selectedProfileId === 'default'" class="check-icon">
                  <Check />
                </el-icon>
              </div>

              <!-- Prompt State -->
              <div
                class="profile-item"
                :class="{
                  'profile-item--active': selectedProfileId === 'prompt',
                  'profile-item--focused': focusedIndex === 1,
                }"
                role="option"
                :aria-selected="selectedProfileId === 'prompt'"
                tabindex="0"
                @click="selectProfile('prompt')"
                @keydown="handleItemKeydown($event, 'prompt')"
                ref="promptProfileRef"
              >
                <el-icon class="profile-item-icon">
                  <Edit />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.prompt') }}</span>
                <el-icon v-if="selectedProfileId === 'prompt'" class="check-icon">
                  <Check />
                </el-icon>
              </div>

              <!-- Search State -->
              <div
                class="profile-item"
                :class="{
                  'profile-item--active': selectedProfileId === 'search',
                  'profile-item--focused': focusedIndex === 2,
                }"
                role="option"
                :aria-selected="selectedProfileId === 'search'"
                tabindex="0"
                @click="selectProfile('search')"
                @keydown="handleItemKeydown($event, 'search')"
                ref="searchProfileRef"
              >
                <el-icon class="profile-item-icon">
                  <Search />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.search') }}</span>
                <el-icon v-if="selectedProfileId === 'search'" class="check-icon">
                  <Check />
                </el-icon>
              </div>

              <!-- Custom Profiles -->
              <div
                v-for="(profile, index) in savedProfiles"
                :key="profile.id"
                class="profile-item"
                :class="{
                  'profile-item--active': selectedProfileId === profile.id,
                  'profile-item--focused': focusedIndex === index + 3,
                }"
                role="option"
                :aria-selected="selectedProfileId === profile.id"
                tabindex="0"
                @click="selectProfile(profile.id)"
                @keydown="handleItemKeydown($event, profile.id)"
                :ref="(el) => (profileRefs[index] = el)"
              >
                <el-icon class="profile-item-icon">
                  <User />
                </el-icon>
                <span class="profile-item-label">{{ profile.name }}</span>
                <el-icon v-if="selectedProfileId === profile.id" class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>

          <div class="popup-footer">
            <div class="keyboard-hints">
              <span class="hint">{{ t('profilePopup.keyboardHints.navigate') }}</span>
              <span class="hint">{{ t('profilePopup.keyboardHints.select') }}</span>
              <span class="hint">{{ t('profilePopup.keyboardHints.close') }}</span>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { Check, Close, Edit, House, Search, User } from '@element-plus/icons-vue'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedProfileId: {
    type: String,
    required: true,
  },
  savedProfiles: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['close', 'profile-selected'])

const { t } = useI18n()

// Focus management
const focusedIndex = ref(0)
const defaultProfileRef = ref(null)
const promptProfileRef = ref(null)
const searchProfileRef = ref(null)
const profileRefs = ref([])

// Total number of profiles (default + prompt + search + custom)
const totalProfiles = computed(() => 3 + props.savedProfiles.length)

// Handle keyboard navigation
const handleKeydown = (event) => {
  if (!props.visible) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      focusedIndex.value = (focusedIndex.value + 1) % totalProfiles.value
      focusCurrentItem()
      break
    case 'ArrowUp':
      event.preventDefault()
      focusedIndex.value = (focusedIndex.value - 1 + totalProfiles.value) % totalProfiles.value
      focusCurrentItem()
      break
    case 'Enter':
    case ' ':
      event.preventDefault()
      selectFocusedProfile()
      break
    case 'Escape':
      event.preventDefault()
      closePopup()
      break
  }
}

// Handle item-specific keydown
const handleItemKeydown = (event, profileId) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      selectProfile(profileId)
      break
  }
}

// Focus the currently focused item
const focusCurrentItem = () => {
  nextTick(() => {
    if (focusedIndex.value === 0) {
      defaultProfileRef.value?.focus()
    } else if (focusedIndex.value === 1) {
      promptProfileRef.value?.focus()
    } else if (focusedIndex.value === 2) {
      searchProfileRef.value?.focus()
    } else {
      const profileIndex = focusedIndex.value - 3
      profileRefs.value[profileIndex]?.focus()
    }
  })
}

// Select the currently focused profile
const selectFocusedProfile = () => {
  if (focusedIndex.value === 0) {
    selectProfile('default')
  } else if (focusedIndex.value === 1) {
    selectProfile('prompt')
  } else if (focusedIndex.value === 2) {
    selectProfile('search')
  } else {
    const profileIndex = focusedIndex.value - 3
    const profile = props.savedProfiles[profileIndex]
    if (profile) {
      selectProfile(profile.id)
    }
  }
}

// Select a profile
const selectProfile = (profileId) => {
  emit('profile-selected', profileId)
  closePopup()
}

// Close popup
const closePopup = () => {
  emit('close')
}

// Handle overlay click
const handleOverlayClick = (event) => {
  if (event.target === event.currentTarget) {
    closePopup()
  }
}

// Set initial focus when popup opens
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // Find the index of the currently selected profile
      if (props.selectedProfileId === 'default') {
        focusedIndex.value = 0
      } else if (props.selectedProfileId === 'prompt') {
        focusedIndex.value = 1
      } else if (props.selectedProfileId === 'search') {
        focusedIndex.value = 2
      } else {
        const profileIndex = props.savedProfiles.findIndex((p) => p.id === props.selectedProfileId)
        focusedIndex.value = profileIndex >= 0 ? profileIndex + 3 : 0
      }

      nextTick(() => {
        focusCurrentItem()
      })
    }
  },
)

// Add/remove keyboard listeners
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.profile-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  /* Highest z-index to ensure popup appears above all other elements
     including Element Plus dialogs (9999999), MenuHideControl popups (9999999),
     and any other overlays in the application */
  z-index: 99999999;
  backdrop-filter: blur(4px);
}

.profile-popup {
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--el-border-color);
  min-width: 400px;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.popup-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.close-button {
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--el-fill-color-light);
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.profile-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0 16px;
}

.profile-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  outline: none;
}

.profile-item:hover {
  background: var(--el-fill-color-light);
}

.profile-item--focused {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.profile-item--active {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.profile-item--active.profile-item--focused {
  background: var(--el-color-primary-light-7);
}

.profile-item-icon {
  font-size: 1rem;
  flex-shrink: 0;
  color: var(--el-text-color-regular);
}

.profile-item--active .profile-item-icon {
  color: var(--el-color-primary);
}

.profile-item-label {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.check-icon {
  font-size: 1rem;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.popup-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid var(--el-border-color-light);
  background: var(--el-fill-color-extra-light);
}

.keyboard-hints {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
}

.hint {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Transitions */
.popup-fade-enter-active,
.popup-fade-leave-active {
  transition: all 0.3s ease;
}

.popup-fade-enter-from,
.popup-fade-leave-to {
  opacity: 0;
}

.popup-fade-enter-from .profile-popup,
.popup-fade-leave-to .profile-popup {
  transform: scale(0.9) translateY(-20px);
}

/* Dark theme adjustments */
.dark .profile-popup-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.dark .profile-popup {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .profile-popup {
    min-width: 320px;
    max-width: 90vw;
    margin: 20px;
  }

  .popup-header {
    padding: 16px 20px 12px;
  }

  .popup-title {
    font-size: 1.1rem;
  }

  .profile-list {
    padding: 0 12px;
  }

  .profile-item {
    padding: 14px 12px;
  }

  .keyboard-hints {
    gap: 12px;
    font-size: 0.7rem;
  }

  .popup-footer {
    padding: 12px 20px 16px;
  }
}
</style>
