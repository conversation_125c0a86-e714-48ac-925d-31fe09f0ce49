<script setup>
import {
  createUserWithEmailAndPassword,
  GoogleAuthProvider,
  sendEmailVerification,
  sendPasswordResetEmail,
  signInWithCredential,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
} from 'firebase/auth'
import { storeToRefs } from 'pinia'
import { computed, inject, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useFirebaseAuth } from 'vuefire'

import { useAppStateStore } from '@/stores/appState'

import { setUserData } from '../firebase'
import { setToPluginStorageLocal } from '../localStorage'

const appState = useAppStateStore()
const { t } = useI18n()
const auth = useFirebaseAuth()

const promo = inject('promo', ref(''))

const safePromoValue = computed(() => {
  try {
    return promo?.value || ''
  } catch (error) {
    console.warn('SignInSignUpForm: Error accessing promo value:', error)
    return ''
  }
})

const { isLoggingIn, isSigningUp } = storeToRefs(appState)

const activeTab = ref('signIn')

// Sign Up
const email = ref('')
const name = ref('')
const password = ref('')
const passwordConfirmation = ref('')
const coupon = ref(safePromoValue.value)
const agree = ref(false)

// Sign In
const userEmail = ref('')
const userPassword = ref('')

const error = ref(null)
const successMessage = ref(null)
const shouldShowVerificationEmailButton = ref(false)
const isSigningInWithGoogle = ref(false)
const userForVerification = ref(null)

async function connectToFirebaseLocalStorage() {
  return new Promise((resolve, reject) => {
    const request = window.indexedDB.open('firebaseLocalStorageDb')
    request.onsuccess = (event) => {
      const db = event.target.result

      db
        .transaction('firebaseLocalStorage')
        .objectStore('firebaseLocalStorage')
        .getAll().onsuccess = (event) => {
        setToPluginStorageLocal('firebaseLocalStorage', event.target.result)
        resolve()
      }
    }

    request.onerror = (event) => {
      reject(event)
    }
  })
}

async function signInWithEmail() {
  successMessage.value = null
  error.value = null
  userForVerification.value = null
  shouldShowVerificationEmailButton.value = false
  try {
    isLoggingIn.value = true
    const userCredentials = await signInWithEmailAndPassword(
      auth,
      userEmail.value,
      userPassword.value,
    )
    if (!userCredentials.user.emailVerified) {
      error.value = t('messages.unverifiedEmail')
      userForVerification.value = userCredentials.user
      shouldShowVerificationEmailButton.value = true

      await signOut(auth)
      return
    }

    await connectToFirebaseLocalStorage()

    error.value = null
  } catch (reason) {
    console.error('Failed signInWithEmailAndPassword', reason)
    if (reason.code === 'auth/missing-password') {
      error.value = t('messages.missingPassword')
    } else if (reason.code === 'auth/wrong-password') {
      error.value = t('messages.wrongPassword')
    } else if (reason.code === 'auth/invalid-email') {
      error.value = t('messages.invalidEmail')
    } else if (reason.code === 'auth/invalid-login-credentials') {
      error.value = t('messages.invalidLoginCredentials')
    } else if (reason.code === 'auth/user-not-found') {
      error.value = t('messages.userNotFound')
    } else {
      error.value = reason
    }
  } finally {
    setTimeout(() => {
      isLoggingIn.value = false
    }, 5000)
  }
}

async function signInWithGoogle() {
  isSigningInWithGoogle.value = true
  chrome.runtime.sendMessage({ action: 'loginWithGoogle' }, async (response) => {
    if (!response) {
      console.error('No response from signInWithGoogle')
      isSigningInWithGoogle.value = false
      return
    }
    if (response.user) {
      const credential = GoogleAuthProvider.credentialFromResult(response)
      let user
      try {
        user = await signInWithCredential(auth, credential)
        if (user.user.emailVerified) {
          await connectToFirebaseLocalStorage()

          if (activeTab.value === 'signUp') {
            setTimeout(async () => {
              await setUserData(user.user.uid, {
                name: user.user.displayName || t('messages.googleUser'),
                coupon: coupon.value,
              })
            }, 5000)

            error.value = null
            activeTab.value = 'signIn'
          }
        } else {
          error.value = t('messages.googleEmailNotVerified')
          userForVerification.value = user.user
          shouldShowVerificationEmailButton.value = true
          await signOut(auth)
        }
      } catch (error) {
        isSigningInWithGoogle.value = false
        console.error('Failed signInWithCredential', error)
      } finally {
        isSigningInWithGoogle.value = false
      }
    } else if (response.error) {
      isSigningInWithGoogle.value = false
      console.error('Login error:', response.error)
    } else {
      isSigningInWithGoogle.value = false
    }
  })
}

async function resetPassword() {
  successMessage.value = null
  try {
    const userCredentials = await sendPasswordResetEmail(auth, userEmail.value)
    successMessage.value = t('messages.resetPasswordEmailSent')
    error.value = null
    setTimeout(() => {
      successMessage.value = null
    }, 5000)
  } catch (reason) {
    console.error('Failed resetPassword', reason)
    if (reason.code === 'auth/missing-email') {
      error.value = t('messages.missingEmail')
    } else if (reason.code === 'auth/invalid-email') {
      error.value = t('messages.invalidEmail')
    } else if (reason.code === 'auth/user-not-found') {
      error.value = t('messages.userNotFound')
    } else {
      error.value = reason
    }
  }
}

async function sendVerificationEmail() {
  error.value = null
  successMessage.value = null
  shouldShowVerificationEmailButton.value = false
  try {
    await sendEmailVerification(userForVerification.value)

    successMessage.value = t('messages.verificationEmailSent')
  } catch (reason) {
    console.error('Failed sendEmailVerification', reason)
    if (reason.code === 'auth/too-many-requests') {
      error.value = t('messages.tooManyRequests')
    } else {
      error.value = reason
    }
  }
}

async function signUp() {
  shouldShowVerificationEmailButton.value = false

  if (!email.value.length) {
    error.value = t('messages.emailCannotBeEmpty')
    return
  }
  if (!password.value.length) {
    error.value = t('messages.passwordCannotBeEmpty')
    return
  }
  if (password.value !== passwordConfirmation.value) {
    error.value = t('messages.passwordsMustMatch')
    return
  }
  if (!agree.value) {
    error.value = t('messages.mustAgreeToLicense')
    return
  }

  try {
    isSigningUp.value = true
    const userCredentials = await createUserWithEmailAndPassword(auth, email.value, password.value)

    await updateProfile(userCredentials.user, {
      displayName: name.value,
    })

    setTimeout(async () => {
      await setUserData(userCredentials.user.uid, { name: name.value, coupon: coupon.value })
      isSigningUp.value = false
    }, 5000)

    error.value = null
    activeTab.value = 'signIn'

    if (!userCredentials.user.emailVerified) {
      userForVerification.value = userCredentials.user
      await sendVerificationEmail()
    }
  } catch (reason) {
    isSigningUp.value = false
    console.error('Failed createUserWithEmailAndPassword', reason)
    if (reason.code === 'auth/email-already-in-use') {
      error.value = t('messages.emailAlreadyInUse')
    } else if (reason.code === 'auth/weak-password') {
      error.value = t('messages.passwordTooShort')
    } else if (reason.code === 'auth/missing-password') {
      error.value = t('messages.missingPassword')
    } else {
      error.value = reason
    }
  }
}
</script>

<template>
  <div>
    <el-tabs v-model="activeTab" stretch>
      <el-tab-pane :label="t('messages.signIn')" name="signIn">
        <el-form
          ref=""
          status-icon
          label-width="120px"
          label-position="top"
          v-loading.fullscreen.lock="isSigningInWithGoogle"
          :element-loading-text="t('messages.signingInWithGoogle')"
        >
          <el-form-item :label="t('messages.email')">
            <el-input
              v-model="userEmail"
              type="email"
              :placeholder="t('messages.enterEmail')"
              data-testid="sign-in-email-input"
            />
          </el-form-item>
          <el-form-item :label="t('messages.password')">
            <el-input
              v-model="userPassword"
              type="password"
              :placeholder="t('messages.enterPassword')"
              show-password
              data-testid="sign-in-password-input"
            />
          </el-form-item>
          <div class="flex flex-col gap-4 justify-center">
            <el-button plain type="primary" @click="signInWithEmail()" data-testid="sign-in-button">
              {{ t('messages.signIn') }}
            </el-button>
            <el-button plain type="primary" @click="signInWithGoogle()">
              {{ t('messages.signInWithGoogle') }}
            </el-button>
            <el-button
              plain
              link
              type="info"
              @click="resetPassword()"
              data-testid="reset-password-button"
            >
              {{ t('messages.resetPassword') }}
            </el-button>
          </div>
        </el-form>
      </el-tab-pane>

      <el-tab-pane :label="t('messages.signUp')" name="signUp">
        <el-form ref="" status-icon label-width="120px" label-position="top">
          <el-form-item :label="t('messages.name')">
            <el-input
              v-model="name"
              type="name"
              :placeholder="t('messages.enterName')"
              data-testid="sign-up-name-input"
            />
          </el-form-item>
          <el-form-item :label="t('messages.email')">
            <el-input
              v-model="email"
              type="email"
              :placeholder="t('messages.enterEmail')"
              data-testid="sign-up-email-input"
            />
          </el-form-item>
          <el-form-item :label="t('messages.password')">
            <el-input
              v-model="password"
              type="password"
              :placeholder="t('messages.enterPassword')"
              show-password
              data-testid="sign-up-password-input"
            />
          </el-form-item>
          <el-form-item :label="t('messages.passwordConfirmation')">
            <el-input
              v-model="passwordConfirmation"
              type="password"
              :placeholder="t('messages.passwordConfirmation')"
              show-password
              data-testid="sign-up-password-confirmation-input"
            />
          </el-form-item>
          <el-form-item :label="t('messages.couponCode')">
            <el-input
              v-model="coupon"
              type="coupon"
              :placeholder="t('messages.enterCouponCode')"
              data-testid="sign-up-coupon-input"
            />
          </el-form-item>

          <div class="flex gap-1 items-center">
            <el-checkbox
              v-model="agree"
              :label="t('messages.iAgreeToTerms')"
              size="large"
              data-testid="sign-up-agree-checkbox"
            >
              {{ t('messages.iAgree') }}
            </el-checkbox>
            <el-link
              type="primary"
              :underline="false"
              href="https://ai-promptlab.com/license-agreement/"
              target="_blank"
              >{{ t('messages.licenseAgreement') }}</el-link
            >
          </div>
          <br />
          <div class="flex flex-col gap-4 justify-center">
            <el-button plain type="primary" @click="signUp()" data-testid="sign-up-button">
              {{ t('messages.signUp') }}
            </el-button>
            <el-button plain type="primary" @click="signInWithGoogle()">
              {{ t('messages.signUpWithGoogle') }}
            </el-button>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
  <div v-if="error" style="margin-top: 10px; text-align: center; color: red">
    {{ error }}
  </div>
  <div v-if="successMessage" style="margin-top: 10px; text-align: center; color: greenyellow">
    {{ successMessage }}
  </div>
  <div v-if="shouldShowVerificationEmailButton" class="text-center mt-1">
    <el-button plain type="primary" @click="sendVerificationEmail()">
      {{ t('messages.sendVerificationEmail') }}
    </el-button>
  </div>
</template>
