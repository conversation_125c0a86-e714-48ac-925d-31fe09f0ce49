<template>
  <div class="profile-switcher">
    <el-tooltip :content="t('profileSwitcher.tooltip')" placement="top">
      <el-dropdown
        @command="switchProfile"
        trigger="click"
        size="small"
        class="profile-dropdown"
        :disabled="isLoading"
      >
        <el-button
          size="small"
          :type="selectedProfileId === 'default' ? 'default' : 'primary'"
          class="profile-button"
          :loading="isLoading"
        >
          <el-icon class="profile-icon">
            <User />
          </el-icon>
          <span class="profile-label">{{ currentProfileName }}</span>
          <el-icon class="dropdown-arrow">
            <ArrowDown />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu class="profile-dropdown-menu">
            <el-dropdown-item
              command="default"
              :class="{ 'active-profile': selectedProfileId === 'default' }"
            >
              <div class="profile-item">
                <el-icon class="profile-item-icon">
                  <House />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.default') }}</span>
                <el-icon v-if="selectedProfileId === 'default'" class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
              command="prompt"
              :class="{ 'active-profile': selectedProfileId === 'prompt' }"
            >
              <div class="profile-item">
                <el-icon class="profile-item-icon">
                  <Edit />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.prompt') }}</span>
                <el-icon v-if="selectedProfileId === 'prompt'" class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
              command="search"
              :class="{ 'active-profile': selectedProfileId === 'search' }"
            >
              <div class="profile-item">
                <el-icon class="profile-item-icon">
                  <Search />
                </el-icon>
                <span class="profile-item-label">{{ t('profileSwitcher.search') }}</span>
                <el-icon v-if="selectedProfileId === 'search'" class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </el-dropdown-item>
            <el-dropdown-item
              v-for="profile in savedProfiles"
              :key="profile.id"
              :command="profile.id"
              :class="{ 'active-profile': selectedProfileId === profile.id }"
            >
              <div class="profile-item">
                <el-icon class="profile-item-icon">
                  <User />
                </el-icon>
                <span class="profile-item-label">{{ profile.name }}</span>
                <el-icon v-if="selectedProfileId === profile.id" class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ArrowDown, Check, Edit, House, Search, User } from '@element-plus/icons-vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  selectedProfileId: {
    type: String,
    required: true,
  },
  savedProfiles: {
    type: Array,
    default: () => [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['profile-changed'])

const { t } = useI18n()

const currentProfileName = computed(() => {
  if (props.selectedProfileId === 'default') {
    return t('profileSwitcher.default')
  }

  if (props.selectedProfileId === 'prompt') {
    return t('profileSwitcher.prompt')
  }

  if (props.selectedProfileId === 'search') {
    return t('profileSwitcher.search')
  }

  const profile = props.savedProfiles.find((p) => p.id === props.selectedProfileId)
  return profile ? profile.name : t('profileSwitcher.default')
})

const switchProfile = (profileId) => {
  if (profileId !== props.selectedProfileId && !props.isLoading) {
    emit('profile-changed', profileId)
  }
}
</script>

<style scoped></style>
