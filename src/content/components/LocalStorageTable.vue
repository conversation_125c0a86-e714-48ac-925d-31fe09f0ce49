<template>
  <div class="table-container">
    <el-table
      :data="data"
      stripe
      height="500"
      class="localStorage-table"
      :class="{ 'dark-theme': isDarkTheme }"
      empty-text=""
    >
      <el-table-column
        prop="key"
        :label="t('localStoragePanel.table.key')"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column
        :label="t('localStoragePanel.table.description')"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ getKeyDescription(row.key) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="preview"
        :label="t('localStoragePanel.table.value')"
        min-width="180"
        show-overflow-tooltip
      />

      <el-table-column prop="type" :label="t('localStoragePanel.table.type')" width="90">
        <template #default="{ row }">
          <el-tag size="small" :type="getTypeTagType(row.type)">
            {{ t(`localStoragePanel.types.${row.type}`) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="size" :label="t('localStoragePanel.table.size')" width="70">
        <template #default="{ row }">
          {{ formatSize(row.size) }}
        </template>
      </el-table-column>

      <el-table-column :label="t('localStoragePanel.table.actions')" width="110" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" text @click="$emit('view-item', row)" :icon="View">
            {{ t('localStoragePanel.actions.view') }}
          </el-button>

          <el-popconfirm
            :title="t('localStoragePanel.confirmations.deleteItem')"
            @confirm="$emit('delete-item', row.key)"
            :width="250"
          >
            <template #reference>
              <el-button type="danger" size="small" text :icon="Delete">
                {{ t('localStoragePanel.actions.delete') }}
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- Empty State -->
    <el-empty
      v-if="data.length === 0"
      :description="t('localStoragePanel.empty.description')"
      class="empty-state"
    >
      <template #image>
        <el-icon :size="64" color="var(--el-color-info)">
          <FolderOpened />
        </el-icon>
      </template>
    </el-empty>
  </div>
</template>

<script setup>
import { Delete, FolderOpened, View } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['view-item', 'delete-item'])

const { t } = useI18n()

const getKeyDescription = (key) => {
  // Check for exact matches first
  try {
    const exactMatch = t(`localStoragePanel.descriptions.${key}`)
    if (exactMatch && exactMatch !== `localStoragePanel.descriptions.${key}`) {
      return exactMatch
    }
  } catch (e) {
    // Key doesn't exist, continue to pattern matching
  }

  // Check for pattern matches
  if (key.startsWith('component_state_')) {
    return t('localStoragePanel.descriptions.component_state')
  }

  if (key.startsWith('menuControlState_')) {
    return t('localStoragePanel.descriptions.menuControlState')
  }

  if (key.includes('_version') || key.endsWith('_version')) {
    return t('localStoragePanel.descriptions.version')
  }

  if (key.toLowerCase().includes('theme')) {
    return t('localStoragePanel.descriptions.theme')
  }

  if (key.toLowerCase().includes('view')) {
    return t('localStoragePanel.descriptions.view')
  }

  if (key.toLowerCase().includes('sort')) {
    return t('localStoragePanel.descriptions.sort')
  }

  if (key.toLowerCase().includes('lang') || key.toLowerCase().includes('locale')) {
    return t('localStoragePanel.descriptions.locale')
  }

  // Default fallback
  return t('localStoragePanel.descriptions.default')
}

const getTypeTagType = (type) => {
  const typeMap = {
    string: '',
    object: 'success',
    array: 'warning',
    number: 'info',
    boolean: 'danger',
  }
  return typeMap[type] || ''
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.table-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  overflow: hidden;
}

.localStorage-table {
  width: 100%;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

/* Table responsive adjustments */
@media (max-width: 1024px) {
  .localStorage-table :deep(.el-table__cell) {
    padding: 8px 6px;
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .localStorage-table :deep(.el-table__cell) {
    padding: 6px 4px;
    font-size: 12px;
  }

  /* Hide description column on very small screens to save space */
  .localStorage-table
    :deep(.el-table__header-wrapper .el-table__header .el-table__cell:nth-child(3)),
  .localStorage-table :deep(.el-table__body-wrapper .el-table__body .el-table__cell:nth-child(3)) {
    display: none;
  }
}

/* Tablet responsive adjustments */
@media (max-width: 1200px) and (min-width: 769px) {
  .localStorage-table :deep(.el-table__cell) {
    padding: 8px 5px;
  }
}
</style>
