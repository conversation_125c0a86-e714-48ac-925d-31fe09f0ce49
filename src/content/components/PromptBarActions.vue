<script setup>
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { TABS } from '../utils.js'
import Tooltip from './Tooltip.vue'

const props = defineProps({
  tabName: {
    type: String,
    required: true,
  },
  isTabEmpty: {
    type: Boolean,
    required: true,
  },
  selectedBarId: {
    type: String,
    default: '',
  },
  selectedBar: {
    type: Object,
    default: () => ({}),
  },
  canAddMoreFreePromptBars: {
    type: Boolean,
    required: true,
  },
  hasSubscription: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['editPromptBar', 'removePromptBar', 'openAddPromptBarDialog'])

const { t } = useI18n()

const btnAddType = ref('')
const btnEditType = ref('')
const btnDeleteType = ref('')
</script>

<template>
  <div class="flex gap-1">
    <Tooltip
      v-if="[TABS.MyPrompts].includes(tabName)"
      class="box-item"
      effect="light"
      :content="t('messages.addANewPromptBar')"
      placement="bottom"
    >
      <el-button
        :disabled="!isTabEmpty && !canAddMoreFreePromptBars && !hasSubscription"
        :type="btnAddType"
        plain
        class="no-bg relative"
        data-testid="add-prompt-bar-button"
        @click="$emit('openAddPromptBarDialog')"
        :class="{ 'z-10': isTabEmpty }"
        @mouseenter="btnAddType = 'success'"
        @mouseleave="btnAddType = ''"
      >
        <el-icon><Plus /></el-icon>
        <span class="hidden xl:block">{{ t('btn.add') }}</span>
        <span
          v-if="isTabEmpty && !selectedBarId"
          class="animate-ping absolute inset-0 inline-flex h-full w-full rounded-full bg-green-500 opacity-50"
        ></span>
      </el-button>
    </Tooltip>

    <Tooltip
      v-if="
        [TABS.MyPrompts, TABS.Team].includes(tabName) ||
        ([TABS.Public].includes(tabName) && selectedBar?.isEditable)
      "
      class="box-item"
      effect="light"
      :content="t('messages.editCurrentPromptBar')"
      placement="bottom"
    >
      <el-button
        :type="btnEditType"
        plain
        data-testid="edit-prompt-bar-button"
        class="no-bg"
        @click="$emit('editPromptBar')"
        :disabled="isTabEmpty || !selectedBarId"
        @mouseenter="btnEditType = 'primary'"
        @mouseleave="btnEditType = ''"
      >
        <el-icon><Edit /></el-icon>
        <span class="hidden xl:block">{{ t('btn.edit') }}</span>
      </el-button>
    </Tooltip>

    <el-popconfirm
      v-if="[TABS.MyPrompts].includes(tabName)"
      width="220"
      :confirm-button-text="t('btn.remove')"
      :cancel-button-text="t('btn.cancel')"
      @confirm="$emit('removePromptBar')"
      :title="t('messages.areYouSureToDeleteThis')"
    >
      <template #reference>
        <el-button
          :type="btnDeleteType"
          plain
          class="no-bg"
          data-testid="delete-prompt-bar-button"
          :disabled="isTabEmpty || !selectedBarId"
          @mouseenter="btnDeleteType = 'danger'"
          @mouseleave="btnDeleteType = ''"
        >
          <el-icon><Delete /></el-icon>
          <span class="hidden xl:block">{{ t('btn.remove') }}</span>
        </el-button>
      </template>
    </el-popconfirm>
  </div>
</template>
