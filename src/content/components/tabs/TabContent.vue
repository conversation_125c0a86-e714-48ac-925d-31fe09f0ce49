<script setup>
import { useI18n } from 'vue-i18n'

import { TABS } from '@/content/utils.js'

import MyPromptsTab from './MyPromptsTab.vue'
import OtherTabsContent from './OtherTabsContent.vue'

const { t } = useI18n()

const props = defineProps({
  tabName: String,
})

const emit = defineEmits([
  'editPromptBar',
  'removePromptBar',
  'openAddPromptBarDialog',
  'openSubscriptionDialog',
  'openEditPromptBarDialog',
])
</script>

<template>
  <MyPromptsTab
    v-if="tabName === TABS.MyPrompts"
    @editPromptBar="$emit('editPromptBar')"
    @removePromptBar="$emit('removePromptBar')"
    @openAddPromptBarDialog="$emit('openAddPromptBarDialog')"
    @openSubscriptionDialog="$emit('openSubscriptionDialog')"
    @openEditPromptBarDialog="$emit('openEditPromptBarDialog')"
  />
  <OtherTabsContent v-else />
</template>
