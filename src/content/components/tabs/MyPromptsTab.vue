<script setup>
import { storeToRefs } from 'pinia'

import { useAppStateStore } from '../../../stores/appState'
import MyPromptsEmptyState from '../MyPromptsEmptyState.vue'
import PromptsMenu from '../PromptsMenu.vue'

const emit = defineEmits([
  'editPromptBar',
  'removePromptBar',
  'openAddPromptBarDialog',
  'openSubscriptionDialog',
  'openEditPromptBarDialog',
])

const appState = useAppStateStore()
const {
  hasSubscription,
  isSelectedBarLoading,
  selectedBarId,
  selectedBar,
  isAdmin,
  canAddMoreFreePromptBars,
  isTabEmpty,
} = storeToRefs(appState)
</script>

<template>
  <div
    v-if="isSelectedBarLoading"
    class="inline-flex justify-center items-center w-full gap-1 min-h-[32px]"
  >
    <el-skeleton animated class="flex gap-4 justify-center items-center w-full">
      <template #template>
        <el-skeleton-item variant="text" style="width: 10%" />
        <el-skeleton-item variant="text" style="width: 10%" />
        <el-skeleton-item variant="text" style="width: 10%" />
      </template>
    </el-skeleton>
  </div>
  <div v-else-if="selectedBar && selectedBar?.promptMenu?.length > 0" class="w-full max-w-[100%]">
    <PromptsMenu
      :selectedBar="selectedBar"
      :isAdmin="isAdmin"
      :destination="selectedBar.destination"
    />
  </div>
  <div
    v-else
    class="flex-1 flex justify-center items-center text-center text-gray-800 dark:text-gray-400 my-1"
  >
    <MyPromptsEmptyState
      :has-subscription="hasSubscription"
      :can-add-more-free-prompt-bars="canAddMoreFreePromptBars"
      :selected-bar-id="selectedBarId"
      :selected-bar="selectedBar"
      :is-tab-empty="isTabEmpty"
      @open-subscription-dialog="$emit('openSubscriptionDialog')"
      @edit-prompt-bar="$emit('editPromptBar')"
      @open-edit-prompt-bar-dialog="$emit('openEditPromptBarDialog')"
      @open-add-prompt-bar-dialog="$emit('openAddPromptBarDialog')"
    />
  </div>
</template>
