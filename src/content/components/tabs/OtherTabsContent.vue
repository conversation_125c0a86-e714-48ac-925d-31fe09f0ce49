<script setup>
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

import PromptsMenu from '../PromptsMenu.vue'

const appState = useAppStateStore()
const { t } = useI18n()

const { isSelectedBarLoading, selectedBar } = storeToRefs(appState)

// Handle prompt click events from RecursiveMenu
const handlePromptClick = (eventData) => {
  // The PromptsMenu component will handle the actual insertion
  // This is just to ensure the event is properly received
}
</script>

<template>
  <div
    v-if="isSelectedBarLoading"
    class="inline-flex justify-center items-center w-full gap-1 min-h-[32px]"
  >
    <el-skeleton animated class="flex gap-4 justify-center items-center w-full">
      <template #template>
        <el-skeleton-item variant="text" style="width: 10%" />
        <el-skeleton-item variant="text" style="width: 10%" />
        <el-skeleton-item variant="text" style="width: 10%" />
      </template>
    </el-skeleton>
  </div>
  <div v-else-if="selectedBar && selectedBar?.promptMenu?.length > 0" class="w-full max-w-[100%]">
    <PromptsMenu
      :selectedBar="selectedBar"
      :destination="selectedBar.destination"
      @onPromptClick="handlePromptClick"
    />
  </div>
  <div
    v-else
    class="flex-1 flex justify-center items-center text-center text-gray-800 dark:text-gray-400 my-1"
    data-testid="no-bar-selected"
  >
    <template v-if="selectedBar?.promptMenu?.length === 0"> {{ t('bar.empty') }} </template>
    <template v-else-if="!selectedBar?.id"> {{ t('bar.noBarSelected') }} </template>
  </div>
</template>
