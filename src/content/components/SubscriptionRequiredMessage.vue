<script setup>
import { useI18n } from 'vue-i18n'

const props = defineProps({
  tabName: String,
})
const { t } = useI18n()

const emit = defineEmits(['openSubscriptionDialog'])
</script>

<template>
  <div class="h-[36px] flex items-center justify-center">
    <p>{{ t('messages.subscriptionRequiredToUse') + ' ' + tabName }}</p>
  </div>
  <div class="min-h-[32px] flex items-center justify-center">
    <el-button
      type="info"
      plain
      @click="$emit('openSubscriptionDialog', 'tab')"
      class="text-purple-400 hover:!bg-purple-500 hover:!text-white"
    >
      {{ t('subscription.buySubscription') }}
    </el-button>
  </div>
</template>
