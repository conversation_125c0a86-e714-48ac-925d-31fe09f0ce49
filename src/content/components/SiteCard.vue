<template>
  <el-card
    :class="['site-card', { 'edit-mode': showDragHandle, 'dark-theme': isDarkTheme }]"
    :draggable="showDragHandle"
    @dragstart="$emit('dragstart', $event)"
    @dragover="$emit('dragover', $event)"
    @drop="$emit('drop', $event)"
    shadow="hover"
    :body-style="{ padding: '1rem', position: 'relative' }"
  >
    <!-- Config button (gear icon) - left side in edit mode -->
    <el-tooltip v-if="showConfigIcon" :content="t('siteCard.config.tooltip')" placement="top">
      <el-button
        @click.stop="handleConfigClick"
        class="config-button"
        size="small"
        :icon="Setting"
        circle
        type="info"
      />
    </el-tooltip>

    <!-- Remove/Add button - right side top -->
    <el-tooltip
      v-if="shouldShowFavoriteButton"
      :content="showRemoveIcon ? t('sidePanel.removeFromFavorites') : t('sidePanel.addToFavorites')"
      placement="top"
    >
      <el-button
        @click.stop="toggleFavorite"
        class="favorite-button"
        :class="favoriteButtonClass"
        size="small"
        :type="favoriteButtonType"
        :icon="showRemoveIcon ? Close : Plus"
        circle
      />
    </el-tooltip>

    <div class="card-content">
      <div class="site-info">
        <el-text type="primary" size="large" class="site-name" truncated>
          {{ site.name }}
        </el-text>

        <el-text type="info" size="small" class="site-url" truncated>
          {{ displayUrl }}
        </el-text>

        <div v-if="site.tags && site.tags.length > 0" class="site-tags">
          <el-tag
            v-for="tag in site.tags.slice(0, 3)"
            :key="tag"
            size="small"
            :style="getTagCSSStyle(tag)"
            :data-selected="selectedTags.includes(tag)"
            @click.stop="handleTagClick(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-tag v-if="site.tags.length > 3" size="small" type="info" class="tag-more">
            +{{ site.tags.length - 3 }}
          </el-tag>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <el-tooltip :content="t('sitesGrid.actions.openInCurrentTab')" placement="top">
            <el-button
              @click.stop="openInCurrentTab"
              class="action-button action-button-primary"
              size="small"
              :icon="Link"
              type="default"
              plain
            >
              {{ t('sitesGrid.actions.open') }}
            </el-button>
          </el-tooltip>
          <el-tooltip :content="t('sitesGrid.actions.openInNewTab')" placement="top">
            <el-button
              @click.stop="openInNewTab"
              class="action-button action-button-success"
              size="small"
              :icon="TopRight"
              type="default"
              plain
            >
              {{ t('sitesGrid.actions.openNewTab') }}
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { Close, Link, Plus, Setting, TopRight } from '@element-plus/icons-vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useMyAISettings } from '../composables/useMyAISettings.js'
import { useTagColors } from '../composables/useTagColors.js'

const props = defineProps({
  site: {
    type: Object,
    required: true,
    validator: (site) => site && typeof site.url === 'string' && typeof site.name === 'string',
  },
  isDarkTheme: {
    type: Boolean,
    required: true,
  },
  selectedTags: {
    type: Array,
    default: () => [],
  },
  showConfigIcon: {
    type: Boolean,
    default: false,
  },
  showRemoveIcon: {
    type: Boolean,
    default: false,
  },
  showDragHandle: {
    type: Boolean,
    default: false,
  },
  isSearchTab: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'click',
  'favorite-changed',
  'config-click',
  'tag-selected',
  'dragstart',
  'dragover',
  'drop',
])

const { t } = useI18n()
const { myAI, addToMyAI, removeFromMyAI, isMyAIItem } = useMyAISettings()

const displayUrl = computed(() => {
  return props.site.url.replace('https://', '').split('/')[0]
})

const isFavorite = computed(() => {
  return isMyAIItem(props.site.url)
})

// Determine when to show the favorite button
const shouldShowFavoriteButton = computed(() => {
  if (props.isSearchTab) {
    // Search AI tab: always show add button
    return true
  } else {
    // My AI tab: only show remove button in edit mode
    return props.showRemoveIcon
  }
})

// Determine button styling class
const favoriteButtonClass = computed(() => {
  if (props.isSearchTab) {
    return 'favorite-button-search'
  } else if (props.showRemoveIcon) {
    return 'favorite-button-remove'
  }
  return ''
})

// Determine button type for Element Plus styling
const favoriteButtonType = computed(() => {
  if (props.showRemoveIcon) {
    return 'danger'
  } else if (props.isSearchTab) {
    return 'default' // Start with neutral/gray type
  }
  return 'success'
})

const handleConfigClick = () => {
  emit('config-click', props.site)
}

const toggleFavorite = () => {
  let wasAdded = false

  if (props.isSearchTab) {
    // W Search AI zawsze dodaj (pozwól na duplikaty)
    addToMyAI(props.site)
    wasAdded = true
  } else {
    // W My AI trybie normalnym - dodaj/usuń
    if (props.showRemoveIcon) {
      // W trybie edycji My AI - usuń konkretny element po ID
      removeFromMyAI(props.site)
      wasAdded = false
    } else if (isFavorite.value) {
      removeFromMyAI(props.site.url)
      wasAdded = false
    } else {
      addToMyAI(props.site)
      wasAdded = true
    }
  }

  emit('favorite-changed', wasAdded)
}

const getTagCSSStyle = (tag) => {
  const { getTagStyle } = useTagColors(props.isDarkTheme)
  const isSelected = props.selectedTags.includes(tag)
  return getTagStyle(tag, isSelected)
}

// Tag click handler
const handleTagClick = (tag) => {
  emit('tag-selected', tag)
}

// Action button handlers
const openInCurrentTab = () => {
  // Ensure URL has proper protocol
  let url = props.site.url
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://' + url
  }

  try {
    // For Chrome extensions, we might need to use different methods
    if (typeof chrome !== 'undefined' && chrome.tabs) {
      // If we have access to Chrome tabs API, use it
      chrome.tabs.update({ url: url })
    } else {
      // Otherwise use standard navigation
      window.location.href = url
    }
  } catch (error) {
    console.error('Error opening URL in current tab:', error)
    try {
      // Fallback: try using window.location.assign
      window.location.assign(url)
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError)
      // Last resort: try window.open with _self target
      window.open(url, '_self')
    }
  }
}

const openInNewTab = () => {
  window.open(props.site.url, '_blank')
}
</script>

<style scoped>
.site-card {
  transition: all 0.2s ease;
  position: relative;
}

.site-card.edit-mode {
  cursor: grab;
}

.site-card.edit-mode:active {
  cursor: grabbing;
}

.config-button {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  z-index: 10;
  opacity: 0.8;
}

.favorite-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;
  opacity: 0.8;
  transition: all 0.2s ease;
}

/* Search AI tab - Add to favorites button styling */
.favorite-button-search {
  background-color: #f5f5f5 !important;
  border-color: #bdbdbd !important;
  color: #424242 !important;
}

.favorite-button-search:hover {
  background-color: #43a047 !important;
  border-color: #43a047 !important;
  color: white !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(67, 160, 71, 0.3);
}

/* My AI tab - Remove button styling (only visible in edit mode) */
.favorite-button-remove {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
  color: white !important;
}

.favorite-button-remove:hover {
  background-color: #d32f2f !important;
  border-color: #d32f2f !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.site-card:hover .config-button,
.site-card:hover .favorite-button {
  opacity: 1;
}

.card-content {
  display: flex;
  align-items: flex-start;
  min-height: 60px;
}

.site-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.site-card.edit-mode .site-info {
  margin-left: 2rem;
}

.site-url {
  margin: 0 0 0.5rem 0;
}

.site-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  justify-content: center;
}

.action-button {
  flex: 1;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 1;
}

/* Primary action button hover */
.action-button-primary:hover {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
  color: white !important;
}

/* Success action button hover */
.action-button-success:hover {
  background-color: var(--el-color-success) !important;
  border-color: var(--el-color-success) !important;
  color: white !important;
}
</style>
