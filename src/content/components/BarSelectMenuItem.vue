<script setup>
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '@/stores/appState'

import CustomIcon from '../components/CustomIcon.vue'
import { useBarFiltering } from '../composables/useBarFiltering'
import { TABS } from '../utils'
import Tooltip from './Tooltip.vue'

const appState = useAppStateStore()

defineProps({
  bar: {
    type: Object,
    required: true,
  },
  tabName: {
    type: String,
    required: true,
  },
  formatFirebaseDate: {
    type: Function,
    required: true,
  },
})

const { t } = useI18n()
const { onTagClick, getStyle } = useBarFiltering()
</script>

<template>
  <div class="selectMenu leading-snug py-2">
    <div class="truncate">
      <div
        v-if="bar.createdAt?.seconds"
        class="float-right text-[0.65rem] opacity-75 text-neutral-400"
      >
        {{ formatFirebaseDate(bar.createdAt, false) }}
      </div>
      <CustomIcon :name="bar?.icon" :size="'24'" class="mr-2" />
      <span class="truncate" :style="getStyle(bar.name, 'bar')"> {{ bar.name }}</span>
      <span class="opacity-50 italic truncate" v-if="tabName !== TABS.Popular && bar.owner?.name">
        - {{ bar.owner?.name }}
      </span>
    </div>
    <div class="text-xs opacity-70 text-neutral-400 truncate py-1" v-if="bar.description">
      {{ bar.description }}
    </div>
    <div v-if="bar.tags && bar.tags.length != 0" class="flex items-center">
      <div class="text-xs opacity-70 text-neutral-400 py-2 mr-2">{{ t('bar.tags') }}:</div>
      <div class="flex gap-1 overflow-auto py-1">
        <el-button
          v-for="tag in bar.tags"
          :key="tag"
          size="small"
          text
          bg
          :style="getStyle(tag, 'tag')"
          class="tagInSelect"
          @click="onTagClick($event, tag)"
        >
          {{ tag }}
        </el-button>
      </div>
    </div>
    <div
      v-if="bar.destination"
      class="flex items-center justify-between gap-1 text-xs opacity-70 text-neutral-400 py-1"
    >
      <div class="truncate">
        <Tooltip
          :content="`${t('bar.destination')}: ${bar.destination}`"
          effect="light"
          :enterable="false"
          >{{ t('bar.destination') }}: {{ bar.destination }}
        </Tooltip>
      </div>
      <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-150">
        <Tooltip
          :content="`${t('btn.openUrl')}: ${bar.destination}`"
          effect="light"
          :enterable="false"
        >
          <el-button
            plain
            text
            bg
            size="small"
            @click="appState.goToOrigin($event, bar.destination, bar.id)"
            >{{ t('btn.openUrl') }}</el-button
          >
        </Tooltip>
        <Tooltip
          :content="`${t('btn.openUrlInNewTab')}: ${bar.destination}`"
          effect="light"
          :enterable="false"
        >
          <el-button
            class="!ml-1"
            plain
            text
            bg
            size="small"
            @click="appState.goToOrigin($event, bar.destination, bar.id, '_blank')"
            >{{ t('btn.openInNewTab') }}</el-button
          >
        </Tooltip>
      </div>
    </div>
  </div>
</template>
