<template>
  <el-dialog
    v-model="isVisible"
    :title="t('localStoragePanel.title')"
    width="clamp(600px, 95vw, 1400px)"
    height="clamp(600px, 85vh, 900px)"
    top="5vh"
    @close="handleClose"
    destroy-on-close
    class="localStorage-panel-modal"
    :class="{ 'dark-theme': isDarkTheme }"
  >
    <div class="localStorage-panel-content">
      <!-- Search and Actions Bar -->
      <div class="actions-bar">
        <el-input
          v-model="searchQuery"
          :placeholder="t('localStoragePanel.search.placeholder')"
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <div class="action-buttons">
          <el-button type="primary" @click="exportData" :icon="Download" size="small">
            {{ t('localStoragePanel.actions.export') }}
          </el-button>

          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".json"
            @change="handleFileSelect"
            class="import-upload"
          >
            <el-button type="success" :icon="Upload" size="small">
              {{ t('localStoragePanel.actions.import') }}
            </el-button>
          </el-upload>

          <el-popconfirm
            :title="t('localStoragePanel.confirmations.clearAll')"
            @confirm="clearAllData"
            :width="300"
          >
            <template #reference>
              <el-button type="danger" :icon="Delete" size="small">
                {{ t('localStoragePanel.actions.clearAll') }}
              </el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>

      <!-- Data Table -->
      <div class="table-container">
        <el-table
          :data="filteredData"
          stripe
          height="400"
          class="localStorage-table"
          :class="{ 'dark-theme': isDarkTheme }"
          empty-text=""
        >
          <el-table-column
            prop="key"
            :label="t('localStoragePanel.table.key')"
            min-width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="preview"
            :label="t('localStoragePanel.table.value')"
            min-width="200"
            show-overflow-tooltip
          />

          <el-table-column prop="type" :label="t('localStoragePanel.table.type')" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getTypeTagType(row.type)">
                {{ t(`localStoragePanel.types.${row.type}`) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="size" :label="t('localStoragePanel.table.size')" width="80">
            <template #default="{ row }">
              {{ formatSize(row.size) }}
            </template>
          </el-table-column>

          <el-table-column :label="t('localStoragePanel.table.actions')" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" text @click="viewItem(row)" :icon="View">
                {{ t('localStoragePanel.actions.view') }}
              </el-button>

              <el-popconfirm
                :title="t('localStoragePanel.confirmations.deleteItem')"
                @confirm="deleteItem(row.key)"
                :width="250"
              >
                <template #reference>
                  <el-button type="danger" size="small" text :icon="Delete">
                    {{ t('localStoragePanel.actions.delete') }}
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- Empty State -->
        <el-empty
          v-if="localStorageData.length === 0"
          :description="t('localStoragePanel.empty.description')"
          class="empty-state"
        >
          <template #image>
            <el-icon :size="64" color="var(--el-color-info)">
              <FolderOpened />
            </el-icon>
          </template>
        </el-empty>
      </div>
    </div>
  </el-dialog>

  <!-- View Item Dialog -->
  <el-dialog
    v-model="viewDialogVisible"
    :title="t('localStoragePanel.viewDialog.title')"
    width="clamp(500px, 90vw, 800px)"
    destroy-on-close
    class="view-item-dialog"
    :class="{ 'dark-theme': isDarkTheme }"
  >
    <div v-if="selectedItem" class="view-item-content">
      <div class="item-info">
        <p>
          <strong>{{ t('localStoragePanel.viewDialog.key') }}</strong> {{ selectedItem.key }}
        </p>
        <p>
          <strong>{{ t('localStoragePanel.viewDialog.type') }}</strong>
          {{ t(`localStoragePanel.types.${selectedItem.type}`) }}
        </p>
        <p>
          <strong>{{ t('localStoragePanel.viewDialog.size') }}</strong>
          {{ formatSize(selectedItem.size) }}
        </p>
      </div>

      <div class="item-value">
        <p>
          <strong>{{ t('localStoragePanel.viewDialog.value') }}</strong>
        </p>
        <el-input
          v-model="selectedItem.formattedValue"
          type="textarea"
          :rows="15"
          readonly
          class="value-textarea"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { Delete, Download, FolderOpened, Search, Upload, View } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()

// Reactive data
const searchQuery = ref('')
const localStorageData = ref([])
const viewDialogVisible = ref(false)
const selectedItem = ref(null)
const uploadRef = ref(null)

// Computed properties
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const filteredData = computed(() => {
  if (!searchQuery.value) return localStorageData.value

  const query = searchQuery.value.toLowerCase()
  return localStorageData.value.filter(
    (item) => item.key.toLowerCase().includes(query) || item.preview.toLowerCase().includes(query),
  )
})

// Methods
const loadLocalStorageData = () => {
  const data = []

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      const value = localStorage.getItem(key)
      const item = processLocalStorageItem(key, value)
      data.push(item)
    }
  }

  localStorageData.value = data.sort((a, b) => a.key.localeCompare(b.key))
}

const processLocalStorageItem = (key, value) => {
  let type = 'string'
  let formattedValue = value
  let preview = value

  try {
    const parsed = JSON.parse(value)
    if (Array.isArray(parsed)) {
      type = 'array'
      formattedValue = JSON.stringify(parsed, null, 2)
      preview = `[${parsed.length} items]`
    } else if (typeof parsed === 'object' && parsed !== null) {
      type = 'object'
      formattedValue = JSON.stringify(parsed, null, 2)
      const keys = Object.keys(parsed)
      preview = `{${keys.length} keys}`
    } else if (typeof parsed === 'number') {
      type = 'number'
      formattedValue = value
      preview = value
    } else if (typeof parsed === 'boolean') {
      type = 'boolean'
      formattedValue = value
      preview = value
    }
  } catch (e) {
    // Keep as string if not valid JSON
    if (value.length > 50) {
      preview = value.substring(0, 50) + '...'
    }
  }

  return {
    key,
    value,
    formattedValue,
    preview,
    type,
    size: new Blob([value]).size,
  }
}

const getTypeTagType = (type) => {
  const typeMap = {
    string: '',
    object: 'success',
    array: 'warning',
    number: 'info',
    boolean: 'danger',
  }
  return typeMap[type] || ''
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const viewItem = (item) => {
  selectedItem.value = item
  viewDialogVisible.value = true
}

const deleteItem = (key) => {
  localStorage.removeItem(key)
  loadLocalStorageData()
  ElMessage.success(t('localStoragePanel.messages.itemDeleted'))
}

const clearAllData = () => {
  localStorage.clear()
  loadLocalStorageData()
  ElMessage.success(t('localStoragePanel.messages.allCleared'))
}

const exportData = () => {
  const data = {}
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      data[key] = localStorage.getItem(key)
    }
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `localStorage-backup-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success(t('localStoragePanel.messages.exportSuccess'))
}

const handleFileSelect = (file) => {
  if (!file || !file.raw) {
    ElMessage.error(t('localStoragePanel.messages.importError'))
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target.result)
      if (typeof data !== 'object' || data === null) {
        ElMessage.error(t('localStoragePanel.messages.invalidJson'))
        return
      }
      importData(data)
    } catch (error) {
      ElMessage.error(t('localStoragePanel.messages.invalidJson'))
    }
  }
  reader.onerror = () => {
    ElMessage.error(t('localStoragePanel.messages.importError'))
  }
  reader.readAsText(file.raw)
}

const importData = (data) => {
  try {
    let importedCount = 0
    Object.entries(data).forEach(([key, value]) => {
      if (typeof key === 'string' && typeof value === 'string') {
        localStorage.setItem(key, value)
        importedCount++
      }
    })

    if (importedCount > 0) {
      loadLocalStorageData()
      ElMessage.success(t('localStoragePanel.messages.importSuccess'))
    } else {
      ElMessage.warning(t('localStoragePanel.messages.noData'))
    }
  } catch (error) {
    console.error('Import error:', error)
    ElMessage.error(t('localStoragePanel.messages.importError'))
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}

// Lifecycle
onMounted(() => {
  loadLocalStorageData()
})

// Watch for visibility changes to reload data
watch(isVisible, (newValue) => {
  if (newValue) {
    loadLocalStorageData()
  }
})
</script>

<style scoped>
.localStorage-panel-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.actions-bar {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.import-upload {
  display: inline-block;
}

.table-container {
  flex: 1;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.localStorage-table {
  flex: 1;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-item-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item-info {
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.item-info p {
  margin: 4px 0;
  font-size: 14px;
}

.item-value {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* Dark theme styles */
.localStorage-panel-modal.dark-theme :deep(.el-dialog) {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
}

.localStorage-panel-modal.dark-theme :deep(.el-dialog__header) {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
}

.localStorage-table.dark-theme :deep(.el-table) {
  background: var(--el-bg-color-page);
}

.localStorage-table.dark-theme :deep(.el-table__row) {
  background: var(--el-bg-color-page);
}

.localStorage-table.dark-theme :deep(.el-table__row--striped) {
  background: var(--el-fill-color-lighter);
}

.view-item-dialog.dark-theme :deep(.el-dialog) {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
}

.view-item-dialog.dark-theme .item-info {
  background: var(--el-fill-color-darker);
  border: 1px solid var(--el-border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .actions-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }

  .search-input {
    min-width: auto;
  }
}

/* Accessibility improvements */
.localStorage-table :deep(.el-table__cell) {
  padding: 8px 12px;
}

.localStorage-table :deep(.el-button) {
  margin: 0 2px;
}

/* Loading spinner styles */
.localStorage-panel-content.loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.localStorage-panel-content.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid var(--el-color-primary-light-7);
  border-top: 3px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
