<script setup>
import { Check, Close } from '@element-plus/icons-vue'
import { doc, setDoc } from 'firebase/firestore'
import { computed, inject, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import Analytics from '../../Analytics'
import { useAppStateStore } from '../../stores/appState'
import { barsRef, getUserBars, syncBarWithUserBars, usersRef } from '../firebase'
import { setToLocalStorage } from '../localStorage'
import { avaibleSitesUrlAndName } from '../sites_index'
import { MAX_FREE_PROMPT_BARS, TABS } from '../utils'

const props = defineProps(['isOpen', 'areBarsLoaded', 'userBars'])
const emit = defineEmits(['closeDialog', 'addBar'])
const user = useCurrentUser()
const appState = useAppStateStore()
const shouldShowSubscriptionButton = ref(false)

const isSubscriptionDialogOpen = inject('isSubscriptionDialogOpen', ref(false))
const { t } = useI18n()

function openSubscriptionDialog(type = '') {
  Analytics.fireEvent('subscription_dialog', { type })
  isSubscriptionDialogOpen.value = true
}
// console.log('user', user)
// console.log('props.currentURL', props.currentURL)
const close = (newPromptBarId = '') => {
  if (typeof newPromptBarId !== 'string') {
    newPromptBarId = ''
  }
  return emit('closeDialog', newPromptBarId)
}

const addPromptBar = async () => {
  // console.log('addPromptBar')
  if (promptBarName.value.length < 2) {
    promptBarNameError.value = 'Name must be at least 2 characters'
    return
  }

  const userId = user.value?.uid
  if (!userId) {
    promptBarNameError.value = 'You must be logged in to add a prompt bar'
    return
  }
  const userBars = (props.areBarsLoaded.value ? props.userBars : await getUserBars(userId)) ?? []
  if (userBars.length >= MAX_FREE_PROMPT_BARS && !appState.hasSubscription) {
    promptBarNameError.value = 'You must have a subscription to add more prompt bars'
    shouldShowSubscriptionButton.value = true
    return
  }
  const barNames = userBars.map((bar) => bar.name) ?? []

  if (barNames.includes(promptBarName.value)) {
    // console.log('promptBarName.value: ', promptBarName.value, appState.automaticNewBar, userBars)
    promptBarNameError.value = 'Name already exists'
    if (appState.automaticNewBar) {
      const matchingBar = userBars.find((bar) => bar.name === promptBarName.value)
      // console.log('matchingBar: ', matchingBar)
      if (matchingBar) {
        close(matchingBar.id)
      }
    }
    return
  }

  promptBarNameError.value = ''

  const createdAt = new Date()

  const barRef = doc(barsRef)
  // console.log(new Date().toUTCString() + ' Read-> barsRef: addPromptBar')
  const promptBarDoc = {
    name: promptBarName.value,
    description: '',
    destination: selectedAI.value || '',
    prompt_menu: '[]',
    links: '[]',
    isPublic: false,
    isPopular: false,
    createdAt,
    editedAt: createdAt,
    domain: location.host,
    owner: doc(usersRef, userId),
    tags: [],
    icon: '',
  }

  await setDoc(barRef, promptBarDoc)
  // console.log(new Date().toUTCString() + ' Write-> barsRef: addPromptBar')

  promptBarDoc.id = barRef.id

  Analytics.fireEvent('bar_add', {
    tab_name: TABS.MyPrompts,
    prompt_bar_id: promptBarDoc.id,
  })
  // console.log('promptBarDoc', promptBarDoc)

  await syncBarWithUserBars({ bar: promptBarDoc, userId })

  promptBarDoc.promptMenu = []
  promptBarDoc.linkMenu = []
  setToLocalStorage('My Prompts', promptBarDoc)

  // Emit the new bar data along with the selected URL
  emit('addBar', { barId: barRef.id, url: selectedAI.value })

  close(barRef.id)
}

const userName = user.value?.displayName
// console.log('userName', userName)
const promptBarNumber = props.areBarsLoaded.value ? props.userBars.length + 1 : null

const defaultPromptBarName = computed(() => {
  if (appState.currentChatNameForNewBar) {
    // console.log('Using currentChatName for defaultPromptBarName:', appState.currentChatNameForNewBar)
    return `${appState.currentChatNameForNewBar}`
  }
  const name = `Prompt Bar${promptBarNumber ? ' #' + promptBarNumber : ''}`
  // console.log('Using generated name for defaultPromptBarName:', name)
  return name
})

const promptBarName = ref('')
const promptBarNameError = ref('')

if (appState.automaticNewBar) {
  let automaticNewBarName = `Generated Prompts ${new Date().toLocaleDateString('pl-PL')}`
  if (userName) automaticNewBarName = userName + '`s ' + automaticNewBarName
  promptBarName.value = automaticNewBarName
  // addPromptBar()
}

const selectedAI = ref(appState.currentURLForNewBar || '')
const customChatLink = 'Custom'
const defaultChatLink = 'Default'
const isChatLinkOnList = (url) => {
  let findURL = avaibleSitesUrlAndName.find((item) => item.url === url)
  if (findURL && url !== '') return true
  return false
}
const checkAndSetChatLinkSelected = (url) => {
  if (!url) {
    return defaultChatLink
  }

  if (isChatLinkOnList(url)) {
    return url
  }

  return customChatLink
}
const selectedAIOption = ref(checkAndSetChatLinkSelected(selectedAI.value))
const onAISelectChange = (value) => {
  if (value === customChatLink) {
    selectedAI.value = currentUrl.value
  } else if (value === defaultChatLink) {
    selectedAI.value = ''
  } else {
    selectedAI.value = value
  }
}

const currentUrl = ref('')
const getCurrentUrl = () => {
  currentUrl.value = window.location.href
}
onMounted(getCurrentUrl)

const updateSelectedAI = () => {
  if (selectedAIOption.value === customChatLink) {
    selectedAI.value = currentUrl.value
  }
}

// Update the selectedAI and selectedAIOption when the dialog opens
watch(
  () => props.isOpen,
  (newValue) => {
    if (newValue) {
      selectedAI.value = appState.currentURLForNewBar || ''
      selectedAIOption.value = checkAndSetChatLinkSelected(selectedAI.value)
      promptBarName.value = defaultPromptBarName.value
    }
  },
)

// Watch for changes in currentChatNameForNewBar and update promptBarName accordingly
watch(
  () => appState.currentChatNameForNewBar,
  (newValue) => {
    if (newValue) {
      promptBarName.value = `${newValue}`
      // console.log('AddPromptBarDialog - promptBarName updated:', promptBarName.value)
    }
  },
)

watch(
  () => props.isOpen,
  (newValue) => {
    // console.log('AddPromptBarDialog - isOpen changed:', newValue)
    if (newValue) {
      // console.log('AddPromptBarDialog - Dialog opened, currentChatName:', appState.currentChatNameForNewBar)
      extractTitleAndName()
    }
  },
)

// Log all props when the component is mounted
onMounted(() => {
  // console.log('AddPromptBarDialog mounted - props:', props)
})

// Watch for changes in all props
Object.keys(props).forEach((propName) => {
  watch(
    () => props[propName],
    (newValue) => {
      // console.log(`AddPromptBarDialog - ${propName} changed:`, newValue)
    },
  )
})

const extractTitleAndName = () => {
  // console.log('extractTitleAndName function called')

  // Wybierz zaznaczony element
  const selectedElement = document.querySelector(
    '.group.relative.rounded-lg.active\\:opacity-90.bg-token-sidebar-surface-secondary',
  )
  // console.log('Selected element:', selectedElement)

  if (selectedElement) {
    const linkElement = selectedElement.querySelector('a')
    // console.log('Link element:', linkElement)

    if (linkElement) {
      const titleElement = linkElement.querySelector(
        '.relative.grow.overflow-hidden.whitespace-nowrap',
      )
      // console.log('Title element:', titleElement)

      if (titleElement) {
        const title = titleElement.textContent.trim()

        const name = linkElement.getAttribute('href')

        // console.log('Extracted Title:', title)
        // console.log('Extracted Name:', name)

        return { title, name }
      }
    }
  }

  // console.log('Nie znaleziono zaznaczonego elementu lub wymaganych informacji.')
  return null
}

watch(
  () => props.isOpen,
  (newValue) => {
    // console.log('AddPromptBarDialog - isOpen changed:', newValue)
    if (newValue) {
      // console.log('AddPromptBarDialog - Dialog opened, currentChatName:', appState.currentChatNameForNewBar)
      // console.log('Calling extractTitleAndName')
      const extractedInfo = extractTitleAndName()
      // console.log('Extracted info:', extractedInfo)
      if (extractedInfo && extractedInfo.title) {
        promptBarName.value = extractedInfo.title
        // console.log('Setting promptBarName to extracted title:', promptBarName.value)
      } else {
        promptBarName.value = defaultPromptBarName.value
        // console.log('Setting promptBarName to default:', promptBarName.value)
      }
    }
  },
  { immediate: true },
)
</script>

<template>
  <el-dialog
    :model-value="props.isOpen"
    :title="t('bar.createNew')"
    width="clamp(300px, 90vw, 600px)"
    @close="close"
    destroy-on-close
  >
    <div class="w-full">
      <el-input
        size="large"
        v-model="promptBarName"
        :placeholder="t('bar.newPromptBarName')"
        minlength="2"
        maxlength="60"
        data-testid="add-prompt-bar-dialog-input"
        show-word-limit
        clearable
        @keydown.enter="addPromptBar"
      />
    </div>

    <div class="w-full text-center">
      <div class="text-red-500 text-sm my-4">{{ promptBarNameError }}</div>
      <el-button
        v-if="shouldShowSubscriptionButton"
        type="info"
        plain
        @click="close(), openSubscriptionDialog('add_prompt_bar')"
        class="text-purple-400 focus:bg-purple-400 hover:!bg-purple-500 hover:!text-white"
        >{{ t('subscription.buySubscription') }}
      </el-button>
    </div>

    <div class="mb-2">
      <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('bar.destination') }}</label>
      <el-select
        v-model="selectedAIOption"
        :placeholder="t('bar.selectAI')"
        class="w-full"
        @change="onAISelectChange"
        data-testid="add-prompt-bar-destination-dialog-select"
      >
        <el-option
          :label="defaultChatLink"
          :value="defaultChatLink"
          data-testid="ai-option-destination"
        />
        <el-option
          :label="customChatLink"
          :value="customChatLink"
          data-testid="ai-option-destination"
        />
        <el-option
          v-for="ai in avaibleSitesUrlAndName"
          :key="ai.name"
          :label="ai.name"
          :value="ai.url"
          data-testid="ai-option-destination"
        />
      </el-select>
    </div>
    <div v-if="selectedAIOption === customChatLink" class="mb-2">
      <el-input v-model="selectedAI" class="w-full" :placeholder="t('prompt.enterCustomURL')" />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button data-testid="add-prompt-bar-dialog-cancel-button" @click="close" :icon="Close">{{
          t('btn.cancel')
        }}</el-button>
        <el-button
          @click="addPromptBar"
          :disabled="promptBarName.length < 2"
          type="primary"
          :icon="Check"
          data-testid="add-prompt-bar-dialog-confirmation-button"
          >{{ t('btn.add') }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped></style>
