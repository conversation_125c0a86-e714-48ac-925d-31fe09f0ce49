<template>
  <el-dialog
    v-model="isVisible"
    :title="t('sidePanelPopup.title')"
    width="clamp(320px, 90vw, 500px)"
    @close="handleClose"
    destroy-on-close
    class="side-panel-popup"
    :class="{ 'dark-theme': isDarkTheme }"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
  >
    <div class="popup-content">
      <!-- Welcome message -->
      <div class="welcome-section">
        <div class="welcome-icon">
          <el-icon size="48" color="#3b82f6">
            <InfoFilled />
          </el-icon>
        </div>
        <h3 class="welcome-title">{{ t('sidePanelPopup.welcomeTitle') }}</h3>
        <p class="welcome-description">
          {{ t('sidePanelPopup.welcomeDescription') }}
        </p>
      </div>

      <!-- Features list -->
      <div class="features-section">
        <h4 class="features-title">{{ t('sidePanelPopup.featuresTitle') }}</h4>
        <ul class="features-list">
          <li class="feature-item">
            <el-icon color="#10b981"><Check /></el-icon>
            <span>{{ t('sidePanelPopup.feature1') }}</span>
          </li>
          <li class="feature-item">
            <el-icon color="#10b981"><Check /></el-icon>
            <span>{{ t('sidePanelPopup.feature2') }}</span>
          </li>
          <li class="feature-item">
            <el-icon color="#10b981"><Check /></el-icon>
            <span>{{ t('sidePanelPopup.feature3') }}</span>
          </li>
          <li class="feature-item">
            <el-icon color="#10b981"><Check /></el-icon>
            <span>{{ t('sidePanelPopup.feature4') }}</span>
          </li>
        </ul>
      </div>

      <!-- Settings section -->
      <div class="settings-section">
        <h4 class="settings-title">{{ t('sidePanelPopup.settingsTitle') }}</h4>
        <p class="settings-description">{{ t('sidePanelPopup.settingsDescription') }}</p>
        <div class="settings-grid">
          <div class="setting-item">
            <label class="setting-label">{{ t('sidePanelPopup.themeLabel') }}</label>
            <ThemeSwitcher />
          </div>
          <div class="setting-item">
            <label class="setting-label">{{ t('sidePanelPopup.languageLabel') }}</label>
            <LanguageSelector />
          </div>
          <div class="setting-item">
            <label class="setting-label">{{ t('sidePanelPopup.profileLabel') }}</label>
            <ProfileSwitcher
              :selectedProfileId="selectedProfileId"
              :savedProfiles="savedProfiles"
              :isLoading="isLoading"
              @profile-changed="handleProfileChanged"
            />
          </div>
        </div>
      </div>

      <!-- Expansion tip -->
      <div class="expansion-tip">
        <p class="tip-text">{{ t('sidePanelPopup.expansionTip') }}</p>
      </div>

      <!-- Don't show again checkbox -->
      <div class="checkbox-section">
        <el-checkbox v-model="dontShowAgain" size="large">
          {{ t('sidePanelPopup.dontShowAgain') }}
        </el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleOk" size="large">
          {{ t('btn.ok') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { Check, InfoFilled } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { useProfileSync } from '../composables/useProfileSync'
import { useSidePanelTheme } from '../composables/useSidePanelTheme'
import LanguageSelector from './LanguageSelector.vue'
import ProfileSwitcher from './ProfileSwitcher.vue'
import ThemeSwitcher from './ThemeSwitcher.vue'

// Props
const props = defineProps({
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['close', 'profile-changed'])

// Composables
const { t } = useI18n()
const { setTheme, initializeThemeManagement, selectedTheme } = useSidePanelTheme()
const { selectedProfileId, savedProfiles, setProfile, initializeProfileSync } = useProfileSync()

// Local storage keys
const POPUP_STORAGE_KEY = 'sidePanel_popup_shown'
const THEME_STORAGE_KEY = 'sidePanel_selectedTheme'

// Global first-time user initialization for dark theme default
const initializeGlobalDefaults = () => {
  try {
    const hasSeenPopup = localStorage.getItem(POPUP_STORAGE_KEY)
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY)

    // Check if this is a completely new user (no popup shown, no theme saved)
    const isFirstTimeUser = hasSeenPopup !== 'true' && !savedTheme

    if (isFirstTimeUser) {
      // Set dark theme as default for new users
      localStorage.setItem(THEME_STORAGE_KEY, 'dark')

      // Immediately apply dark theme to DOM to prevent any light theme flash
      const htmlElement = document.documentElement
      htmlElement.classList.remove('light')
      htmlElement.classList.add('dark')
      htmlElement.setAttribute('data-theme', 'dark')

      console.log('🌙 First-time user detected: Dark theme set as default')
    }
  } catch (error) {
    console.warn('Failed to initialize global defaults:', error)
  }
}

// Execute global initialization IMMEDIATELY (before any theme system loads)
initializeGlobalDefaults()

// Initialize theme management system (will now load 'dark' for first-time users)
initializeThemeManagement()

// Reactive state
const isVisible = ref(false)
const dontShowAgain = ref(true)

// Computed properties
const shouldShowPopup = computed(() => {
  const hasBeenShown = localStorage.getItem(POPUP_STORAGE_KEY)
  return hasBeenShown !== 'true'
})

// Methods
const checkAndShowPopup = () => {
  if (shouldShowPopup.value) {
    // Verify theme state before showing popup
    verifyThemeState()
    isVisible.value = true
  }
}

// Verify and ensure correct theme state for first-time users
const verifyThemeState = () => {
  try {
    const hasSeenPopup = localStorage.getItem(POPUP_STORAGE_KEY)
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY)

    // For first-time users, ensure dark theme is active
    if (hasSeenPopup !== 'true' && savedTheme === 'dark') {
      // Double-check that theme is applied to DOM
      const htmlElement = document.documentElement
      if (!htmlElement.classList.contains('dark')) {
        htmlElement.classList.remove('light')
        htmlElement.classList.add('dark')
        htmlElement.setAttribute('data-theme', 'dark')
        console.log('🔧 Theme state corrected: Dark theme applied')
      }

      // Ensure theme composable is synchronized
      if (selectedTheme.value !== 'dark') {
        setTheme('dark')
        console.log('🔧 Theme composable synchronized: Dark theme set')
      }
    }
  } catch (error) {
    console.warn('Failed to verify theme state:', error)
  }
}

const handleOk = () => {
  // Only save to localStorage if "Don't show this again" checkbox is checked
  if (dontShowAgain.value) {
    localStorage.setItem(POPUP_STORAGE_KEY, 'true')
  }
  // If checkbox is unchecked, do not modify localStorage (allows popup to show again)

  isVisible.value = false
  emit('close')
}

const handleProfileChanged = (profileId) => {
  setProfile(profileId)
  emit('profile-changed', profileId)
}

// Debug function to log current state
const logCurrentState = () => {
  try {
    const hasSeenPopup = localStorage.getItem(POPUP_STORAGE_KEY)
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY)
    const htmlTheme = document.documentElement.getAttribute('data-theme')
    const htmlClasses = document.documentElement.className

    console.log('📊 Current State:', {
      hasSeenPopup,
      savedTheme,
      htmlTheme,
      htmlClasses,
      selectedTheme: selectedTheme.value,
      shouldShowPopup: shouldShowPopup.value,
    })
  } catch (error) {
    console.warn('Failed to log current state:', error)
  }
}

// Lifecycle
onMounted(() => {
  // Initialize profile synchronization
  initializeProfileSync()

  // Log current state for debugging
  logCurrentState()

  // Small delay to ensure theme is fully applied before showing popup
  setTimeout(() => {
    checkAndShowPopup()
  }, 50) // Minimal delay for theme synchronization
})

// Watch for theme changes
watch(
  () => props.isDarkTheme,
  (newValue) => {
    // Theme change handling if needed
  },
)

// Expose methods for parent component (if needed for future functionality)
defineExpose({
  isVisible: computed(() => isVisible.value),
})
</script>

<style scoped>
.side-panel-popup :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.side-panel-popup.dark-theme :deep(.el-dialog) {
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
}

.popup-content {
  @apply space-y-6;
}

.welcome-section {
  @apply text-center space-y-4;
}

.welcome-icon {
  @apply flex justify-center;
}

.welcome-title {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100 m-0;
}

.welcome-description {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed m-0;
}

.features-section {
  @apply space-y-3;
}

.features-title {
  @apply text-lg font-medium text-gray-900 dark:text-gray-100 m-0 mb-3;
}

.features-list {
  @apply space-y-2 list-none p-0 m-0;
}

.feature-item {
  @apply flex items-center space-x-3 text-gray-700 dark:text-gray-300;
}

.settings-section {
  @apply space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700;
}

.settings-title {
  @apply text-lg font-medium text-gray-900 dark:text-gray-100 m-0 mb-3;
}

.settings-description {
  @apply text-sm text-gray-600 dark:text-gray-400 leading-relaxed m-0 mb-4;
}

.settings-grid {
  @apply space-y-4;
}

/* Ensure consistent spacing between setting items */
.settings-grid .setting-item:not(:last-child) {
  margin-bottom: 16px;
}

.setting-item {
  @apply flex items-center justify-between gap-3;
}

.setting-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 min-w-0 flex-shrink-0;
  width: 80px;
}

/* Match ComponentControlBar styling with fixed width for visual consistency */
.setting-item :deep(.el-select),
.setting-item :deep(.el-dropdown),
.setting-item :deep(.profile-switcher) {
  width: 120px;
  min-width: 120px;
}

/* ThemeSwitcher - match original styling with fixed width */
.setting-item :deep(.theme-select .el-input__wrapper) {
  padding: 0 8px;
  width: 100%;
  box-sizing: border-box;
}

.setting-item :deep(.theme-select .el-select__caret) {
  margin-left: 4px;
  flex-shrink: 0;
}

.setting-item :deep(.theme-option) {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.setting-item :deep(.theme-icon) {
  font-size: 14px;
  flex-shrink: 0;
}

.setting-item :deep(.theme-label) {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-item :deep(.theme-select .el-input__inner) {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

/* LanguageSelector - match original styling with fixed width */
.setting-item :deep(.language-select .el-input__wrapper) {
  padding: 0 8px;
  width: 100%;
  box-sizing: border-box;
}

.setting-item :deep(.language-select .el-select__caret) {
  margin-left: 4px;
  flex-shrink: 0;
}

.setting-item :deep(.language-select .el-input__inner) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ProfileSwitcher - use default Element Plus styling (no custom overrides) */
/* This ensures it matches the ComponentControlBar exactly */

/* ProfileSwitcher uses default Element Plus styling with fixed width */
.setting-item :deep(.profile-switcher .el-dropdown) {
  width: 100%;
}

.setting-item :deep(.profile-switcher .el-button) {
  width: 100%;
  justify-content: flex-start;
}

.setting-item :deep(.profile-switcher .profile-label) {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.setting-item :deep(.profile-switcher .dropdown-arrow) {
  margin-left: auto;
  flex-shrink: 0;
}

.expansion-tip {
  @apply pt-4 border-t border-gray-200 dark:border-gray-700;
}

.tip-text {
  @apply text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg m-0 leading-relaxed;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.checkbox-section {
  @apply pt-4;
}

.dialog-footer {
  @apply flex gap-3 justify-center;
}

/* Responsive adjustments for side panel */
@media (max-width: 450px) {
  .side-panel-popup :deep(.el-dialog) {
    width: clamp(300px, 95vw, 400px) !important;
    margin: 5vh auto;
  }

  .welcome-title {
    @apply text-lg;
  }

  .features-title {
    @apply text-base;
  }

  .settings-title {
    @apply text-base;
  }

  .settings-description {
    @apply text-xs;
  }

  .setting-item {
    @apply flex-col items-start gap-2;
  }

  .setting-label {
    @apply text-xs;
    width: auto !important;
    min-width: auto !important;
  }

  .tip-text {
    @apply text-xs p-2;
  }

  /* Maintain consistent sizing on mobile - expand to full width */
  .setting-item :deep(.el-select),
  .setting-item :deep(.el-dropdown),
  .setting-item :deep(.profile-switcher) {
    width: 100% !important;
    min-width: 100% !important;
  }

  .setting-item :deep(.theme-select .el-input__wrapper),
  .setting-item :deep(.language-select .el-input__wrapper),
  .setting-item :deep(.profile-switcher .el-button) {
    width: 100% !important;
  }

  .dialog-footer {
    @apply flex-col gap-2;
  }

  .dialog-footer .el-button {
    @apply w-full;
  }
}

/* Dropdown consistency */
:deep(.theme-select-dropdown),
:deep(.language-select-dropdown),
:deep(.profile-dropdown-menu) {
  z-index: 3000 !important;
  border-radius: 6px !important;
  border: 1px solid var(--el-border-color) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

:deep(.theme-select-dropdown .el-select-dropdown__item),
:deep(.language-select-dropdown .el-select-dropdown__item),
:deep(.profile-dropdown-menu .el-dropdown-menu__item) {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Animation for smooth appearance */
.side-panel-popup :deep(.el-dialog__body) {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
