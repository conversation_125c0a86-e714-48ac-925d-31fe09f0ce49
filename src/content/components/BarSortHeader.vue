<script setup>
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useAppStateStore } from '../../stores/appState'
import { useBarOptions } from '../composables/useBarOptions'
import { TABS } from '../utils'
import BarSortButton from './BarSortButton.vue'
import LanguageSelectorPromptFilter from './LanguageSelectorPromptFilter.vue'

const { t } = useI18n()

const appState = useAppStateStore()
const { activeTab } = storeToRefs(appState)
const { getBarsForCurrentTab } = useBarOptions()
const barsForCurrentTab = computed(() => getBarsForCurrentTab())
</script>

<template>
  <div class="flex items-center justify-between w-full">
    <div v-if="barsForCurrentTab.length > 0" class="flex flex-wrap items-center">
      <span class="text-sm text-neutral-400 whitespace-nowrap mr-2">{{ t('bar.sortBy') }}:</span>

      <el-button-group size="small" class="flex flex-nowrap">
        <BarSortButton fieldName="name" />
        <BarSortButton fieldName="createdAt" fieldLabel="date" />
        <BarSortButton fieldName="destination" />
      </el-button-group>
    </div>

    <div
      v-if="activeTab === TABS.Popular || activeTab === TABS.Library"
      class="ml-auto min-w-[80px] w-auto"
    >
      <LanguageSelectorPromptFilter />
    </div>
  </div>
</template>
