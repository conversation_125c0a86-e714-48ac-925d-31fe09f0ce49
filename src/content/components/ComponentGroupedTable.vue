<template>
  <div class="component-grouped-container">
    <div v-if="groupedData.length === 0" class="empty-state">
      <el-empty :description="t('localStoragePanel.empty.description')" class="empty-state">
        <template #image>
          <el-icon :size="64" color="var(--el-color-info)">
            <FolderOpened />
          </el-icon>
        </template>
      </el-empty>
    </div>

    <div v-else class="component-groups">
      <div v-for="group in groupedData" :key="group.componentName" class="component-group">
        <div class="component-header">
          <h3 class="component-name">
            <el-icon class="component-icon"><Grid /></el-icon>
            {{ group.componentName }}
            <el-tag size="small" type="info" class="item-count">
              {{ group.items.length }} {{ group.items.length === 1 ? 'item' : 'items' }}
            </el-tag>
          </h3>
        </div>

        <div class="component-table">
          <el-table
            :data="group.items"
            stripe
            size="small"
            class="localStorage-table"
            :class="{ 'dark-theme': isDarkTheme }"
            empty-text=""
          >
            <el-table-column
              prop="key"
              :label="t('localStoragePanel.table.key')"
              min-width="150"
              show-overflow-tooltip
            />

            <el-table-column
              :label="t('localStoragePanel.table.description')"
              min-width="180"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ getKeyDescription(row.key) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="preview"
              :label="t('localStoragePanel.table.value')"
              min-width="160"
              show-overflow-tooltip
            />

            <el-table-column prop="type" :label="t('localStoragePanel.table.type')" width="80">
              <template #default="{ row }">
                <el-tag size="small" :type="getTypeTagType(row.type)">
                  {{ t(`localStoragePanel.types.${row.type}`) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="size" :label="t('localStoragePanel.table.size')" width="60">
              <template #default="{ row }">
                {{ formatSize(row.size) }}
              </template>
            </el-table-column>

            <el-table-column
              :label="t('localStoragePanel.table.actions')"
              width="100"
              fixed="right"
            >
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  text
                  @click="$emit('view-item', row)"
                  :icon="View"
                >
                  {{ t('localStoragePanel.actions.view') }}
                </el-button>

                <el-popconfirm
                  :title="t('localStoragePanel.confirmations.deleteItem')"
                  @confirm="$emit('delete-item', row.key)"
                  :width="250"
                >
                  <template #reference>
                    <el-button type="danger" size="small" text :icon="Delete">
                      {{ t('localStoragePanel.actions.delete') }}
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Delete, FolderOpened, Grid, View } from '@element-plus/icons-vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  isDarkTheme: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['view-item', 'delete-item'])

const { t } = useI18n()

// Group data by component names
const groupedData = computed(() => {
  const groups = new Map()

  props.data.forEach((item) => {
    const componentName = extractComponentName(item.key)

    if (!groups.has(componentName)) {
      groups.set(componentName, {
        componentName,
        items: [],
      })
    }

    groups.get(componentName).items.push(item)
  })

  // Sort groups by component name and sort items within each group
  return Array.from(groups.values())
    .sort((a, b) => a.componentName.localeCompare(b.componentName))
    .map((group) => ({
      ...group,
      items: group.items.sort((a, b) => a.key.localeCompare(b.key)),
    }))
})

// Extract component name from localStorage key
const extractComponentName = (key) => {
  // Check for component_state_ pattern
  if (key.startsWith('component_state_')) {
    const componentId = key.replace('component_state_', '').split('_')[0]
    return formatComponentName(componentId)
  }

  // Check for menuControlState_ pattern
  if (key.startsWith('menuControlState_')) {
    return 'Menu Control System'
  }

  // Check for aiPromptManager_ pattern
  if (key.startsWith('aiPromptManager_')) {
    return 'AI Prompt Manager'
  }

  // Check for ai-tools- pattern
  if (key.startsWith('ai-tools-')) {
    return 'AI Tools Collection'
  }

  // Check for localStorage-panel- pattern
  if (key.startsWith('localStorage-panel-')) {
    return 'LocalStorage Panel'
  }

  // Check for common component prefixes
  const componentPrefixes = [
    { prefix: 'SitesGrid_', name: 'Sites Grid' },
    { prefix: 'AITools_', name: 'AI Tools' },
    { prefix: 'ComponentControlBar_', name: 'Component Control Bar' },
    { prefix: 'LocalStorage_', name: 'LocalStorage Manager' },
    { prefix: 'ThemeManager_', name: 'Theme Manager' },
    { prefix: 'LanguageSelector_', name: 'Language Selector' },
    { prefix: 'SearchBar_', name: 'Search Bar' },
    { prefix: 'TabManager_', name: 'Tab Manager' },
    { prefix: 'UserProfile_', name: 'User Profile' },
    { prefix: 'Settings_', name: 'Settings' },
  ]

  for (const { prefix, name } of componentPrefixes) {
    if (key.startsWith(prefix)) {
      return name
    }
  }

  // Check for version keys
  if (key.includes('_version') || key.endsWith('_version')) {
    const baseName = key.replace('_version', '').replace(/Version$/, '')
    if (baseName.startsWith('component_state_')) {
      const componentId = baseName.replace('component_state_', '')
      return formatComponentName(componentId) + ' (Version)'
    }
    return formatComponentName(baseName) + ' (Version)'
  }

  // Check for tab-related keys
  const tabKeys = ['All', 'Popular', 'Public', 'My Prompts', 'Library', 'Favorites']
  if (tabKeys.includes(key)) {
    return 'Application Tabs'
  }

  // Check for core app keys
  const coreKeys = [
    'activeTab',
    'lang',
    'barLangPopular',
    'barLangLibrary',
    'barSortFieldName',
    'barSortDirection',
  ]
  if (coreKeys.includes(key)) {
    return 'Core Application'
  }

  // Check for sorting and filtering keys
  if (key.toLowerCase().includes('sort') || key.toLowerCase().includes('filter')) {
    return 'Sorting & Filtering'
  }

  // Check for theme-related keys
  if (
    key.toLowerCase().includes('theme') ||
    key.toLowerCase().includes('dark') ||
    key.toLowerCase().includes('light')
  ) {
    return 'Theme System'
  }

  // Check for language/locale keys
  if (
    key.toLowerCase().includes('lang') ||
    key.toLowerCase().includes('locale') ||
    key.toLowerCase().includes('i18n')
  ) {
    return 'Internationalization'
  }

  // Default fallback
  return 'Miscellaneous'
}

// Format component name for display
const formatComponentName = (name) => {
  return name
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
    .trim()
}

// Description function (reused from LocalStorageTable)
const getKeyDescription = (key) => {
  try {
    const exactMatch = t(`localStoragePanel.descriptions.${key}`)
    if (exactMatch && exactMatch !== `localStoragePanel.descriptions.${key}`) {
      return exactMatch
    }
  } catch (e) {
    // Key doesn't exist, continue to pattern matching
  }

  if (key.startsWith('component_state_')) {
    return t('localStoragePanel.descriptions.component_state')
  }

  if (key.startsWith('menuControlState_')) {
    return t('localStoragePanel.descriptions.menuControlState')
  }

  if (key.includes('_version') || key.endsWith('_version')) {
    return t('localStoragePanel.descriptions.version')
  }

  if (key.toLowerCase().includes('theme')) {
    return t('localStoragePanel.descriptions.theme')
  }

  if (key.toLowerCase().includes('view')) {
    return t('localStoragePanel.descriptions.view')
  }

  if (key.toLowerCase().includes('sort')) {
    return t('localStoragePanel.descriptions.sort')
  }

  if (key.toLowerCase().includes('lang') || key.toLowerCase().includes('locale')) {
    return t('localStoragePanel.descriptions.locale')
  }

  return t('localStoragePanel.descriptions.default')
}

const getTypeTagType = (type) => {
  const typeMap = {
    string: '',
    object: 'success',
    array: 'warning',
    number: 'info',
    boolean: 'danger',
  }
  return typeMap[type] || ''
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.component-grouped-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  overflow: hidden;
}

.component-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.component-group {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  overflow: hidden;
  background: var(--el-bg-color);
}

.component-header {
  background: linear-gradient(
    135deg,
    var(--el-fill-color-light) 0%,
    var(--el-fill-color-lighter) 100%
  );
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background 0.3s ease;
}

.component-header:hover {
  background: linear-gradient(135deg, var(--el-fill-color) 0%, var(--el-fill-color-light) 100%);
}

.component-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.component-icon {
  color: var(--el-color-primary);
  font-size: 18px;
}

.item-count {
  margin-left: auto;
  font-weight: 500;
}

.component-table {
  background: var(--el-bg-color);
}

.localStorage-table {
  width: 100%;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

/* Dark theme support */
.component-grouped-container.dark-theme .component-header {
  background: linear-gradient(
    135deg,
    var(--el-fill-color-darker) 0%,
    var(--el-fill-color-dark) 100%
  );
}

.component-grouped-container.dark-theme .component-header:hover {
  background: linear-gradient(
    135deg,
    var(--el-fill-color-dark) 0%,
    var(--el-fill-color-darker) 100%
  );
}

.component-grouped-container.dark-theme .component-group {
  background: var(--el-bg-color-page);
  border-color: var(--el-border-color);
}

.component-grouped-container.dark-theme .component-name {
  color: var(--el-text-color-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .component-groups {
    padding: 8px;
    gap: 12px;
  }

  .component-header {
    padding: 8px 12px;
  }

  .component-name {
    font-size: 14px;
    flex-wrap: wrap;
  }

  .localStorage-table :deep(.el-table__cell) {
    padding: 6px 4px;
    font-size: 12px;
  }
}
</style>
