<script setup>
import { ElMessage } from 'element-plus'
import { deleteField, doc, getDoc, runTransaction, setDoc, updateDoc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { computed, onMounted } from 'vue'

import { useAppStateStore } from '../../stores/appState'
import { getBarById } from '../composables/usePromptBarManagement'
import { db, updateBarLanguage } from '../firebase'

const message = (setContent, setType = 'info') => {
  ElMessage.closeAll()
  ElMessage({
    message: setContent,
    type: setType,
  })
}
const appState = useAppStateStore()
const { promptBarLanguage, selectedBarId, supportedLanguages } = storeToRefs(appState)

const selectedLanguage = computed({
  get: () => promptBarLanguage.value,
  set: (val) => handleLocaleChange(val),
})

const getDocumentName = (baseDocName, langCode) => {
  return langCode === 'en' ? baseDocName : `${baseDocName}_${langCode}`
}

const handleLocaleChange = async (value) => {
  if (value === promptBarLanguage.value) {
    return
  }

  const deleteFromPopular = getDocumentName('popular', promptBarLanguage.value)
  const deleteFromLibrary = getDocumentName('library', promptBarLanguage.value)

  const addPromptBarToPopular = getDocumentName('popular', value)
  const addPromptBarToLibrary = getDocumentName('library', value)

  const popularDocToCopyRef = doc(db, 'bar_info', deleteFromPopular)
  const libraryDocToCopyRef = doc(db, 'bar_info', deleteFromLibrary)

  const moveBarBetweenDocuments = async (sourceDocRef, targetCollection, targetDocName) => {
    const newDocRef = doc(db, targetCollection, targetDocName)
    await runTransaction(db, async (transaction) => {
      const snap = await transaction.get(sourceDocRef)
      if (!snap.exists()) return
      const dataBarToMove = snap.data()[selectedBarId.value]
      if (!dataBarToMove) return
      // Set in target
      transaction.set(newDocRef, { [selectedBarId.value]: dataBarToMove }, { merge: true })
      // Delete from source
      transaction.update(sourceDocRef, { [selectedBarId.value]: deleteField() })
    })
  }

  try {
    await Promise.all([
      moveBarBetweenDocuments(popularDocToCopyRef, 'bar_info', addPromptBarToPopular),
      moveBarBetweenDocuments(libraryDocToCopyRef, 'bar_info', addPromptBarToLibrary),
    ])
    message('Bar language updated', 'success')
  } catch (error) {
    console.error('Error updating bar language:', error)
  }

  promptBarLanguage.value = value

  if (selectedBarId.value) {
    try {
      await updateBarLanguage(selectedBarId.value, value)
    } catch (error) {
      console.error('Error updating bar language in database:', error)
    }
  }
}

onMounted(async () => {
  if (selectedBarId.value) {
    const bar = await getBarById(selectedBarId.value)

    if (bar.lang) {
      promptBarLanguage.value = bar.lang
    } else {
      // If language is not found, set default to 'en' and update in database
      promptBarLanguage.value = 'en'
      // await updateBarLanguage(selectedBarId.value, 'en')
    }
  }
})
</script>

<template>
  <el-select
    v-model="selectedLanguage"
    class="language-select"
    size="small"
    style="max-width: 150px"
    popper-class="language-select-dropdown"
    data-testid="language-select-dropdown"
  >
    <el-option
      v-for="lang in supportedLanguages"
      :key="lang.value"
      :label="lang.label"
      :value="lang.value"
      data-testid="language-select-option-in-edit-mode"
    />
  </el-select>
</template>

<style scoped>
.language-select {
  flex-shrink: 0;
}
.language-select :deep(.el-input__wrapper) {
  padding: 0 8px;
}
.language-select :deep(.el-select__caret) {
  margin-left: 4px;
}
</style>
