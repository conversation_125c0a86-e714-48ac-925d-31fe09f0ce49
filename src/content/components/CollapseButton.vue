<template>
  <button
    @click="$emit('click')"
    @mousedown="handleMouseDown"
    @touchstart="handleTouchStart"
    class="collapse-button"
    :class="{ 'drag-handle': isCollapsed, 'dark-theme': isDarkTheme }"
    :title="
      isCollapsed
        ? t('menuHideControl.collapseButton.expand')
        : t('menuHideControl.collapseButton.hide')
    "
  >
    <iconLogo
      :width="isCollapsed ? '46px' : '14px'"
      :height="isCollapsed ? '46px' : '14px'"
      class="logo-icon"
      :class="{ 'collapsed-icon': isCollapsed }"
    />
  </button>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

import iconLogo from './icons/iconLogo.vue'

const { t } = useI18n()

const props = defineProps({
  isCollapsed: Boolean,
  isDarkTheme: Boolean,
})

const emit = defineEmits(['click', 'startDrag'])

const handleMouseDown = (event) => {
  emit('startDrag', event)
}

const handleTouchStart = (event) => {
  emit('startDrag', event)
}
</script>

<style scoped>
.collapse-button {
  width: 36px;
  height: 24px;
  background: rgba(107, 114, 128, 0.8);
  border: 1px solid #9ca3af;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  margin-top: 4px;
  position: relative;
  overflow: hidden;
}

.collapse-button.drag-handle {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  cursor: grab;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-button.drag-handle:hover {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  cursor: grab;
  transform: scale(1.15) rotate(5deg);
  animation: dragPulse 2s infinite;
}

.collapse-button.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.collapse-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.collapse-button:hover {
  background: rgba(107, 114, 128, 1);
  border-color: #6b7280;
  transform: scale(1.1);
  box-shadow:
    0 3px 12px rgba(107, 114, 128, 0.4),
    0 0 15px rgba(107, 114, 128, 0.2);
}

.collapse-button:hover::before {
  left: 100%;
}

.collapse-button.drag-handle::before {
  background: linear-gradient(90deg, transparent, rgba(163, 163, 163, 0.4), transparent);
}

.logo-icon {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 0.9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.logo-icon.collapsed-icon {
  width: 46px !important;
  height: 46px !important;
  min-width: 46px !important;
  min-height: 46px !important;
  max-width: 46px !important;
  max-height: 46px !important;
  transform: translateY(5px);
}

.collapse-button:hover .logo-icon {
  opacity: 1;
  transform: scale(1.1) rotate(5deg);
}

.collapse-button.drag-handle:hover .logo-icon {
  animation: logoGlow 1.5s infinite;
}

@keyframes dragPulse {
  0%,
  100% {
    box-shadow:
      0 4px 20px rgba(107, 114, 128, 0.5),
      0 0 25px rgba(107, 114, 128, 0.3);
  }
  50% {
    box-shadow:
      0 6px 25px rgba(107, 114, 128, 0.7),
      0 0 30px rgba(107, 114, 128, 0.4);
  }
}

@keyframes logoGlow {
  0%,
  100% {
    opacity: 1;
    filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.4));
  }
  50% {
    opacity: 0.8;
    filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.7));
  }
}

.collapse-button.dark-theme {
  background: rgba(64, 64, 64, 0.8);
  border-color: rgba(115, 115, 115, 0.6);
}

.collapse-button.dark-theme:hover {
  background: rgba(75, 75, 75, 1);
  border-color: #9ca3af;
}
</style>
