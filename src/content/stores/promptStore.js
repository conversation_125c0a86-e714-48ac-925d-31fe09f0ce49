import { defineStore } from 'pinia'

import { removeFromPluginStorageLocal, setToPluginStorageLocal } from '../localStorage'

export const usePromptStore = defineStore('prompt', {
  state: () => ({
    isPromptPopupOpen: false,
    currentPrompt: null,
    currentPromptName: '',
    currentDescription: '',
    currentPromptDestination: '',
    sharedPromptId: null,
  }),

  actions: {
    setSharedPromptId(id) {
      this.sharedPromptId = id
      if (id) {
        setToPluginStorageLocal('sharedPrompt', id)
      }
    },

    openPromptPopup(prompt, destination = '') {
      if (this.isPromptPopupOpen) return

      // Set data first
      this.currentPrompt = prompt
      this.currentPromptName = prompt.label
      this.currentDescription = prompt.description || ''
      this.currentPromptDestination = prompt.destination || destination

      // Show dialog
      this.isPromptPopupOpen = true
    },

    closePromptPopup() {
      // Hide dialog first
      this.isPromptPopupOpen = false

      // Clean up data
      this.currentPrompt = null
      this.currentPromptName = ''
      this.currentDescription = ''
      this.currentPromptDestination = ''

      // Clean up shared prompt if exists
      if (this.sharedPromptId) {
        removeFromPluginStorageLocal('sharedPrompt')
        this.sharedPromptId = null
      }
    },
  },
})
