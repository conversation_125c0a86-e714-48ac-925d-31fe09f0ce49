export const getFromLocalStorage = (key) => {
  const value = localStorage.getItem(key)
  return value ? JSON.parse(value) : ''
}

function convertArray(value, withOwner) {
  // add id to value
  value = {
    ...value,
    id: value.id,
  }

  // add id to owner
  const ownerId = value.owner?.id || value.owner?.split('/').pop()
  if (ownerId) {
    if (withOwner) {
      // Keep full owner data
      let owner = {
        ...(value.owner?.id ? value.owner : {}),
        id: ownerId,
      }
      value.owner = owner
    } else {
      // Keep only essential owner data (id and name) for display purposes
      let owner = {
        id: ownerId,
      }
      // Preserve name if it exists
      if (value.owner?.name) {
        owner.name = value.owner.name
      }
      value.owner = owner
    }
  } else {
    delete value.owner
  }

  // remove unwanted keys
  let unwantedKeys = ['links', 'prompt_menu', 'domain']
  unwantedKeys.forEach((e) => delete value[e])

  return value
}
export const setToLocalStorage = (key, value) => {
  //   console.log('setToLocalStorage: ', key, value, value.id, value.owner.id)
  if (typeof value === 'object' && value !== null && value.id) {
    value = convertArray(value, true)
  }
  localStorage.setItem(key, JSON.stringify(value))
}

export const setToLocalStorageSubscriptionCheck = (key, value, subscription) => {
  // console.log('setToLocalStorageSubscriptionCheck: ', key, value, subscription)
  if (value.id) {
    value = convertArray(value, false)
  }
  if (!subscription) {
    let prompts = value.promptMenu
    // check if key premium is true and do it recursively if prompt have key children, if premium is true delete key prompt
    function deletePremiumPrompts(prompts) {
      for (let i = 0; i < prompts.length; i++) {
        if (prompts[i].premium) {
          delete prompts[i].prompt
        }
        if (prompts[i].children && prompts[i].children.length > 0) {
          deletePremiumPrompts(prompts[i].children)
        }
      }
    }
    deletePremiumPrompts(prompts)

    value.promptMenu = prompts
  }
  localStorage.setItem(key, JSON.stringify(value))
}

export const updateMyPromptsBarKeyInLocalStorage = (key, value) => {
  // console.log('updateMyPromptsBarKeyInLocalStorage: ', key, value)
  if (!getFromLocalStorage('My Prompts')) return
  let bar = getFromLocalStorage('My Prompts')
  bar[key] = value
  setToLocalStorage('My Prompts', bar)
}

export const removeFromLocalStorage = (key) => {
  localStorage.removeItem(key)
}

export const saveAffiliateParams = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const clientReferenceId = urlParams.get('cid')
  const linkId = urlParams.get('linkId')
  const sourceId = urlParams.get('sourceId')
  const tenantId = urlParams.get('tenantId')

  const affiliateParams = getFromLocalStorage('affiliateParams') || {}
  if (clientReferenceId) {
    affiliateParams.clientReferenceId = clientReferenceId
  }
  if (linkId) {
    affiliateParams.linkId = linkId
  }
  if (sourceId) {
    affiliateParams.sourceId = sourceId
  }
  if (tenantId) {
    affiliateParams.tenantId = tenantId
  }

  if (Object.keys(affiliateParams).length > 0) {
    setToLocalStorage('affiliateParams', affiliateParams)
  }
}

export const setToPluginStorageLocal = (key, value, shouldMerge = false) => {
  // console.log('setToPluginStorageLocal: ', key, value, shouldMerge)
  if (shouldMerge) {
    chrome.storage.local.get([key], (result) => {
      const mergedObject = { ...result[key], ...value }
      chrome.storage.local.set({ [key]: mergedObject }, function () {
        // console.log('setToPluginStorageLocal with merge: ', key, value)
      })
    })
  } else {
    chrome.storage.local.set({ [key]: value }, function () {
      // console.log('setToPluginStorageLocal: ', key, value)
    })
  }
}

export const getFromPluginStorageLocal = (key) => {
  return new Promise((resolve, reject) => {
    chrome.storage.local.get([key], (result) => {
      if (result[key]) {
        // console.log('getPluginStorge: ', key, result, result[key]);
        resolve(result[key])
      } else {
        // console.log('getPluginStorge: error: ', key, result);
        reject(`Error: Key: ${key} not found`)
      }
    })
  })
}

// DetectChangesInStorage
export const onChangeInPluginStorageLocal = (callback) => {
  chrome.storage.onChanged.addListener((changes, namespace) => {
    for (let [key, { oldValue, newValue }] of Object.entries(changes)) {
      if (namespace === 'local') {
        callback(key, oldValue, newValue)
      }
    }
  })
}

export const removeFromPluginStorageLocal = (key) => {
  chrome.storage.local.remove(key, function () {
    // console.log('removePluginStorage: ', key)
  })
}
