@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  .prompt-manager button:not(:hover),
  .prompt-manager [type='button']:not(:hover) {
    background-color: var(--el-button-bg-color, var(--el-color-transparent));
  }
}
@layer components {
  *,
  ::before,
  ::after {
    box-sizing: initial;
  }
  .chat-prompt-textarea {
    /* margin-top: 1em; */
    /* margin-top: 1em; */
  }
  .chat-prompt-button {
    /* transform: translateX(-1em); */
  }
  #_chatButton {
    margin-bottom: 1em;
  }
}
