<script setup>
import { Star, StarFilled } from '@element-plus/icons-vue'
import { signOut } from 'firebase/auth'
import { doc } from 'firebase/firestore'
import { isEqual } from 'lodash'
import { storeToRefs } from 'pinia'
import { nextTick, onBeforeUnmount, onMounted, provide, ref, watch, watchEffect } from 'vue'
import { useCurrentUser, useDocument, useFirebaseAuth } from 'vuefire'

import Analytics from '../Analytics.js'
import { useAppStateStore } from '../stores/appState'
import AddPromptBarDialog from './components/AddPromptBarDialog.vue'
//import ChatScraping from './components/ChatScraping.vue'
import EditPromptBarDialog from './components/EditPromptBarDialog.vue'
import InformationDialog from './components/InformationDialog.vue'
import MenuTabs from './components/MenuTabs.vue'
import SharePromptBarDialog from './components/SharePromptBarDialog.vue'
import SmallMenu from './components/SmallMenu.vue'
import { setPhotopeaLinks } from './composables/setPhotopeaLinks'
import { useExternalTextarea } from './composables/useExternalTextarea'
import { useFavorites } from './composables/useFavorites'
import { useMenuHideLevel } from './composables/useMenuHideLevel'
import { getBarById, usePromptBarManagement } from './composables/usePromptBarManagement'
import { useStorageManagement } from './composables/useStorageManagement'
import { barsRef, createOrEditChat, getUserBars, ROLES, usersRef } from './firebase'
import {
  getFromPluginStorageLocal,
  onChangeInPluginStorageLocal,
  removeFromLocalStorage,
  saveAffiliateParams,
  setToLocalStorage,
  setToLocalStorageSubscriptionCheck,
  setToPluginStorageLocal,
} from './localStorage'
import { chatSite } from './sites_index.js'
import { usePromptStore } from './stores/promptStore'
import { convertIdToString, TABS } from './utils.js'

// import './collectionMigrationHelpers.js'

// Define props with proper validation
const props = defineProps({
  showSmallMenu: {
    type: Boolean,
    default: true,
    validator: (value) => typeof value === 'boolean',
  },
})

const promptStore = usePromptStore()
const appState = useAppStateStore()
const { isFavorite, toggleFavorite } = useFavorites()
const {
  activeTab,
  sharePrompt,
  redirectedPrompt,
  promo,
  selectedBarId,
  isSubscriptionDialogOpen,
  isPromptBookmark,
  areMyPromptBarsLoaded,
  sharedBarId,
  popularBars,
  publicBars,
  myPromptBars,
  libraryBars,
  teamBars,
  isTabEmpty,
  userDoc,
  selectRef,
  hasSubscription,
  isLoggingIn,
  isSigningUp,
  isAdmin,
  outsideTextareaPrompt: outsideTextareaPromptRef,
  outsideTextarea: outsideTextareaRef,
} = storeToRefs(appState)
let { selectedBar } = storeToRefs(appState)
const { fetchDataFromPluginStorage } = useStorageManagement()

console.info('AI Prompt Lab initialized for:', chatSite.name)
saveAffiliateParams()
provide('sharePrompt', sharePrompt) // Todo: change to get from strore and remove provide
provide('promo', promo) // Todo: change to get from strore and remove provide
//Get shared bar id from local storage, set in appState, if exists, get shared prompt id from local storage, set in promptStore
//Todo: separate to another file
const initializeSharedData = async () => {
  const sharedBarIdValue = (await fetchDataFromPluginStorage('sharedBar')) ?? ''
  appState.setSharedBarId(sharedBarIdValue)
  if (sharedBarIdValue) {
    const sharedPrompt = (await fetchDataFromPluginStorage('sharedPrompt')) ?? ''
    if (sharedPrompt) {
      await nextTick()
      promptStore.setSharedPromptId(sharedPrompt)
    }
  }
}
provide('redirectedPrompt', redirectedPrompt)

//Get redirected prompt id from local storage, set in appState
const initializeRedirectedPrompt = async () => {
  const redirectedPromptValue = (await fetchDataFromPluginStorage('redirectedPrompt')) ?? ''

  appState.setRedirectedPrompt(redirectedPromptValue)
}
initializeRedirectedPrompt()

const user = useCurrentUser()
const auth = useFirebaseAuth()

const showApp = ref(false)
const { menuHideLevel, changeMenuHideLevel } = useMenuHideLevel()

function formatFirebaseDate(date, shouldShowTime = true) {
  if (!date?.seconds) return ''
  return new Date(date.seconds * 1000 + date.nanoseconds / 1000000)
    .toISOString()
    .substring(0, shouldShowTime ? 16 : 10)
    .replace('T', ' ')
}

const { outsideButton, getOutsideSelectorFromChatSite } = useExternalTextarea()

const siteUrl = ref('')

provide('isSubscriptionDialogOpen', isSubscriptionDialogOpen)

function openSubscriptionDialog(type = '') {
  Analytics.fireEvent('subscription_dialog', { type, tab_name: activeTab.value })
  appState.openSubscriptionDialog(type)
}

provide('isPromptBookmark', isPromptBookmark)

const bookmarkLink = ref('')
provide('bookmarkLink', bookmarkLink)

onMounted(async () => {
  Analytics.fireEvent('app_start', { referrer: document.referrer })

  promo.value = (await getFromPluginStorageLocal('promo').catch(() => {})) || ''
  // console.log('promo: ', promo.value)

  let currentPage = ''
  setInterval(function () {
    if (currentPage != window.location.href) {
      currentPage = window.location.href
      siteUrl.value = currentPage
    }
  }, 500)

  await initializeSharedData()
})

watch(
  () => user?.value?.uid,
  () => {
    if (user?.value?.uid) {
      // console.log('user.value.uid', user.value.uid)
      Analytics.setUserId(user.value.uid)
    }
  },
)

watch(
  () => userDoc.value,
  async () => {
    const firebaseLocalStorage =
      (await getFromPluginStorageLocal('firebaseLocalStorage').catch(() => {})) ?? []
    if (
      userDoc?.value?.id &&
      !firebaseLocalStorage.length &&
      !isLoggingIn.value &&
      !isSigningUp.value
    ) {
      removeFromLocalStorage('activeTab')
      removeFromLocalStorage('All')
      removeFromLocalStorage('Popular')
      removeFromLocalStorage('Public')
      removeFromLocalStorage('My Prompts')
      removeFromLocalStorage('Library')
      removeFromLocalStorage('Favorites')

      await signOut(auth)
      setToPluginStorageLocal('firebaseLocalStorage', [])
    }
  },
)

const outsideTextarea = ref('')
const outsideTextareaPrompt = ref('')

watch(
  () => siteUrl.value,
  (newVal, oldVal) => {
    // console.log('oldVal: ', oldVal, 'newVal: ', newVal)
    // console.log('siteUrl.value', siteUrl.value)

    if (!oldVal) {
      // console.log('no oldVal')
      setPhotopeaLinks()
    }

    showApp.value = false
    if (chatSite && chatSite.allowOnSubdomains) {
      // console.log('chatSite.allowOnSubdomains: ', chatSite.allowOnSubdomains)
      for (const subdomain of chatSite.allowOnSubdomains) {
        // console.log('subdomain: ', subdomain)
        if (subdomain === 'home' && siteUrl.value === chatSite.url) {
          showApp.value = true
          break
        }
        if (siteUrl.value.includes(subdomain)) {
          showApp.value = true
          break
        }
      }
    } else {
      // console.log('showApp else')
      showApp.value = true
    }
    // console.log('showApp: ', showApp.value)
    let oldTextarea = outsideTextarea.value
    outsideTextarea.value = getOutsideSelectorFromChatSite('textarea')
    outsideButton.value = getOutsideSelectorFromChatSite('button')
    if (!outsideTextarea.value) return

    const updateOutsideTextareaPrompt = () => {
      // console.log('updateOutsideTextareaPrompt')
      if (
        outsideTextarea.value instanceof HTMLTextAreaElement ||
        outsideTextarea.value instanceof HTMLInputElement
      ) {
        outsideTextareaPrompt.value = outsideTextarea.value.value
        outsideTextareaPromptRef.value = outsideTextarea.value.value
      } else {
        let content = outsideTextarea.value.innerHTML
        outsideTextareaPrompt.value = content
          .replace(/<p><br><\/p>/g, '\n\n')
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<\/p><p>/g, '\n')
          .replace(/<[^>]+>/g, '')
          .replace(/&nbsp;/g, ' ')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .trim()
      }
      outsideTextareaPromptRef.value = outsideTextareaPrompt.value
      // Check if the input is empty
      if (outsideTextareaPrompt.value === '') {
        // console.log('Input is empty')
      }
    }

    const checkIfEmptyOnKeydown = (event) => {
      if (event.key === 'Backspace' || event.key === 'Delete') {
        setTimeout(() => {
          // console.log('checkIfEmptyOnKeydown')
          // if (outsideTextarea.value.value === '') {
          updateOutsideTextareaPrompt()
          // }
        }, 50)
      }
    }

    const checkIfEmptyOnCut = (event) => {
      setTimeout(() => {
        // console.log('checkIfEmptyOnCut')
        // if (outsideTextarea.value.value === '') {
        updateOutsideTextareaPrompt()
        // }
      }, 50)
    }

    updateOutsideTextareaPrompt()

    let outsideTextareaMutationObserver = null

    if (oldTextarea && typeof oldTextarea.removeEventListener === 'function') {
      oldTextarea.removeEventListener('input', updateOutsideTextareaPrompt)
      oldTextarea.removeEventListener('paste', updateOutsideTextareaPrompt) // Remove paste event listener
      oldTextarea.removeEventListener('keydown', checkIfEmptyOnKeydown) // Remove keydown event listener
      oldTextarea.removeEventListener('cut', checkIfEmptyOnCut) // Remove cut event listener
      outsideTextareaPrompt.value = ''
      outsideTextareaPromptRef.value = ''

      // Disconnect MutationObserver if the oldTextarea is a div
      if (oldTextarea.tagName.toLowerCase() === 'div' && outsideTextareaMutationObserver) {
        outsideTextareaMutationObserver.disconnect()
        outsideTextareaMutationObserver = null
      }
    }
    if (outsideTextarea.value && typeof outsideTextarea.value.addEventListener === 'function') {
      outsideTextarea.value.addEventListener('input', updateOutsideTextareaPrompt)
      outsideTextarea.value.addEventListener('paste', updateOutsideTextareaPrompt) // Add paste event listener
      outsideTextarea.value.addEventListener('keydown', checkIfEmptyOnKeydown) // Add keydown event listener
      outsideTextarea.value.addEventListener('cut', checkIfEmptyOnCut) // Add cut event listener

      // Add MutationObserver to handle innerHTML changes if the element is a div
      if (outsideTextarea.value.tagName.toLowerCase() === 'div') {
        outsideTextareaMutationObserver = new MutationObserver(updateOutsideTextareaPrompt)
        outsideTextareaMutationObserver.observe(outsideTextarea.value, {
          childList: true,
          subtree: true,
        })
      }
    }
  },
)

const {
  openEditPromptBarDialog,
  closeEditPromptBarDialog,
  barIdToCopyFrom,
  barsTabName,
  initialEditUrl,
} = usePromptBarManagement()

provide('barIdToCopyFrom', barIdToCopyFrom)
provide('barsTabName', barsTabName)

const closeAddPromptBarDialog = async (newPromptBarId = '') => {
  appState.isAddPromptBarDialogOpen = false

  appState.setAutomaticNewBar(false)

  if (newPromptBarId.length) {
    myPromptBars.value = await getUserBars(user.value.uid)

    appState.updateActiveBarIdForTab(TABS.MyPrompts, newPromptBarId)

    selectedBarId.value = newPromptBarId
    const newBar = await getBarById(newPromptBarId)
    selectedBar.value = newBar

    if (appState.nameForGeneratedPrompt) {
      await onBarChange(newPromptBarId)
    }
    openEditPromptBarDialog()
    isTabEmpty.value = false
  }
}

const handleAddBar = async ({ barId, url }) => {
  await closeAddPromptBarDialog(barId)
  // initialEditUrl.value = url
  appState.openEditPromptBarDialog()
}

const isInformationDialogOpen = ref(false)
const informationDialogLinkItem = ref('')
const openInformationDialog = (data) => {
  isInformationDialogOpen.value = true
  informationDialogLinkItem.value = data
}
const closeInformationDialog = () => {
  isInformationDialogOpen.value = false
}

const isPromptBarPublic = ref(undefined)
const isPromptBarListed = ref(undefined)
const isPromptBarEditable = ref(undefined)

const openSharePromptBarDialog = (returnFromBarIsPublicEdit) => {
  if (returnFromBarIsPublicEdit) {
    closeEditPromptBarDialog()
  }
  sharePrompt.value = ''
  appState.openSharePromptBarDialog()
}
const closeSharePromptBarDialog = () => {
  appState.closeSharePromptBarDialog()
  isPromptBarPublic.value = undefined
  isPromptBarListed.value = undefined
  isPromptBarEditable.value = undefined
}

const changeIsPublic = (isPublic) => {
  isPromptBarPublic.value = isPublic
  openEditPromptBarDialog()
}

const changeIsListed = (isListed) => {
  isPromptBarListed.value = isListed
  openEditPromptBarDialog()
}

const changeIsEditable = (isEditable) => {
  isPromptBarEditable.value = isEditable
  openEditPromptBarDialog()
}

function updateBars(bar, property) {
  if (!bar || !property) return

  if (property === 'isPublic') {
    if (bar.isPublic) {
      publicBars.value.push(bar)
    } else {
      publicBars.value = publicBars.value.filter((publicBar) => publicBar.id !== bar.id)
    }
  } else if (property === 'isPopular') {
    if (bar.isPopular) {
      popularBars.value.push(bar)
    } else {
      popularBars.value = popularBars.value.filter((popularBar) => popularBar.id !== bar.id)
    }
  } else if (property === 'isInLibrary') {
    if (bar.isInLibrary) {
      libraryBars.value.push(bar)
    } else {
      libraryBars.value = libraryBars.value.filter((libraryBar) => libraryBar.id !== bar.id)
    }
  }
}

watchEffect(async () => {
  const firebaseLocalStorage =
    (await getFromPluginStorageLocal('firebaseLocalStorage').catch(() => {})) ?? []

  if (firebaseLocalStorage.length) {
    const request = window.indexedDB.open('firebaseLocalStorageDb')
    request.onsuccess = (event) => {
      // console.log('request.onsuccess', event)
      const db = event.target.result

      // console.log('db', db)
      const objectStore = db
        .transaction('firebaseLocalStorage', 'readwrite')
        .objectStore('firebaseLocalStorage')

      for (const firebaseLocalStorageItem of firebaseLocalStorage) {
        // console.log('firebaseLocalStorageItem: ', firebaseLocalStorageItem)
        objectStore.add(firebaseLocalStorageItem).onsuccess = (event) => {}
      }
    }

    request.onerror = (event) => {}
  }
})

const handlePluginStorageChange = async (key, oldValue, newValue) => {
  if (key === 'editDateByBarId' && appState.activeTab === TABS.MyPrompts) {
    if (!selectedBarId.value || !selectedBar.value || isEqual(oldValue, newValue)) return

    const { editedAt } = selectedBar.value
    const editedAtString = new Date(
      editedAt.seconds * 1000 + editedAt.nanoseconds / 1000000,
    ).toISOString()
    if (selectedBarId.value && newValue[selectedBarId.value] > editedAtString) {
      setTimeout(async () => {
        selectedBar.value = await getBarById(selectedBarId.value)
        const barIndex = myPromptBars.value.findIndex((bar) => bar.id === selectedBarId.value)
        myPromptBars.value[barIndex] = selectedBar.value
        setToLocalStorage(TABS.MyPrompts, selectedBar.value)
      }, 0)
    }
  }
}

onChangeInPluginStorageLocal(handlePluginStorageChange)

// Note: Removed watch on teamBars for selectedBar updates to avoid conflicts
// The setupTeamBarListener handles all real-time updates for Team tab

// Real-time listener for full bar data when in Team tab
let teamBarUnwatch = null

const setupTeamBarListener = (barId) => {
  // Clean up previous listener
  if (teamBarUnwatch) {
    teamBarUnwatch()
    teamBarUnwatch = null
  }

  if (!barId) return

  const barRef = doc(barsRef, barId)
  const barDoc = useDocument(barRef)

  teamBarUnwatch = watch(
    barDoc,
    (newBarData) => {
      if (newBarData && activeTab.value === TABS.Team && selectedBarId.value === barId) {
        // Parse prompt and link menus
        newBarData.promptMenu = newBarData.prompt_menu
          ? JSON.parse(newBarData.prompt_menu, convertIdToString)
          : []
        newBarData.linkMenu = newBarData.links
          ? JSON.parse(newBarData.links, convertIdToString)
          : []

        selectedBar.value = { ...newBarData, id: barId }
      }
    },
    { immediate: true },
  )
}

// Watch for selectedBarId changes in Team tab to setup real-time listener
watch(
  [() => selectedBarId.value, () => activeTab.value],
  ([newBarId, newTab]) => {
    if (newTab === TABS.Team && newBarId) {
      setupTeamBarListener(newBarId)
    } else if (newTab !== TABS.Team) {
      // Clean up listener when leaving Team tab
      if (teamBarUnwatch) {
        teamBarUnwatch()
        teamBarUnwatch = null
      }
    }
  },
  { immediate: true },
)

// Cleanup listeners on component unmount
onBeforeUnmount(() => {
  if (teamBarUnwatch) {
    teamBarUnwatch()
    teamBarUnwatch = null
  }
})

const btnCopyType = ref('')

const receivedScrapsAndSiteName = async (questionANDanswer, site) => {
  if (!user || !questionANDanswer.question || !questionANDanswer.answer) {
    return
  }

  await createOrEditChat({
    prompt: questionANDanswer.question,
    answer: questionANDanswer.answer,
    user,
  })
}

const onBarChange = async (barId, _blank = false) => {
  appState.isSelectedBarLoading = true
  const bar = await getBarById(barId)

  // For Team tab, let the real-time listener handle selectedBar updates
  // to ensure proper synchronization of prompt/folder changes
  if (appState.activeTab !== TABS.Team) {
    selectedBar.value = bar
  }

  if (_blank) {
    setToPluginStorageLocal('redirectedPrompt', {
      barId,
      activeTabName: appState.activeTab,
    })

    window.open(bar.destination, '_blank', 'noopener,noreferrer')
    return
  }

  Analytics.fireEvent('bar_select', { tab_name: appState.activeTab, prompt_bar_id: barId })

  if (appState.activeTab === TABS.MyPrompts || appState.activeTab === TABS.All) {
  } else {
    setToLocalStorageSubscriptionCheck(appState.activeTab, bar, hasSubscription.value)
  }

  if (selectRef.value && selectRef.value[0]) {
    selectRef.value[0].blur()
  }

  if (
    bar.destination &&
    bar.destination !== '' &&
    (window.location.href !== bar.destination || _blank)
  ) {
    setToPluginStorageLocal('redirectedPrompt', {
      barId,
      activeTabName: appState.activeTab,
    })
  }

  appState.isSelectedBarLoading = false
}
</script>

<template>
  <div id="prompt-manager" class="prompt-manager">
    <!-- Use appState.showApp -->
    <div
      v-show="appState.showApp"
      class="prompt-toolbar relative bg-neutral-200 dark:bg-neutral-800"
      :class="{
        'h-0 hideApp': !appState.showApp,
        'm-1 rounded-lg': chatSite.name === 'ChatGPT',
        'mt-2 mx-2 rounded-2xl contents': chatSite.name === 'Bard',
        'mx-2 rounded-lg': chatSite.name === 'Bing',
        'm-2 rounded-2xl': chatSite.name === 'Perplexity',
        'm-2 rounded-lg': !['ChatGPT', 'Bard', 'Bing', 'Perplexity'].includes(chatSite.name),
      }"
    >
      <SmallMenu v-if="props.showSmallMenu" :isAdmin="isAdmin" :menuHideLevel="menuHideLevel" />
      <MenuTabs
        :menuHideLevel="menuHideLevel"
        :sharedBarId="sharedBarId"
        :isAdmin="isAdmin"
        :selectedBar="selectedBar"
        :selectedBarId="selectedBarId"
        :formatFirebaseDate="formatFirebaseDate"
        :btnCopyType="btnCopyType"
        :onBarChange="onBarChange"
        @openSubscriptionDialog="openSubscriptionDialog"
        @openSharePromptBarDialog="openSharePromptBarDialog()"
        @openInformationDialog="openInformationDialog"
        @toggleFavorite="toggleFavorite"
        @changeMenuHideLevel="changeMenuHideLevel"
      >
        <template #favorite-icon>
          <StarFilled v-if="isFavorite" />
          <Star v-else />
        </template>
      </MenuTabs>
    </div>

    <AddPromptBarDialog
      v-if="appState.isAddPromptBarDialogOpen"
      :isOpen="appState.isAddPromptBarDialogOpen"
      @close-dialog="closeAddPromptBarDialog"
      @add-bar="handleAddBar"
      :areBarsLoaded="areMyPromptBarsLoaded"
      :userBars="myPromptBars"
    />
    <EditPromptBarDialog
      v-if="appState.isEditPromptBarDialogOpen"
      :isOpen="appState.isEditPromptBarDialogOpen"
      @close-dialog="closeEditPromptBarDialog"
      :areBarsLoaded="areMyPromptBarsLoaded"
      :userBars="myPromptBars"
      :bar="selectedBar"
      :isPromptBookmark="appState.isPromptBookmark"
      :role="appState.userDoc?.role"
      :isAdmin="isAdmin"
      :isPromptBarPublic="isPromptBarPublic"
      :isPromptBarListed="isPromptBarListed"
      :isPromptBarEditable="isPromptBarEditable"
      :initialUrl="initialEditUrl"
      @openShare="openSharePromptBarDialog"
      @updateBars="updateBars"
    />
    <InformationDialog
      v-if="isInformationDialogOpen"
      :isOpen="isInformationDialogOpen"
      @close-dialog="closeInformationDialog"
      :linkItem="informationDialogLinkItem"
    />
    <SharePromptBarDialog
      v-if="appState.isShareDialogPromptBarOpen"
      :isOpen="appState.isShareDialogPromptBarOpen"
      @close-dialog="closeSharePromptBarDialog"
      @changeIsPublic="changeIsPublic"
      @changeIsListed="changeIsListed"
      @changeIsEditable="changeIsEditable"
    />
  </div>

  <!-- <ChatScraping @sendScrapsAndSiteName="receivedScrapsAndSiteName" :chatSite="chatSite" /> -->
</template>

<style>
#side-panel-content .prompt-toolbar {
  z-index: 100 !important;
  margin: 0;
}

@media all and (min-width: 768px) {
  .absolute.left-2.top-2.hidden.md\:inline-block + div .ChatGPT {
    padding-left: 50px;
  }
}
#prompt-manager {
  transition:
    width 150ms ease-in-out,
    opacity 150ms ease-in-out;
}
/* .prompt-toolbar:not(#_appBing .prompt-toolbar) {
  padding: 0.5rem;
} */
/* #prompt-manager input {
} */
/* fix input bg diffrence between run build and run dev */
#prompt-manager input {
  box-shadow: none;
  background-color: transparent !important;
}

.prompt-toolbar[class*='rounded-'] .el-tabs {
  border-radius: inherit;
}

.prompt-toolbar[class*='rounded-'] .el-tabs__header.is-top {
  border-top-left-radius: inherit !important;
  border-top-right-radius: inherit !important;
  overflow: hidden;
}

.menu-tabs .el-tabs__content {
  overflow: unset;
  padding: 0 !important;
}
.menu-tabs .el-tabs__header {
  border-top-left-radius: 0.375rem !important;
  border-top-right-radius: 0.375rem !important;
  margin-bottom: 0 !important;
}
.hide-header .el-tabs__header {
  border-color: transparent !important;
  display: none;
}
.hide-header .el-tabs__item {
  height: 0;
  translate: 0 -10px;
  border-color: transparent !important;
}
.small-menu {
  transition: all 0.3s ease-in-out;
  opacity: 100%;
  transform: translateY(0);
}
/* Ukryj SmallMenu gdy MenuTabs ma klasę hide-header */
.prompt-toolbar:has(.hide-header) .small-menu,
.small-menu.menu-hidden {
  opacity: 0 !important;
  transform: translateY(-80%) !important;
  pointer-events: none !important;
  transition: none !important; /* Usuń animację dla ukrywania */
}
.elem-to-hide {
  transition: all 0 ease-in-out;
  opacity: 100%;
  transform: translateY(0);
}
.hide-bar {
  height: 0 !important;
  margin: 0 !important;
  opacity: 0;
  transform: translateY(-50px);
  pointer-events: none;
}
.el-select-dropdown__wrap {
  max-height: 50vh;
}
.el-select-dropdown__item {
  height: auto !important ;
  left: 0 !important;
  padding: 0 10px !important;
}
.el-button.tagInSelect {
  margin: 0;
  padding: 0.2em 0.5em;
}
.el-select .el-popper {
  width: 100%;
}
.el-select__selection,
.el-select__input-wrapper,
.el-select__input {
  width: 100% !important;
}
.el-select__input {
  padding: 0 !important;
}
#_appAIPM input:-webkit-autofill,
#_appAIPM input:-webkit-autofill:hover,
#_appAIPM input:-webkit-autofill:focus,
#_appAIPM input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0px 1000px var(--el-bg-color-overlay, var(--el-fill-color-blank)) inset !important;
}

.flex > .el-button + .el-button {
  margin-left: 0;
}
@keyframes fadeOutUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-1em);
  }
  100% {
    opacity: 0;
    transform: translateY(-1.5em);
  }
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(1em);
  }
  100% {
    opacity: 0;
    transform: translateY(1.5em);
  }
}

.fade-out-up {
  animation: fadeOutUp 1s ease-out;
}

.fade-out-down {
  animation: fadeOutDown 1s ease-in;
}

:root {
  --wandColor: theme('colors.blue.600');
}
.dark {
  --wandColor: theme('colors.blue.400');
}
@keyframes rotateLeftRight {
  0% {
    transform-origin: bottom left;
    transform: rotate(0deg);
    color: inherit;
  }
  25% {
    transform-origin: bottom left;
    transform: rotate(-30deg);
    color: inherit;
  }
  50% {
    transform-origin: bottom left;
    transform: rotate(0deg);
    color: var(--wandColor);
  }
  75% {
    transform-origin: bottom left;
    transform: rotate(0deg);
    color: var(--wandColor);
  }
  100% {
    transform-origin: bottom left;
    transform: rotate(0deg);
    color: inherit;
  }
}

.rotate-left-right {
  animation: rotateLeftRight 3s infinite;
}

.chat-prompt-button.is-circle:has(+ .chat-prompt-button.sub-button),
.chat-prompt-button.sub-button + .chat-prompt-button.is-circle {
  z-index: 1;
}

.chat-prompt-button.sub-button {
  --add_padding: calc(var(--el-button-size, 32px) / 3);
  position: relative;
  width: calc(var(--el-button-size, 32px) + var(--add_padding));
  overflow: hidden;
  z-index: 0;
}

.chat-prompt-button.is-circle + .chat-prompt-button.sub-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding-left: calc(1.8 * var(--add_padding));
  border-left: none;
  margin-left: calc(-1 * var(--add_padding) / 1.6);
}

.chat-prompt-button.sub-button.is-circle:has(+ .chat-prompt-button.is-circle) {
  padding-left: calc(0.8 * var(--add_padding));
}

.chat-prompt-button.sub-button:has(+ .chat-prompt-button.is-circle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding-right: calc(1.8 * var(--add_padding));
  border-right: none;
  margin-right: calc(-1 * var(--add_padding) / 1.6);
}

.chat-prompt-button + .chat-prompt-button.sub-button::before,
.chat-prompt-button.sub-button:has(+ .chat-prompt-button.is-circle)::before {
  position: absolute;
  display: block;
  content: '';
  border-radius: 50%;
  border: var(--el-border);
  width: calc(var(--el-button-size, 32px) + var(--add_padding));
  height: calc(var(--el-button-size, 32px) + var(--add_padding));
  background: transparent;
  right: calc(-1 * var(--el-button-size, 32px) - calc(var(--add_padding) / 4));
}
.chat-prompt-button:not(.sub-button) + .chat-prompt-button.sub-button::before {
  left: calc(-1 * var(--el-button-size, 32px) - calc(var(--add_padding) / 4));
}

.chat-prompt-button + .chat-prompt-button.sub-button:hover::before,
.chat-prompt-button.sub-button:has(+ .chat-prompt-button.is-circle):hover::before {
  color: var(--el-button-hover-text-color);
  border-color: var(--el-button-hover-border-color);
  outline: 0;
}
</style>
