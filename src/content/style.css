@tailwind base;

.ltr\:-translate-x-1\/2:where(:dir(ltr), [dir='ltr'], [dir='ltr'] *) {
  --tw-translate-x: -50% !important;
}

.translate-y-\[-100lvh\] {
  --tw-translate-y: -100lvh;
}

@layer base {
  /* added only to promp bar bcs bugging bing */
  #_appAIPM *,
  #_appAIPM ::before,
  #_appAIPM ::after {
    /* box-sizing: border-box;*/
  }

  /* *,
  ::before,
  ::after {
    border-width: 0;
    border-style: solid;
    border-color: theme('borderColor.DEFAULT', currentColor);
  } */

  ::before,
  ::after {
    --tw-content: '';
  }

  /*
  1. Use a consistent sensible line-height in all browsers.
  2. Prevent adjustments of font size after orientation changes in iOS.
  3. Use a more readable tab size.
  4. Use the user's configured `sans` font-family by default.
  */

  html {
    line-height: 1.5; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
    -moz-tab-size: 4; /* 3 */
    tab-size: 4; /* 3 */
    font-family: theme(
      'fontFamily.sans',
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      'Noto Sans',
      sans-serif,
      'Apple Color Emoji',
      'Segoe UI Emoji',
      'Segoe UI Symbol',
      'Noto Color Emoji'
    ); /* 4 */
  }

  /*
  1. Remove the margin in all browsers.
  2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
  */

  body {
    margin: 0;
    line-height: inherit;
  }

  /*
  1. Add the correct height in Firefox.
  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
  3. Ensure horizontal rules are visible by default.
  */

  hr {
    height: 0; /* 1 */
    color: inherit; /* 2 */
    border-top-width: 1px; /* 3 */
  }

  /*
  Add the correct text decoration in Chrome, Edge, and Safari.
  */

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  /*
  Remove the default font size and weight for headings.
  */

  #_chatButton h2,
  #_chatButton h3,
  #_chatButton h4,
  #_chatButton h5,
  #_chatButton h6,
  #_chatButton .h2,
  #_chatButton .h3,
  #_chatButton .h4,
  #_chatButton .h5,
  #_chatButton .h6,
  #_appAIPM h2,
  #_appAIPM h3,
  #_appAIPM h4,
  #_appAIPM h5,
  #_appAIPM h6,
  #_appAIPM .h2,
  #_appAIPM .h3,
  #_appAIPM .h4,
  #_appAIPM .h5,
  #_appAIPM .h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  #_chatButton h2,
  #_chatButton .h2,
  #_appAIPM h2,
  #_appAIPM .h2 {
    font-size: 1.8em;
  }
  #_chatButton h3,
  #_chatButton .h3,
  #_appAIPM h3,
  #_appAIPM .h3 {
    font-size: 1.6em;
  }
  #_chatButton h4,
  #_chatButton .h4,
  #_appAIPM h4,
  #_appAIPM .h4 {
    font-size: 1.4em;
  }
  #_chatButton h5,
  #_chatButton .h5,
  #_appAIPM h5,
  #_appAIPM .h5 {
    font-size: 1.2em;
  }
  #_chatButton h6,
  #_chatButton .h6,
  #_appAIPM h6,
  #_appAIPM .h6 {
    font-size: 1.1em;
  }

  #_chatButton h1:has(+ *),
  #_chatButton h2:has(+ *),
  #_chatButton h3:has(+ *),
  #_chatButton h4:has(+ *),
  #_chatButton h5:has(+ *),
  #_chatButton h6:has(+ *),
  #_chatButton .h1:has(+ *),
  #_chatButton .h2:has(+ *),
  #_chatButton .h3:has(+ *),
  #_chatButton .h4:has(+ *),
  #_chatButton .h5:has(+ *),
  #_chatButton .h6:has(+ *),
  #_chatButton p:has(+ h1),
  #_chatButton p:has(+ h2),
  #_chatButton p:has(+ h3),
  #_chatButton p:has(+ h4),
  #_chatButton p:has(+ h5),
  #_chatButton p:has(+ h6),
  #_chatButton p:has(+ .h1),
  #_chatButton p:has(+ .h2),
  #_chatButton p:has(+ .h3),
  #_chatButton p:has(+ .h4),
  #_chatButton p:has(+ .h5),
  #_chatButton p:has(+ .h6),
  #_chatButton ul:has(+ h1),
  #_chatButton ul:has(+ h2),
  #_chatButton ul:has(+ h3),
  #_chatButton ul:has(+ h4),
  #_chatButton ul:has(+ h5),
  #_chatButton ul:has(+ h6),
  #_chatButton ul:has(+ .h1),
  #_chatButton ul:has(+ .h2),
  #_chatButton ul:has(+ .h3),
  #_chatButton ul:has(+ .h4),
  #_chatButton ul:has(+ .h5),
  #_chatButton ul:has(+ .h6),
  #_chatButton ol:has(+ h1),
  #_chatButton ol:has(+ h2),
  #_chatButton ol:has(+ h3),
  #_chatButton ol:has(+ h4),
  #_chatButton ol:has(+ h5),
  #_chatButton ol:has(+ h6),
  #_appAIPM h1:has(+ *),
  #_appAIPM h2:has(+ *),
  #_appAIPM h3:has(+ *),
  #_appAIPM h4:has(+ *),
  #_appAIPM h5:has(+ *),
  #_appAIPM h6:has(+ *),
  #_appAIPM .h1:has(+ *),
  #_appAIPM .h2:has(+ *),
  #_appAIPM .h3:has(+ *),
  #_appAIPM .h4:has(+ *),
  #_appAIPM .h5:has(+ *),
  #_appAIPM .h6:has(+ *),
  #_appAIPM p:has(+ h1),
  #_appAIPM p:has(+ h2),
  #_appAIPM p:has(+ h3),
  #_appAIPM p:has(+ h4),
  #_appAIPM p:has(+ h5),
  #_appAIPM p:has(+ h6),
  #_appAIPM p:has(+ .h1),
  #_appAIPM p:has(+ .h2),
  #_appAIPM p:has(+ .h3),
  #_appAIPM p:has(+ .h4),
  #_appAIPM p:has(+ .h5),
  #_appAIPM p:has(+ .h6),
  #_appAIPM ul:has(+ h1),
  #_appAIPM ul:has(+ h2),
  #_appAIPM ul:has(+ h3),
  #_appAIPM ul:has(+ h4),
  #_appAIPM ul:has(+ h5),
  #_appAIPM ul:has(+ h6),
  #_appAIPM ul:has(+ .h1),
  #_appAIPM ul:has(+ .h2),
  #_appAIPM ul:has(+ .h3),
  #_appAIPM ul:has(+ .h4),
  #_appAIPM ul:has(+ .h5),
  #_appAIPM ul:has(+ .h6),
  #_appAIPM ol:has(+ h1),
  #_appAIPM ol:has(+ h2),
  #_appAIPM ol:has(+ h3),
  #_appAIPM ol:has(+ h4),
  #_appAIPM ol:has(+ h5),
  #_appAIPM ol:has(+ h6) {
    margin-bottom: 0.5em;
  }

  /*
  Reset links to optimize for opt-in styling instead of opt-out.
  */

  #_chatButton a,
  #_appAIPM a {
    color: inherit;
    text-decoration: inherit;
  }

  /*
  Add the correct font weight in Edge and Safari.
  */

  #_chatButton b,
  #_appAIPM b,
  #_chatButton strong,
  #_appAIPM strong {
    font-weight: bolder;
  }

  /*
  1. Use the user's configured `mono` font family by default.
  2. Correct the odd `em` font sizing in all browsers.
  */

  #_chatButton code,
  #_appAIPM code,
  #_chatButton kbd,
  #_appAIPM kbd,
  #_chatButton samp,
  #_appAIPM samp,
  #_chatButton pre,
  #_appAIPM pre {
    font-family: theme(
      'fontFamily.mono',
      ui-monospace,
      SFMono-Regular,
      Menlo,
      Monaco,
      Consolas,
      'Liberation Mono',
      'Courier New',
      monospace
    ); /* 1 */
    font-size: 1em; /* 2 */
  }

  /*
  Add the correct font size in all browsers.
  */

  #_chatButton small,
  #_appAIPM small {
    font-size: 80%;
  }

  /*
  Prevent `sub` and `sup` elements from affecting the line height in all browsers.
  */

  #_chatButton sub,
  #_appAIPM sub,
  #_chatButton sup,
  #_appAIPM sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  #_chatButton sub,
  #_appAIPM sub {
    bottom: -0.25em;
  }

  #_chatButton sup,
  #_appAIPM sup {
    top: -0.5em;
  }

  /*
  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
  3. Remove gaps between table borders by default.
  */

  #_chatButton table,
  #_appAIPM table {
    text-indent: 0; /* 1 */
    border-color: inherit; /* 2 */
    border-collapse: collapse; /* 3 */
  }

  /*
  Add the correct vertical alignment in Chrome and Firefox.
  */

  progress {
    vertical-align: baseline;
  }

  /*
  Correct the cursor style of increment and decrement buttons in Safari.
  */

  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }

  /*
  1. Correct the odd appearance in Chrome and Safari.
  2. Correct the outline style in Safari.
  */

  [type='search'] {
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
  }

  /*
  Remove the inner padding in Chrome and Safari on macOS.
  */

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  /*
  1. Correct the inability to style clickable types in iOS and Safari.
  2. Change font properties to `inherit` in Safari.
  */

  ::-webkit-file-upload-button {
    -webkit-appearance: button; /* 1 */
    font: inherit; /* 2 */
  }

  /*
  Add the correct display in Chrome and Safari.
  */

  summary {
    display: list-item;
  }

  /*
  Removes the default spacing and border for appropriate elements.
  */

  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    /* margin: 0; */
  }

  fieldset {
    /* margin: 0;
    padding: 0; */
  }

  legend {
    /* padding: 0; */
  }

  ol,
  ul,
  menu {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  /*
  Prevent resizing textareas horizontally by default.
  */
  input,
  textarea {
    /* background-color: transparent; */
  }

  /*
  1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
  2. Set the default placeholder color to the user's configured gray 400 color.
  */

  input::placeholder,
  textarea::placeholder {
    opacity: 1; /* 1 */
    color: theme('colors.gray.400', #9ca3af); /* 2 */
  }

  /*
  Set the default cursor for buttons.
  */

  button,
  [role='button'] {
    cursor: pointer;
  }

  /*
  Make sure disabled buttons don't get the pointer cursor.
  */
  :disabled {
    cursor: default;
  }

  /*
  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
     This can trigger a poorly considered lint error in some tools but is included by design.
  */

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    /* disabled bugging bard bing*/
    /* display: block;*/
    /* vertical-align: middle; */
  }

  /*
  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
  */

  img,
  video {
    max-width: 100%;
    height: auto;
  }
}

@tailwind components;
@layer components {
  /* Commented - need to fix */
  /* .prompt-manager button:not(:hover),
  .prompt-manager [type='button']:not(:hover) {
    background-color: var(--el-button-bg-color, var(--el-color-transparent));
  } */
}

@tailwind utilities;

.ChatGPT-body #_appAIPM {
  z-index: 1000;
}
.ChatGPT-body div.flex:has(> #_chatButton) {
  flex-direction: column;
}
.ChatGPT-body div.flex > #_chatButton {
  width: 100%;
}

.Copilot-body #_appAIPM {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 100000;
}

.Copilot-body #_chatButton {
  z-index: 1000;
}

#_appAIPM.Copilot ~ #app {
  --top: 151px;
  height: calc(100% - var(--top));
}
#_appAIPM.Copilot:has(.hide-header) ~ #app {
  --top: 112px;
}
#_appAIPM.Copilot:has(.hide-header):has(.hide-bar) ~ #app {
  --top: 70px;
}

.Vespa-body.subpage #_appAIPM {
  position: relative;
  z-index: 1000;
}
.Vespa-body:not(.subpage) #_appAIPM {
  position: sticky;
  top: 0;
}
#_appAIPM {
  position: relative;
  z-index: 5;
}

#_appAIPM.Perplexity {
  --perplexity-offset: 72px;
  position: sticky;
  inset: 0 0 auto 0;
  margin-left: var(--perplexity-offset);
  z-index: 1000;
  width: calc(100% - var(--perplexity-offset));
  height: 0px;
  font-family: var(--font-fk-grotesk-neue, 'FK Grotesk Neue');
}

#_appAIPM.DeepSeek {
  --deepseek-offset: 270px;
  position: sticky;
  inset: 0 0 auto 0;
  margin-left: var(--deepseek-offset);
  z-index: 1000;
  width: calc(100% - var(--deepseek-offset));
  height: 0px;
}

#_appAIPM.Qwen {
  position: sticky;
  inset: 0 0 auto 0;
  z-index: 1000;
  margin-left: var(--qwen-offset);
}

#_chatButton.Qwen {
  margin-inline: auto !important;
}

#_chatButton.LMArena {
  margin-inline: auto !important;
  width: 100% !important;
}

#_appAIPM.Ideogram {
  position: sticky;
  inset: 0 0 auto 0;
  z-index: 1000;
  margin-inline: auto;
}

#_appAIPM.LMArena {
  z-index: 1000;
}

#_chatButton.AIStudio {
  position: sticky;
  bottom: 0;
  left: 0;
  top: 0;
  right: 0;
  z-index: 10000;
}

#_chatButton.Ideogram {
  bottom: 0;
  left: 0;
  top: -20px;
  right: 0;
  width: 859px;
  margin-inline: auto;
  z-index: 1;
}

#_chatButton.AIStudio {
  z-index: 1000;
}

#_chatButton.DeepSeek {
  opacity: 1 !important;
}

ms-prompt-input-wrapper .prompt-input-wrapper,
ms-prompt-input-wrapper .prompt-input-wrapper-container,
ms-prompt-input-wrapper .bottom-buttons {
  /* aistudio */
}

.MuiBox-root.css-1gcektc {
  z-index: 100 !important;
  top: -20px !important;
}

:has(.left-0.inset-y-0.absolute) #_appAIPM.Perplexity,
:has(button[aria-label='Unpin Sidebar']) #_appAIPM.Perplexity {
  --perplexity-offset: 272px;
}

#_appAIPM.Ideogram {
  z-index: 1340;
}
.Ideogram-body #_chatButton {
  position: relative;
  z-index: 1336;
}

#_appAIPM + div {
  --top: 133px;
}
#_appAIPM:has(.hide-header) + div {
  --top: 103px;
}
#_appAIPM:has(.hide-header):has(.hide-bar) + div {
  --top: 54px;
}
#_appAIPM.Perplexity:not(:has(.hideApp)) + div .h-full > .sticky {
  top: var(--top);
}
#_appAIPM.Perplexity:not(:has(.hideApp)) + div > .flex > .grow {
  margin-top: var(--top);
}
#_appAIPM.Perplexity:not(:has(.hideApp)) + div > .flex > .hidden > div > .fixed {
  max-height: calc(100% - var(--top));
  margin-top: var(--top);
}
#_appAIPM.Perplexity:not(:has(.hideApp)) + div div.relative:has(.pointer-events-none #_chatButton) {
  padding-bottom: 50px;
}
/* fix for bing */
div[id*='el-popper'] {
  display: block !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 100000 !important;
}

/* Element Plus dialog and overlay fixes - TYLKO TŁO ZA DIALOGIEM */
.el-overlay,
.el-overlay-dialog,
[class*='el-overlay']:not(.el-dialog__wrapper) {
  background-color: rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.5) !important;
  opacity: 1 !important;
  z-index: 9999998 !important;
}

.el-dialog {
  z-index: 9999999 !important;
}

/* Specjalne style dla overlay, ale NIE dla wrapper */
.el-dialog__wrapper .el-overlay,
.el-overlay + .el-dialog__wrapper,
.el-dialog__wrapper + .el-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.5) !important;
  opacity: 1 !important;
}

/* Nadpisanie tylko dla overlay w body */
/* body > .el-overlay,
body > [class*='overlay']:not([class*='dialog__wrapper']) {
  background-color: rgba(0, 0, 0, 0.5) !important;
  background: rgba(0, 0, 0, 0.5) !important;
} */

/* NIE ZMIENIAJ tła el-dialog__wrapper - to może zawierać dialog */
.el-dialog__wrapper {
  background: transparent !important;
}

/* Element Plus popper container fixes */
[id*='el-popper-container'] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 9999999 !important;
}

[id*='el-popper-container'] > * {
  pointer-events: auto !important;
}

#_appAIPM.Claude {
  z-index: 1000;
}

#_appAIPM.Claude + div,
#_appAIPM.Claude + div > .h-screen:first-child {
  --top: 150px;
  height: calc(100dvh - var(--top)) !important;
  z-index: 0;
}

#_appAIPM.Claude + div > .h-screen:first-child > header {
  z-index: auto;
}

#_chatButton {
  width: 100%;
  max-width: 768px;
  pointer-events: auto;
  margin-inline: auto;
}

.Vespa-body #_chatButton,
.Ideogram-body #_chatButton {
  max-width: 100%;
}

/* fix for gpt canva */
.prompt-manager {
  container-type: inline-size;
  -webkit-container-type: inline-size;
}

@container (max-width: 520px) {
  #_appAIPM .hidden,
  #_chatButton .hidden {
    display: none !important;
  }
}

@container (min-width: 520px) {
  #_appAIPM .sm\:block,
  #_chatButton .sm\:block {
    display: block !important;
  }
  #_appAIPM .sm\:ml-2,
  #_chatButton .sm\:ml-2 {
    margin-left: 0.5rem !important;
  }
}

@container (max-width: 920px) {
  .small-menu.relative {
    position: relative !important;
  }
  .bar-container {
    display: flex !important;
    height: auto !important;
  }
}

@container (min-width: 520px) {
  #_appAIPM .top-navigation-bar .clg\:block,
  #_chatButton .top-navigation-bar .clg\:block {
    display: block !important;
  }
}

@container (min-width: 1100px) {
  #_appAIPM .clg\:block,
  #_chatButton .clg\:block {
    display: block !important;
  }
}

:has(.a02af2e6) #_appAIPM.DeepSeek {
  --deepseek-offset: 70px;
}

.max-w-full.flex-1 {
  z-index: 0 !important;
}

.Bolt-body #resources-dropdown {
  top: calc(var(--top) + 77px) !important;
}

.z-workbench > div:first-child {
  top: calc(var(--top) + 77px) !important;
  overflow: hidden;
}

#_appAIPM.Bolt {
  position: sticky;
  inset: 0 0 auto 0;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.Bolt-body.subpage #_appAIPM {
  position: sticky;
  top: 0;
  z-index: 1000;
  margin-bottom: 0;
}

.Bolt-body:has(.side-menu[style*='opacity: 1'][style*='left: 0']) #_appAIPM.Bolt {
  margin-left: 350px;
  width: calc(100% - 350px);
}

#_chatButton.Bolt {
  z-index: 1;
  position: relative;
  margin-inline: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.Bolt-body:has(.side-menu[style='opacity: 1;']) #_appAIPM.Bolt {
  --bolt-offset: 200px;
  margin-left: var(--bolt-offset);
  width: calc(100% - var(--bolt-offset));
}

#_chatButton.Bolt .ai-response {
  z-index: 10000 !important;
}

.Bolt-body .flex .justify-center .gap-3 {
  z-index: 1 !important;
}

.Bolt-body #root {
  height: calc(100% - var(--top) - 16px);
}

#_chatButton.Bolt .better-prompt-button-group {
  gap: 0px !important;
  padding: 0 !important;
  flex-shrink: 1;
  font-size: 0.9em !important;
}

#_chatButton.Bolt .better-prompt-button-group button {
  padding: 10px !important;
  font-size: 0.9em !important;
}

.Bolt-body:not(:has(.z-workbench)) #_chatButton {
  width: 700px !important;
  margin-left: -100px !important;
  display: block;
}

.Bolt-body ~ * {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
}

.ChatGPT-body #stage-slideover-sidebar {
  z-index: 0 !important;
}

.You-body #_appAIPM {
  z-index: 1000;
  position: sticky;
  inset: 0 0 auto 0;
}

.You-body #_chatButton {
  margin-inline: auto !important;
}

.You-body ._1r4xqmx1 {
  margin-top: -200px !important;
}

.Grok#_appAIPM ~ .\@container\/main {
  --top: 149px;
  height: calc(100% - var(--top));
}
.Grok#_appAIPM:has(.hide-header) ~ .\@container\/main {
  --top: 110px;
}
.Grok#_appAIPM:has(.hide-header):has(.hide-bar) ~ .\@container\/main {
  --top: 70px;
}

.Grok-body #_chatButton {
  z-index: 100000 !important;
}

/* Grok fix covered textarea */
.Grok-body .chat-input-backdrop {
  height: 0px !important;
}

#_appAIPM.Lovable {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000 !important;
}

#_appAIPM.Lovable + div > div {
  --top: 149px;
  height: calc(100dvh - var(--top));
}
#_appAIPM.Lovable:has(.hide-header) + div > div {
  --top: 110px;
}
#_appAIPM.Lovable:has(.hide-header):has(.hide-bar) + div > div {
  --top: 70px;
}

.Lovable-body div[data-panel] {
  min-width: 550px !important;
}

#_chatButton.Lovable .better-prompt-button-group {
  gap: 0px !important;
  flex-shrink: 1;
  font-size: 0.8em !important;
}

#_chatButton.Lovable .better-prompt-button-group button {
  font-size: 0.9em !important;
}

.Mistral-body #_appAIPM {
  z-index: 1000;
  position: sticky;
  margin-left: var(--mistral-offset);
  width: calc(100% - var(--mistral-offset));
  inset: 0 0 auto 0;
  --mistral-offset: 60px;
}

.Mistral-body #_chatButton {
  z-index: 1000;
}

:has(.Mistral-body [data-sidebar='sidebar'] > div.h-full.w-full) #_appAIPM.Mistral {
  --mistral-offset: 300px;
}

#_appAIPM.Mistral + div,
#_appAIPM.Mistral + div .group\/sidebar-wrapper main > div {
  --top: 149px;
  height: calc(100dvh - var(--top));
}
#_appAIPM.Mistral:has(.hide-header) + div,
#_appAIPM.Mistral:has(.hide-header) + div .group\/sidebar-wrapper main > div {
  --top: 110px;
}
#_appAIPM.Mistral:has(.hide-header):has(.hide-bar) + div,
#_appAIPM.Mistral:has(.hide-header):has(.hide-bar) + div .group\/sidebar-wrapper main > div {
  --top: 70px;
}

#_appAIPM.Manus {
  --manus-offset: 0;
  z-index: 1000;
  position: sticky;
  inset: 0 0 auto var(--manus-offset);
  width: calc(100% - var(--manus-offset));
}

#_appAIPM.Manus + div {
  --top: 149px;
  height: calc(100dvh - var(--top));
}
#_appAIPM.Manus:has(.hide-header) + div {
  --top: 110px;
}
#_appAIPM.Manus:has(.hide-header):has(.hide-bar) + div {
  --top: 70px;
}

:has(div > .fixed[style*='width: 400px']) #_appAIPM.Manus {
  --manus-offset: 400px;
}

:has(div > .fixed[style*='width: 300px']) #_appAIPM.Manus {
  --manus-offset: 300px;
}

:has(div > .fixed[style*='width: 280px']) #_appAIPM.Manus {
  --manus-offset: 280px;
}

#_chatButton.Manus {
  z-index: 1000;
}

#_appAIPM.Genspark {
  z-index: 1000;
  position: sticky;
  inset: 0 0 auto 0;
  margin-inline: auto;
}

#_appAIPM.Genspark + div,
#_appAIPM.Genspark + div .app-container,
#_appAIPM.Genspark + div .main-wrapper {
  --top: 149px;
  height: calc(100dvh - var(--top));
}
#_appAIPM.Genspark:has(.hide-header) + div,
#_appAIPM.Genspark:has(.hide-header) + div .app-container,
#_appAIPM.Genspark:has(.hide-header) + div .main-wrapper {
  --top: 110px;
}
#_appAIPM.Genspark:has(.hide-header):has(.hide-bar) + div,
#_appAIPM.Genspark:has(.hide-header):has(.hide-bar) + div .app-container,
#_appAIPM.Genspark:has(.hide-header):has(.hide-bar) + div .main-wrapper {
  --top: 70px;
}

.Genspark-body #_chatButton + div:not(.hide-scrollbar) {
  margin-top: 0 !important;
}

#_appAIPM.Freepik {
  position: sticky;
  top: 0;
  z-index: 1000000;
}

#_appAIPM.Freepik + div,
#_appAIPM.Freepik + div > div:first-child {
  --top: 149px;
  height: calc(100dvh - var(--top));
  min-height: calc(100dvh - var(--top));
}
#_appAIPM.Freepik:has(.hide-header) + div,
#_appAIPM.Freepik:has(.hide-header) + div > div:first-child {
  --top: 110px;
}
#_appAIPM.Freepik:has(.hide-header):has(.hide-bar) + div,
#_appAIPM.Freepik:has(.hide-header):has(.hide-bar) + div > div:first-child {
  --top: 70px;
}

#_appAIPM.Freepik + div > div:first-child > div,
#_appAIPM.Freepik + div #anim-wrapper {
  height: calc(100dvh - var(--top) - 64px);
  min-height: calc(100dvh - var(--top) - 64px);
}

.Freepik-body #left-column {
  min-width: 500px !important;
}

:has(#logo > div > div) #_appAIPM.Freepik {
  margin-left: 250px;
}

.Jules-body #_appAIPM {
  z-index: 1000;
}

.Jules-body .better-prompt-button-group {
  gap: 0px !important;
  font-size: 0.9em !important;
}

.Jules-body .better-prompt-button-group button {
  font-size: 1.05em !important;
}

.Jules-body .prompt-manager :is(.text-base) {
  font-size: 1.2em !important;
  line-height: inherit !important;
}

.Jules-body .prompt-manager :is(.text-xs) {
  font-size: small !important;
  line-height: inherit !important;
}

.Jules-body .prompt-manager :is(.text-lg) {
  font-size: large !important;
  line-height: inherit !important;
}

.Jules-body .prompt-manager :is(.text-xl) {
  font-size: larger !important;
  line-height: 1rem !important;
}

.Jules-body .prompt-manager :is(.text-3xl) {
  font-size: 2em !important;
  line-height: 1.5em !important;
}

.Jules-body .prompt-manager :is(.text-\[0\.65rem\]) {
  font-size: 0.75em !important;
}

.Jules-body .prompt-manager :is(.border-b) {
  border-bottom-style: solid;
}

.Jules-body .prompt-manager :is(.leading-snug) {
  line-height: 1.675;
}

.Jules-body .prompt-manager .text-sm {
  font-size: 1.2rem !important;
  line-height: 1.5rem !important;
}

.NotebookLM-body #_chatButton {
  margin-inline: auto;
}

.NotebookLM-body #_appAIPM {
  z-index: 100000;
}

.NotebookLM-body .boqOnegoogleliteOgbOneGoogleBar {
  --top: 149px;
  top: var(--top);
}
.NotebookLM-body:has(.hide-header) .boqOnegoogleliteOgbOneGoogleBar {
  --top: 110px;
}
.NotebookLM-body:has(.hide-header):has(.hide-bar) .boqOnegoogleliteOgbOneGoogleBar {
  --top: 70px;
}

#_appAIPM.Canva {
  z-index: 1000;
  position: sticky;
  inset: 0 0 auto 0;
  margin-inline: auto;
  --canva-offset: 66px;
  left: var(--canva-offset);
}

#root:not(:has(.zH6SgA)) #_appAIPM.Canva {
  --canva-offset: 330px;
}

#_appAIPM.Canva ~ main {
  --top: 149px;
  min-height: calc(100dvh - var(--top)) !important;
}
#_appAIPM.Canva:has(.hide-header) ~ main {
  --top: 110px;
}
#_appAIPM.Canva:has(.hide-header):has(.hide-bar) ~ main {
  --top: 70px;
}
:has(.Pdo4dA) #_appAIPM.Canva ~ main {
  height: calc(100dvh - var(--top));
}

.Canva-body #_appAIPM,
.Canva-body #_chatButton {
  font-size: 16px;
}

.Canva-body #_chatButton {
  margin-inline: auto;
}

.Canva-body .prompt-manager :is(.text-base) {
  font-size: 1.2em !important;
  line-height: inherit !important;
}

.Canva-body .prompt-manager :is(.text-xs) {
  font-size: small !important;
  line-height: inherit !important;
}

.Canva-body .prompt-manager :is(.text-sm) {
  font-size: 1em !important;
  line-height: inherit !important;
}

.Canva-body .prompt-manager :is(.text-lg) {
  font-size: large !important;
  line-height: inherit !important;
}

.Canva-body .prompt-manager :is(.text-xl) {
  font-size: larger !important;
  line-height: 1rem !important;
}

.Canva-body .prompt-manager :is(.text-3xl) {
  font-size: 2em !important;
  line-height: 1.5em !important;
}

.Canva-body .prompt-manager :is(.text-\[0\.65rem\]) {
  font-size: 0.75em !important;
}

.Canva-body .prompt-manager :is(.border-b) {
  border-bottom-style: solid;
}

.Canva-body .prompt-manager :is(.leading-snug) {
  line-height: 1.675;
}

.Canva-body .el-menu--horizontal .recursive-submenu .el-sub-menu__title,
.Canva-body .el-menu.el-menu--horizontal .el-menu-item.recursive-link {
  line-height: 3rem !important;
  height: 3rem !important;
}

.Canva-body #_chatButton {
  margin-inline: auto;
  z-index: 10000;
}

.Canva-body .BMOCzQ._6W88FA {
  z-index: 0 !important;
}
