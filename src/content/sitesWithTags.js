export const sitesWithTags = [
  {
    id: 'ChatGPT',
    name: 'Chat GPT',
    url: 'https://chatgpt.com',
    tags: ['Chat', 'Graphics', 'Programming', 'Tools', 'Voice'],
  },
  {
    id: 'Bard',
    name: '<PERSON> Gemini',
    url: 'https://gemini.google.com',
    tags: ['Chat', 'Graphics', 'Voice'],
  },
  {
    id: '<PERSON>',
    name: '<PERSON>',
    url: 'https://claude.ai',
    tags: ['Chat', 'Programming', 'Tools'],
  },
  {
    id: 'Perplexity',
    name: 'Perplexity',
    url: 'https://www.perplexity.ai',
    tags: ['Chat', 'AI model hub', 'Voice'],
  },
  {
    id: 'AIStudio',
    name: 'AI Studio',
    url: 'https://aistudio.google.com',
    tags: ['Chat', 'AI model hub', 'Tools', 'Graphics', 'Movie'],
  },
  {
    id: '<PERSON>rok',
    name: '<PERSON><PERSON>',
    url: 'https://grok.com/',
    tags: ['Chat', 'Graphics', 'Programming'],
  },
  {
    id: 'DeepSeek',
    name: 'DeepSeek',
    url: 'https://chat.deepseek.com',
    tags: ['Chat', 'Programming'],
  },
  {
    id: 'Qwen',
    name: 'Qwen',
    url: 'https://chat.qwen.ai',
    tags: ['Chat', 'Graphics', 'Movie', 'Programming', 'Voice'],
  },
  {
    id: 'Mistral',
    name: 'Mistral',
    url: 'https://chat.mistral.ai/chat',
    tags: ['Chat', 'Programming'],
  },
  {
    id: 'Copilot',
    name: 'Microsoft Copilot',
    url: 'https://copilot.microsoft.com',
    tags: ['Chat', 'Graphics'],
  },
  {
    id: 'Canva',
    name: 'Canva',
    url: 'https://www.canva.com',
    tags: ['Tools', 'Graphics', 'Movie'],
  },
  {
    id: 'Ideogram',
    name: 'Ideogram',
    url: 'https://ideogram.ai',
    tags: ['Graphics'],
  },
  {
    id: 'Freepik',
    name: 'Freepik AI Assistant',
    url: 'https://www.freepik.com',
    tags: ['Graphics'],
  },
  {
    id: 'NotebookLM',
    name: 'NotebookLM',
    url: 'https://notebooklm.google.com',
    tags: ['Chat', 'Voice'],
  },
  {
    id: 'LMArena',
    name: 'LMArena',
    url: 'https://lmarena.ai',
    tags: ['Chat', 'AI model hub'],
  },
  {
    id: 'Lovable',
    name: 'Lovable',
    url: 'https://lovable.dev',
    tags: ['Programming'],
  },
  {
    id: 'Bolt',
    name: 'Bolt',
    url: 'https://bolt.new',
    tags: ['Programming'],
  },
  {
    id: 'Jules',
    name: 'Jules',
    url: 'https://jules.google.com/task',
    tags: ['Programming'],
  },
  {
    id: 'Manus',
    name: 'Manus',
    url: 'https://manus.im',
    tags: ['Universal AI agent'],
  },
  {
    id: 'Genspark',
    name: 'Genspark',
    url: 'https://www.genspark.ai/',
    tags: ['Universal AI agent'],
  },
]

export const allTags = [
  'Chat',
  'Graphics',
  'Programming',
  'Tools',
  'Voice',
  'AI model hub',
  'Movie',
  'Universal AI agent',
]

// Migration script to upload data to Firestore
// This function should be called once to migrate the data
export async function migrateSitesToFirestore() {
  try {
    // Import Firestore functions
    const { doc, setDoc } = await import('firebase/firestore')
    const { settingsRef } = await import('./firebase.js')

    const sitesData = {
      sitesWithTags,
      allTags,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
    }

    const sitesDocRef = doc(settingsRef, 'sites')
    await setDoc(sitesDocRef, sitesData)

    console.log('Sites data successfully migrated to Firestore!')
    return true
  } catch (error) {
    console.error('Error migrating sites data to Firestore:', error)
    return false
  }
}

// Uncomment the line below to run the migration
// migrateSitesToFirestore()
