import { deleteDoc, deleteField, doc, getDoc, query, setDoc } from 'firebase/firestore'
import { useCollection } from 'vuefire'

import {
  allBarsRef,
  barsRef,
  libraryBarsRef,
  popularBarsRef,
  publicBarsRef,
  userBarsRef,
} from './firebase'

// console.log('Synchronizing bar_info and user_bars collections...')
// synchronizeBars()

async function tryGettingBarOwnerId(id) {
  const barDoc = await getDoc(doc(barsRef, id))
  const barData = barDoc.data()
  return barData?.owner?.id
}

async function synchronizeBars() {
  // console.log(new Date().toUTCString() + ' Read-> synchronizeBars')

  const q = query(barsRef)
  const barsCollection = useCollection(q, { once: true })

  // console.log('barsCollection', barsCollection)

  const docs = await barsCollection.promise.value
  // console.log('docs', docs)

  const barsByUserIdObject = {}
  const publicBarsObject = {}
  const libraryBarsObject = {}
  const popularBarsObject = {}
  const idsOfBarsWithoutOwner = []
  for await (const doc of docs) {
    const {
      id,
      owner,
      name,
      description,
      tags = [],
      icon = '',
      destination = '',
      createdAt = new Date(),
      isPublic = false,
      isInLibrary = false,
      isPopular = false,
    } = doc
    if (!doc?.owner?.id) {
      console.log('bar without owner', doc)
      idsOfBarsWithoutOwner.push(id)
      const ownerId = await tryGettingBarOwnerId(id)
      if (ownerId) {
        barsByUserIdObject[ownerId] = deleteField()
      }
      publicBarsObject[id] = deleteField()
      libraryBarsObject[id] = deleteField()
      popularBarsObject[id] = deleteField()
    } else {
      const barInfo = {
        name,
        description,
        tags,
        icon,
        destination,
        createdAt,
      }

      if (!barsByUserIdObject[owner.id]) {
        barsByUserIdObject[owner.id] = {}
      }
      barsByUserIdObject[owner.id][id] = { ...barInfo }

      if (isPublic) {
        if (!publicBarsObject[id]) {
          publicBarsObject[id] = {}
        }
        publicBarsObject[id] = { ...barInfo }
      }

      if (isInLibrary) {
        if (!libraryBarsObject[id]) {
          libraryBarsObject[id] = {}
        }
        libraryBarsObject[id] = { ...barInfo }
      }

      if (isPopular) {
        if (!popularBarsObject[id]) {
          popularBarsObject[id] = {}
        }
        popularBarsObject[id] = { ...barInfo }
      }
    }
  }

  // console.log('barsByUserIdObject', barsByUserIdObject)
  // console.log('publicBarsObject', publicBarsObject)
  // console.log('libraryBarsObject', libraryBarsObject)
  // console.log('popularBarsObject', popularBarsObject)

  await setAllBars(barsByUserIdObject)
  await setUserBars(barsByUserIdObject)
  await setPublicBars(publicBarsObject)
  await setLibraryBars(libraryBarsObject)
  await setPopularBars(popularBarsObject)

  if (idsOfBarsWithoutOwner.length > 0) {
    await removeBarsWithoutOwner(idsOfBarsWithoutOwner)
  }
}

async function setAllBars(docsObject) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> setAllBars')

    await setDoc(allBarsRef, docsObject, { merge: true })
  } catch (err) {
    console.log("Can't set all bars: ", err)
  }
}

async function setPopularBars(docsObject) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> setPopularBars')

    await setDoc(popularBarsRef, docsObject, { merge: true })
  } catch (err) {
    console.log("Can't set popular bars: ", err)
  }
}

async function setPublicBars(docsObject) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> setPublicBars')

    await setDoc(publicBarsRef, docsObject, { merge: true })
  } catch (err) {
    console.log("Can't set public bars: ", err)
  }
}

async function setLibraryBars(docsObject) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> setLibraryBars')

    await setDoc(libraryBarsRef, docsObject, { merge: true })
  } catch (err) {
    console.log("Can't set library bars: ", err)
  }
}

async function setUserBars(docsObject) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> setUserBars')

    for await (const userId of Object.keys(docsObject)) {
      const userBarRef = doc(userBarsRef, userId)
      if (docsObject[userId]._methodName === 'deleteField') {
        console.log('Removing user_bar object formissing user', userId)
        await deleteDoc(userBarRef)
      } else {
        await setDoc(userBarRef, docsObject[userId])
      }
    }
  } catch (err) {
    console.log("Can't set user bars: ", err)
  }
}

async function removeBarsWithoutOwner(idsOfBarsWithoutOwner) {
  try {
    // console.log(new Date().toUTCString() + ' Write-> removeBarsWithoutOwner')

    for await (const barId of idsOfBarsWithoutOwner) {
      await deleteDoc(doc(barsRef, barId))
    }
  } catch (err) {
    console.log("Can't remove bars without owner: ", err)
  }
}
