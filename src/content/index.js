import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import {
  arrayUnion,
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  where,
} from 'firebase/firestore'
import { createApp } from 'vue'
import VueSocialSharing from 'vue3-social-sharing'
import { VueFire, VueFireAuth } from 'vuefire'

import './style.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'

import en from '../lang/en.json'
import fr from '../lang/fr.json'
import pl from '../lang/pl.json'
import App from './App.vue'
import chatApp from './chatApp.vue'
import MenuHideControl from './components/MenuHideControl.vue'
import { customersRef, firebaseApp, usersRef } from './firebase'
import { setToLocalStorage, setToPluginStorageLocal } from './localStorage'
import { chatSite } from './sites_index.js'
import { getGACookie, supportedUrls } from './utils.js'

console.info('chrome-ext template-vue-js content script')

// ============================================================================
// EXTERNAL TEXT FIELD OPERATIONS
// ============================================================================

// Function to find external text fields on the current page
const findExternalTextFields = () => {
  const promptAreas = Array.isArray(chatSite.selector.textarea)
    ? chatSite.selector.textarea
    : [chatSite.selector.textarea]
  if (chatSite.selectorAlters) {
    const altPromptAreas = Array.isArray(chatSite.selectorAlters.textarea)
      ? chatSite.selectorAlters.textarea
      : [chatSite.selectorAlters.textarea]
    promptAreas.push(...altPromptAreas)
  }
  const selectors = [
    ...promptAreas,
    'textarea:not(.ai-prompt-textarea):not([data-ai-component])',
    'input[type="text"]:not(.ai-prompt-textarea):not([data-ai-component])',
    'input[type="email"]:not(.ai-prompt-textarea):not([data-ai-component])',
    'input[type="search"]:not(.ai-prompt-textarea):not([data-ai-component])',
    '[contenteditable="true"]:not(.ai-prompt-textarea):not([data-ai-component])',
  ]

  const allElements = []
  selectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    allElements.push(...Array.from(elements))
  })

  // Filter out elements that might be part of extensions or hidden
  return allElements.filter((el) => {
    // Check if element is visible
    const style = window.getComputedStyle(el)
    if (style.display === 'none' || style.visibility === 'hidden') {
      return false
    }

    // Check if element is not part of browser UI or other extensions
    const rect = el.getBoundingClientRect()
    if (rect.width === 0 || rect.height === 0) {
      return false
    }

    return true
  })
}

// Function to get the currently focused text field
const getFocusedTextField = () => {
  const activeElement = document.activeElement

  if (!activeElement) return null

  // Check if it's a text input element
  if (
    activeElement.tagName === 'TEXTAREA' ||
    (activeElement.tagName === 'INPUT' &&
      ['text', 'email', 'search', 'url', 'tel'].includes(activeElement.type)) ||
    activeElement.isContentEditable
  ) {
    // Make sure it's not part of an extension
    if (
      activeElement.closest('[data-ai-component]') ||
      activeElement.classList.contains('ai-prompt-textarea')
    ) {
      return null
    }

    return activeElement
  }

  return null
}

// Function to get the target element for insertion (focused or first available)
const getTargetInsertElement = () => {
  // First try focused element
  const focusedField = getFocusedTextField()
  if (focusedField) {
    return focusedField
  }

  // Then try first available field
  const fields = findExternalTextFields()
  return fields.length > 0 ? fields[0] : null
}

// Function to highlight target element with border
const highlightTargetElement = (element) => {
  if (!element) return null

  // Create unique identifier for this highlight
  const highlightId = 'ai-prompt-lab-highlight-' + Date.now()

  // Store original styles
  const originalStyles = {
    outline: element.style.outline,
    outlineOffset: element.style.outlineOffset,
    transition: element.style.transition,
    boxShadow: element.style.boxShadow,
  }

  // Apply highlight styles
  element.style.transition = 'all 0.2s ease'
  element.style.outline = '2px solid #43A047'
  element.style.outlineOffset = '2px'
  element.style.boxShadow = '0 0 0 4px rgba(67, 160, 71, 0.2)'

  // Store highlight data
  element.setAttribute('data-ai-highlight-id', highlightId)

  return {
    highlightId,
    element,
    originalStyles,
    remove: () => {
      // Restore original styles
      element.style.outline = originalStyles.outline
      element.style.outlineOffset = originalStyles.outlineOffset
      element.style.transition = originalStyles.transition
      element.style.boxShadow = originalStyles.boxShadow
      element.removeAttribute('data-ai-highlight-id')
    },
  }
}

// Function to remove all highlights
const removeAllHighlights = () => {
  const highlightedElements = document.querySelectorAll('[data-ai-highlight-id]')
  highlightedElements.forEach((element) => {
    element.style.outline = ''
    element.style.outlineOffset = ''
    element.style.transition = ''
    element.style.boxShadow = ''
    element.removeAttribute('data-ai-highlight-id')
  })
}

// Function to insert text at cursor position
const insertTextAtCursor = (element, textToInsert) => {
  if (!element || !textToInsert) {
    return { success: false, error: 'Missing element or text' }
  }

  try {
    // Focus the element first
    element.focus()

    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
      // For textarea and input elements
      const start = element.selectionStart || 0
      const end = element.selectionEnd || 0
      const currentValue = element.value || ''

      // Insert text at cursor position
      const newValue = currentValue.substring(0, start) + textToInsert + currentValue.substring(end)

      // Set the new value
      element.value = newValue

      // Set cursor position after inserted text
      const newCursorPosition = start + textToInsert.length
      element.setSelectionRange(newCursorPosition, newCursorPosition)
    } else if (element.isContentEditable) {
      // For contenteditable elements
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        range.deleteContents()

        // Create text node and insert
        const textNode = document.createTextNode(textToInsert)
        range.insertNode(textNode)

        // Move cursor after inserted text
        range.setStartAfter(textNode)
        range.setEndAfter(textNode)
        selection.removeAllRanges()
        selection.addRange(range)
      } else {
        // Fallback: append to end
        element.textContent = (element.textContent || '') + textToInsert
      }
    }

    // Trigger input and change events for reactivity
    const inputEvent = new Event('input', { bubbles: true })
    element.dispatchEvent(inputEvent)

    const changeEvent = new Event('change', { bubbles: true })
    element.dispatchEvent(changeEvent)

    return {
      success: true,
      elementInfo: {
        tagName: element.tagName,
        type: element.type || 'contenteditable',
        id: element.id,
        className: element.className,
      },
    }
  } catch (error) {
    console.error('Error inserting text at cursor:', error)
    return { success: false, error: error.message }
  }
}

// ============================================================================
// CHROME EXTENSION MESSAGE HANDLING
// ============================================================================

// Listen for messages from the extension (side panel via background script)
if (typeof chrome !== 'undefined' && chrome.runtime) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
      case 'insertTextIntoFocusedField':
        const focusedField = getFocusedTextField()

        if (focusedField) {
          const result = insertTextAtCursor(focusedField, message.text)
          sendResponse({
            success: result.success,
            error: result.error,
            elementInfo: result.elementInfo,
            insertedIntoExternal: true,
          })
        } else {
          const availableFields = findExternalTextFields()
          if (availableFields.length > 0) {
            const firstField = availableFields[0]
            const result = insertTextAtCursor(firstField, message.text)
            sendResponse({
              success: result.success,
              error: result.error,
              elementInfo: result.elementInfo,
              insertedIntoExternal: true,
            })
          } else {
            sendResponse({
              success: false,
              error: 'No focused text field found',
              insertedIntoExternal: false,
            })
          }
        }
        return true // Keep message channel open for async response

      case 'findAvailableTextFields':
        const availableFields = findExternalTextFields()

        sendResponse({
          success: true,
          fieldsCount: availableFields.length,
          fields: availableFields.map((field) => ({
            tagName: field.tagName,
            type: field.type || 'contenteditable',
            id: field.id,
            className: field.className,
            placeholder: field.placeholder || field.getAttribute('aria-label') || '',
          })),
        })
        return true

      case 'insertTextIntoFirstAvailableField':
        const fields = findExternalTextFields()

        if (fields.length > 0) {
          const firstField = fields[0]
          const result = insertTextAtCursor(firstField, message.text)
          sendResponse({
            success: result.success,
            error: result.error,
            elementInfo: result.elementInfo,
            insertedIntoExternal: true,
          })
        } else {
          sendResponse({
            success: false,
            error: 'No text fields found on page',
            insertedIntoExternal: false,
          })
        }
        return true

      case 'highlightTargetElement':
        const highlightAvailableFields = findExternalTextFields()

        const highlightFocusedField = getFocusedTextField()

        const targetElement = getTargetInsertElement()

        if (targetElement) {
          const highlight = highlightTargetElement(targetElement)

          sendResponse({
            success: true,
            highlightId: highlight.highlightId,
            elementInfo: {
              tagName: targetElement.tagName,
              type: targetElement.type || 'contenteditable',
              id: targetElement.id,
              className: targetElement.className,
            },
          })
        } else {
          sendResponse({
            success: false,
            error: 'No target element found',
            debug: {
              availableFieldsCount: highlightAvailableFields.length,
              focusedField: !!highlightFocusedField,
            },
          })
        }
        return true

      case 'removeHighlight':
        removeAllHighlights()
        sendResponse({ success: true })
        return true

      case 'insertTextIntoTargetElement':
        const insertTargetElement = getTargetInsertElement()

        if (insertTargetElement) {
          const insertResult = insertTextAtCursor(insertTargetElement, message.text)

          sendResponse({
            success: insertResult.success,
            error: insertResult.error,
            elementInfo: insertResult.elementInfo,
            insertedIntoExternal: true,
          })
        } else {
          sendResponse({
            success: false,
            error: 'No target element found for insertion',
            insertedIntoExternal: false,
          })
        }
        return true

      case 'getCurrentTheme':
        // Existing theme detection logic
        const isDarkTheme =
          document.documentElement.classList.contains('dark') ||
          document.body.classList.contains('dark') ||
          window.matchMedia('(prefers-color-scheme: dark)').matches
        sendResponse({ isDarkTheme })
        return true

      case 'getTheme':
        // Existing theme detection logic
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        sendResponse({ theme: prefersDark ? 'dark' : 'light' })
        return true

      default:
        return false
    }
  })
}

function customRule(choice, choicesLength) {
  if (choice === 0) {
    return 0
  }
  if (choice === 1) {
    return 1
  }
  const teen = choice > 10 && choice < 20

  if (!teen && choice % 10 >= 2 && choice % 10 <= 4) {
    return 2
  }

  return choicesLength < 4 ? 2 : 3
}

const localeObj = new Intl.Locale(navigator.language)
const locale = localStorage.getItem('lang') || localeObj.language || 'en'

const i18n = createI18n({
  locale,
  pluralizationRules: {
    pl: customRule,
  },
  messages: {
    en,
    pl,
    fr,
  },
})

async function savePaymentIdAndTrackAmount() {
  const urlParams = new URLSearchParams(window.location.search)
  const email = urlParams.get('email')

  const customerQuery = query(customersRef, where('email', '==', email), limit(1))
  const customerQuerySnapshot = await getDocs(customerQuery)
  const customerDoc = customerQuerySnapshot.docs[0]

  const paymentsRef = collection(customerDoc.ref, 'payments')
  const paymentQuery = query(
    paymentsRef,
    where('status', '==', 'succeeded'),
    orderBy('created', 'desc'),
    limit(1),
  )
  const paymentsQuerySnapshot = await getDocs(paymentQuery)
  const paymentDoc = paymentsQuerySnapshot.docs[0]
  const amount = paymentDoc.data().amount_received / 100

  const userQuery = query(usersRef, where('email', '==', email), limit(1))
  const userQuerySnapshot = await getDocs(userQuery)
  const userDoc = userQuerySnapshot.docs[0]

  if (!userDoc || userDoc.data().subscription?.paymentIds?.includes(paymentDoc.id)) {
    return
  }

  await setDoc(
    userDoc.ref,
    { subscription: { paymentIds: arrayUnion(paymentDoc.id) } },
    { merge: true },
  )

  window.postMessage(amount.toString())
}

let location = window.location.href
if (location.indexOf('ai-promptlab.com/') > 0) {
  document.documentElement.classList.add('plugin', 'aipromptlab-installed')

  setToLocalStorage(
    'supportedUrls',
    supportedUrls.map((ai) => ai.url),
  )

  if (location.indexOf('ai-promptlab.com/buy') > 0) {
    savePaymentIdAndTrackAmount()
  }
  if (
    location.indexOf('ai-promptlab.com/app') > 0 ||
    location.indexOf('ai-promptlab.com/share') > 0
  ) {
    const params = new URLSearchParams(window.location.search)
    if (params.size > 0) {
      let shareURL = params.get('share')
      let barId = params.get('barId')
      let barName = params.get('barName')
      let promptId = params.get('promptId')
      let promptName = params.get('promptName')
      let promo = params.get('promo')
      if (barId) {
        setToPluginStorageLocal('sharedBar', barId)
      }
      if (barId && promptId) {
        setToPluginStorageLocal('sharedPrompt', promptId)
      }
      if (promo) {
        setToPluginStorageLocal('promo', promo)
      }
    }
    const gaCookie = getGACookie()
    setToPluginStorageLocal('gaCookie', gaCookie)
  }
} else {
  let app = null
  let appChat = null
  let appControl = null

  // Create a single Pinia instance to be shared
  const pinia = createPinia() // <-- CREATE PINIA INSTANCE HERE, ONCE

  if (chatSite?.selector?.main) {
    setInterval(function () {
      let chatMainSelector = document.querySelector(chatSite.selector.main)
      if (!chatMainSelector && chatSite.selectorAlters && chatSite.selectorAlters.main) {
        for (let i = 0; i < chatSite.selectorAlters.main.length; i++) {
          chatMainSelector = document.querySelector(chatSite.selectorAlters.main[i])
          if (chatMainSelector) break
        }
      }

      // if (!chatMainSelector && chatSite.selectorShadows && chatSite.selectorShadows.main) {
      //   let rootElement = document
      //   for (let i = 0; i < chatSite.selectorShadows.main.length; i++) {
      //     chatMainSelector = rootElement.querySelector(chatSite.selectorShadows.main[i])
      //     if (chatMainSelector) {
      //       rootElement = chatMainSelector.shadowRoot
      //     } else {
      //       break
      //     }
      //   }
      // }

      let appElem = chatMainSelector.querySelector('#_appAIPM')
      if (appElem) {
        // appElem.classList.add('gpt')
        // clearInterval(checkApp)
      } else {
        if (!document.body.classList.contains(chatSite.name + '-body')) {
          document.body.classList.add(chatSite.name + '-body')
        }
        const currentPath = window.location.pathname,
          isSubpage = currentPath !== '/'
        if (isSubpage && !document.body.classList.contains('subpage')) {
          document.body.classList.add('subpage')
        }

        if (document.querySelector('#_appAIPM')) {
          destroyApp()
        }
        if (document.querySelector('#_controlPanel')) {
          destroyControlPanel()
        }
        createNewApp(chatMainSelector)
      }

      // Create control panel if it doesn't exist and we have main selector
      if (!document.querySelector('#_controlPanel') && chatMainSelector && !appControl) {
        createControlPanel()
      }
    }, 1000)

    // if (chatSite.name !== 'Bing' && chatSite.name !== 'Copilot') {
    setInterval(function () {
      let appBtn = document.querySelector('#_chatButton')
      let textArea = document.querySelector(chatSite.selector.textarea)
      if (appBtn && appBtn != null && textArea.classList.contains('chat-prompt-textarea')) {
        // appElem.classList.add('gpt')
        // clearInterval(checkApp)
      } else {
        destroyChatApp()
        createChatButton()
      }
    }, 500)
  }
  // }

  function createNewApp(selectorMain) {
    let insertPosition = 'afterbegin'
    let appClass = chatSite.name

    let el = selectorMain
    if (selectorMain) {
      if (chatSite.name == 'Manus') {
        el = selectorMain.querySelector('style')
      }
    }
    if (el) {
      if (chatSite.name == 'ChatGPT') {
        el.classList.add('flex-col')
      } else if (chatSite.name == 'Manus') {
        insertPosition = 'afterend'
      }
      // if (chatSite.name == 'Bing' || chatSite.name == 'Copilot') insertPosition = 'beforeend'
      if (!window.location.pathname.includes('gpts/editor/')) {
        el.insertAdjacentHTML(insertPosition, '<div id="_appAIPM" class="' + appClass + '"></div>')
      }

      app = createApp(App)

      // Use the shared Pinia instance
      app.use(pinia) // <-- Use the shared instance

      app.use(VueFire, {
        firebaseApp,
        modules: [VueFireAuth()],
      })
      app.use(ElementPlus)
      app.use(i18n)
      app.use(VueSocialSharing)
      // use element-plus icons
      for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component)
      }
      app.mount(selectorMain.querySelector('#_appAIPM'))
    }
  }

  function destroyApp() {
    const appElem = document.getElementById('_appAIPM')
    if (!appElem) return
    app.unmount()
    if (appElem) {
      appElem.remove()
    }
  }

  function createControlPanel() {
    document.body.insertAdjacentHTML('beforeend', '<div id="_controlPanel"></div>')

    appControl = createApp(MenuHideControl)
    appControl.use(pinia)
    appControl.use(VueFire, {
      firebaseApp,
      modules: [VueFireAuth()],
    })
    appControl.use(ElementPlus)
    appControl.use(i18n)
    appControl.use(VueSocialSharing)
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      appControl.component(key, component)
    }
    appControl.mount('#_controlPanel')
  }

  function destroyControlPanel() {
    const controlPanel = document.getElementById('_controlPanel')
    if (!controlPanel) return
    appControl.unmount()
    if (controlPanel) {
      controlPanel.remove()
    }
  }

  function createChatButton() {
    let insertPosition = 'afterbegin'
    const textArea = document.querySelector(chatSite.selector.textarea)
    // const textAreas = document.querySelectorAll(chatSite.selector.textarea)
    if (textArea) {
      if (textArea.classList.contains('chat-prompt-textarea')) return
      textArea.classList.add('chat-prompt-textarea')
      let chatButtonTarget = textArea.parentElement

      if (chatSite.name == 'ChatGPT') {
        if (window.location.pathname.includes('/project')) {
          chatButtonTarget = textArea.closest('form').parentElement
          insertPosition = 'beforebegin'
        } else {
          chatButtonTarget = textArea.closest('form')
        }
      }
      if (chatSite.name == 'Bard') {
        chatButtonTarget = textArea.closest('.input-area-container')
      }
      if (chatSite.name == 'Perplexity')
        chatButtonTarget =
          textArea.parentElement.parentElement.parentElement.parentElement.parentElement
      // chatButtonTarget.classList.add('flex-col')

      if (chatSite.name == 'Copilot') {
        chatButtonTarget = textArea.closest('div[data-testid=composer-content]').parentElement
          .parentElement
      }

      if (chatSite.name == 'Vespa') {
        chatButtonTarget = textArea.parentElement.parentElement.parentElement.parentElement
      }

      if (chatSite.name == 'Claude') {
        chatButtonTarget = textArea.closest('fieldset')
        // insertPosition = 'beforeend'
      }

      if (chatSite.name == 'Ideogram') {
        chatButtonTarget =
          textArea.closest('.MuiBox-root').parentElement.parentElement.parentElement
      }

      if (chatSite.name == 'AIStudio') {
        const msPromptWrapper = textArea.closest('ms-prompt-input-wrapper')
        if (msPromptWrapper) {
          chatButtonTarget = msPromptWrapper
          insertPosition = 'afterbegin'
        } else {
          chatButtonTarget = textArea.closest('footer') || textArea.parentElement?.parentElement
          if (!chatButtonTarget) {
            console.warn('Could not find suitable container for AIStudio chat button')
            return
          }
        }
      }

      if (chatSite.name == 'DeepSeek') {
        chatButtonTarget = textArea.parentElement?.parentElement?.parentElement?.parentElement
        if (!chatButtonTarget) {
          console.warn('Could not find suitable container for DeepSeek chat button')
          return
        }
      }

      if (chatSite.name == 'Qwen') {
        // First try to find the new container
        const inputContainer = document.querySelector('.chat-message-input-container.in-chat')
        if (inputContainer) {
          chatButtonTarget = inputContainer
          insertPosition = 'afterbegin' // or 'beforeend' if preferred at bottom
        } else {
          // fallback to old logic
          const recommendBar = document.querySelector('.chat-message-input-recommend-container')
          if (recommendBar) {
            chatButtonTarget = recommendBar.parentElement
          } else {
            chatButtonTarget = textArea.closest('div.chat-container')
          }
        }
      }

      if (chatSite.name == 'You') {
        chatButtonTarget =
          textArea.closest('#ChatQueryBar').parentElement.parentElement.parentElement
      }

      if (chatSite.name == 'Grok') {
        chatButtonTarget = textArea.closest('form').parentElement.parentElement
      }

      if (chatSite.name == 'Bolt') {
        let zPrompt = textArea.closest('.z-prompt')
        if (!zPrompt) {
          zPrompt = document.querySelector('.z-prompt.sticky.bottom-0')
        }
        if (zPrompt) {
          chatButtonTarget = zPrompt
          insertPosition = 'afterbegin'
        } else {
          chatButtonTarget = textArea.parentElement
          insertPosition = 'beforeend'
        }
      }

      if (chatSite.name == 'Lovable') {
        insertPosition = 'beforebegin'
        chatButtonTarget = textArea.closest('form')
      }

      if (chatSite.name == 'Mistral') {
        chatButtonTarget = textArea.closest('form')
      }

      if (chatSite.name == 'Manus') {
        chatButtonTarget = textArea.parentElement.parentElement.parentElement
      }

      if (chatSite.name == 'Genspark') {
        chatButtonTarget = textArea.closest(
          '.search-input-and-toggle > div, .input-wrapper-wrapper',
        )
      }

      if (chatSite.name == 'Freepik') {
        chatButtonTarget = textArea.parentElement.parentElement.parentElement.parentElement

        const editable = document.querySelector(
          'div[data-tour="mode-selector"] div[contenteditable="true"]',
        )
        if (editable) {
          const observer = new MutationObserver(() => {
            const inputEvent = new Event('input', { bubbles: true })
            editable.dispatchEvent(inputEvent)
            const changeEvent = new Event('change', { bubbles: true })
            editable.dispatchEvent(changeEvent)
            const keyupEvent = new KeyboardEvent('keyup', { bubbles: true, key: 'c' })
            editable.dispatchEvent(keyupEvent)
          })
          observer.observe(editable, { childList: true, characterData: true, subtree: true })
        }
      }

      if (chatSite.name == 'Jules') {
        chatButtonTarget = textArea.parentElement.parentElement.parentElement
      }

      if (chatSite.name == 'NotebookLM') {
        chatButtonTarget = textArea.closest('omnibar')
      }

      if (chatSite.name == 'Canva') {
        chatButtonTarget = textArea.closest('form')
      }

      const appClass = chatSite.name

      chatButtonTarget.insertAdjacentHTML(
        insertPosition,
        `<div id="_chatButton" class="${appClass}"></div>`,
      )

      // Apply saved visibility state immediately to prevent flicker
      const getSiteIdentifier = () => {
        return window.location.hostname.replace(/[^a-zA-Z0-9.-]/g, '_')
      }

      const currentSite = getSiteIdentifier()
      const stateKey = `menuControlState_${currentSite}`
      const savedState = localStorage.getItem(stateKey)

      if (savedState) {
        try {
          const state = JSON.parse(savedState)
          const chatButtonVisible = state.chatButtonVisible ?? true
          const chatElement = document.getElementById('_chatButton')
          if (chatElement) {
            chatElement.style.display = chatButtonVisible ? 'block' : 'none'
          }
        } catch (e) {
          console.warn('Failed to load chat button visibility state:', e)
        }
      }

      appChat = createApp(chatApp)

      // Use the shared Pinia instance
      appChat.use(pinia) // <-- Use the shared instance

      appChat.use(VueFire, {
        firebaseApp,
        modules: [VueFireAuth()],
      })
      appChat.use(ElementPlus)
      appChat.use(i18n)
      appChat.use(VueSocialSharing)
      // use element-plus icons
      for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        appChat.component(key, component)
      }
      appChat.mount('#_chatButton')
    }
  }

  function destroyChatApp() {
    const chatButton = document.getElementById('_chatButton')
    if (!chatButton) return
    appChat.unmount()
    if (chatButton) {
      let textAreasWithClass = document.querySelectorAll('.chat-prompt-textarea')
      textAreasWithClass.forEach((ta) => {
        ta.classList.remove('chat-prompt-textarea')
      })
      chatButton.remove()
    }
  }

  document.querySelector('#go-to-options')?.addEventListener('click', function () {
    if (chrome.runtime.openOptionsPage) {
      chrome.runtime.openOptionsPage()
    } else {
      window.open(chrome.runtime.getURL('options.html'))
    }
  })

  // set dark theme - universal detection for all sites
  setInterval(function () {
    const isDarkMode =
      document.body.classList.contains('dark-theme') ||
      document.body.classList.contains('b_dark') ||
      document.body.classList.contains('dark') ||
      document.body.getAttribute('style')?.includes('background-color: rgb(24,') ||
      document.documentElement.classList.contains('dark') ||
      document.documentElement.getAttribute('data-mode') === 'dark' ||
      document.documentElement.getAttribute('data-theme') === 'dark' ||
      document.documentElement.getAttribute('data-color-scheme') === 'dark' ||
      document.documentElement.getAttribute('data-color-scheme') === '@dark' ||
      document.documentElement.getAttribute('data-ds-dark-theme') === 'dark' ||
      document.documentElement.getAttribute('theme') === 'dark' ||
      document.documentElement.getAttribute('data-color-mode') === 'dark' ||
      document.documentElement.getAttribute('style')?.includes('dark') ||
      (['Genspark'].includes(chatSite.name) &&
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches)

    if (isDarkMode) {
      document.getElementsByTagName('html')[0].classList.add('dark')
    } else {
      document.getElementsByTagName('html')[0].classList.remove('dark')
    }
  }, 500)

  const sitesWithForcedDarkMode = ['Jules']

  if (sitesWithForcedDarkMode.includes(chatSite.name)) {
    document.getElementsByTagName('html')[0].classList.add('dark')
  }
}
