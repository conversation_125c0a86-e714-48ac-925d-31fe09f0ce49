import { useChangeCase } from '@vueuse/integrations/useChangeCase'

import { removeFromPluginStorageLocal } from './localStorage'

export const aiStorageName = 'aiResultStorage'

export const supportedUrls = [
  { url: 'https://chatgpt.com', name: 'ChatGPT' },
  { url: 'https://gemini.google.com', name: '<PERSON>' },
  { url: 'https://copilot.microsoft.com', name: 'Copilot' },
  { url: 'https://perplexity.ai', name: 'Perplexity' },
  { url: 'https://claude.ai', name: '<PERSON>' },
  { url: 'https://vespa-engine-colpali-vespa-visual-retrieval.hf.space', name: 'Ves<PERSON>' },
  { url: 'https://ideogram.ai', name: 'Ideogram' },
  { url: 'https://lmarena.ai', name: '<PERSON><PERSON><PERSON>' },
  { url: 'https://aistudio.google.com', name: 'AIStudio' },
  { url: 'https://chat.deepseek.com', name: 'DeepSeek' },
  { url: 'https://chat.qwen.ai', name: 'Qwen' },
  { url: 'https://you.com', name: 'You' },
  { url: 'https://grok.com', name: 'Grok' },
  { url: 'https://bolt.new', name: 'Bolt' },
  { url: 'https://lovable.dev', name: 'Lovable' },
  { url: 'https://chat.mistral.ai/chat', name: 'Mistral' },
  { url: 'https://manus.im', name: 'Manus' },
  { url: 'https://www.genspark.ai', name: 'Genspark' },
  { url: 'https://www.freepik.com', name: 'Freepik' },
  { url: 'https://jules.google.com', name: 'Jules' },
  { url: 'https://notebooklm.google.com', name: 'NotebookLM' },
  { url: 'https://www.canva.com', name: 'Canva' },
]

export const supportedUrlsForManifest = [
  'https://ai-promptlab.com/*',
  'https://v0.dev/*',
  'https://perplexity.ai/*',
  'https://bolt.new/~/*',
  ...supportedUrls.flatMap((ai) => [
    `${ai.url}/*`,
    `${ai.url.replace('https://', 'https://www.')}/*`,
  ]),
]

export const MAX_FREE_PROMPT_BARS = 5

export const MAX_CREDITS = {
  FREE: 10,
  STANDARD: 200,
  PREMIUM: 1000,
}

export function getMaxCreditsByPlan(planName) {
  switch (planName) {
    case 'Standard':
      return MAX_CREDITS.STANDARD
    case 'Premium':
    case 'Team':
      return MAX_CREDITS.PREMIUM
    default:
      return MAX_CREDITS.FREE
  }
}

export function clearAiStorage() {
  try {
    removeFromPluginStorageLocal(aiStorageName)
  } catch (error) {
    console.error('Error while clearing AI storage:', error)
  }
}

function diffInMonths(end, start) {
  if (!end || !start) return 0
  const endDate = end instanceof Date ? end : end.toDate()
  const startDate = start instanceof Date ? start : start.toDate()
  const timeDiff = Math.abs(endDate.getTime() - startDate.getTime())
  return timeDiff / (1000 * 3600 * 24 * 30.44)
}

export async function getCreditsResetDate(userDocData, userHasSubscription) {
  if (!userDocData?.createdAt) return new Date()

  let creditsResetDate = new Date()
  const userCreationDate = userDocData.createdAt.toDate()

  if (userHasSubscription) {
    const subscriptionStartDate =
      (await getSubscriptionMonthStartDate(userDocData)) || userCreationDate
    const monthsDiff = diffInMonths(new Date(), subscriptionStartDate)
    creditsResetDate = new Date(subscriptionStartDate)
    creditsResetDate.setMonth(creditsResetDate.getMonth() + Math.floor(monthsDiff))
  } else {
    const monthsDiff = diffInMonths(new Date(), userCreationDate)
    creditsResetDate = new Date(userCreationDate)
    creditsResetDate.setMonth(creditsResetDate.getMonth() + Math.floor(monthsDiff))
  }
  return creditsResetDate
}

async function getSubscriptionMonthStartDate(newUserDoc) {
  if (!newUserDoc?.email) return null
  console.log('getSubscriptionMonthStartDate called for email:', newUserDoc.email)
  return null
}

export function convertIdToString(key, value) {
  if (key === 'id' && typeof value === 'number') {
    return value.toString()
  }
  return value
}

export const TABS = {
  // None: '',
  MyPrompts: 'My Prompts',
  Favorites: 'Favorites',
  Team: 'Team',
  Popular: 'Popular',
  Public: 'Public',
  Library: 'Library',
  // All: 'All',
  // Company: 'Company',
}

export function cc(text, caseType = 'camelCase') {
  const changeCase = useChangeCase(text, caseType)
  return changeCase.value
}

export const SHARE_LINK_BASE = 'https://ai-promptlab.com/share/'

export function generatePromptBarShareLink({ id, name, destination }) {
  let redirectLink = destination
  if (!redirectLink) {
    const isGPT = window.location.href.includes('chatgpt.com/g/')
    redirectLink = window.location.origin + (isGPT ? window.location.pathname : '')
  }
  return encodeURI(
    `https://ai-promptlab.com/app/?share=${redirectLink}&barId=${id}&barName=${name}`,
  )
}
export function generatePromptShareLink(bar, prompt, destinationLink) {
  let redirectLink = destinationLink
  if (!redirectLink) {
    const isGPT = window.location.href.includes('chatgpt.com/g/')
    redirectLink = window.location.origin + (isGPT ? window.location.pathname : '')
  }
  return encodeURI(
    `https://ai-promptlab.com/app/?share=${redirectLink}&barId=${bar.id}&barName=${bar.name}&promptId=${prompt.id}&promptName=${prompt.label}`,
  )
}
export function generatePromptRedirectLink(destinationLink) {
  return encodeURI(`${destinationLink}`)
}

export function getGACookie() {
  const gaCookie = document.cookie.split('; ').find((cookie) => cookie.startsWith('_ga='))

  if (!gaCookie) {
    return ''
  }

  return gaCookie.split('=')[1] || ''
}

export async function createSharePage(
  barId,
  barName,
  doUpdate = false,
  destination = '',
  promptId = '',
  promptName = '',
  description = '',
  barTags = '',
) {
  const createSharePageBase = SHARE_LINK_BASE + 'create/index.php'

  const hash = encodeURIComponent(promptId ? promptId : barId)
  const jsonData = JSON.stringify({
    barId,
    barName,
    ...(destination && { destination }),
    ...(promptId && { promptId }),
    ...(promptName && { promptName }),
    ...(description && { description }),
    ...(barTags && { tags: barTags }),
    ...(doUpdate && { update: 'true' }),
  })

  try {
    const response = await fetch(createSharePageBase, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: jsonData,
    })

    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status}`)
    }

    const contentType = response.headers.get('content-type')
    let responseData

    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json()
    } else {
      responseData = await response.text()
    }

    return {
      success: responseData.success,
      imageUrl: responseData.imageUrl,
      shareLink: responseData.shareUrl,
    }
  } catch (error) {
    console.error('Error creating share page:', error)
    return {
      success: false,
      error: error.message,
    }
  }
}

export async function deleteShared(arrayOfIds) {
  const deleteSharedBase = SHARE_LINK_BASE + 'delete/index.php'
  const jsonData = JSON.stringify({
    ids: arrayOfIds,
  })

  try {
    const response = await fetch(deleteSharedBase, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: jsonData,
    })

    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status}`)
    }

    const responseData = await response.json()

    return {
      success: true,
      deleted: responseData.deleted || [],
      errors: responseData.errors || [],
      deletedCount: responseData.deletedCount || 0,
    }
  } catch (error) {
    console.error('Error deleting shared:', error)
    return {
      success: false,
      error: error.message,
      deleted: [],
      errors: [],
      deletedCount: 0,
    }
  }
}

export function processPromptFields(item) {
  const selectRegex = /\[\[(.*?)\]\]/g
  const inputRegex = /{{(.*?)}}/g
  const textareaRegex = /\(\((.*?)\)\)/g

  let processedPrompt = []
  let tagPositions = []

  const addFields = () => {
    const addField = (regex, type) => {
      let match
      while ((match = regex.exec(item.prompt)) !== null) {
        const [fullMatch, placeholder] = match
        let field
        if (type === 'select') {
          const options = placeholder
            .split(',')
            .map((option) => ({ label: option.trim(), value: option.trim() }))
          field = { type: 'select', options, value: options[0].value }
        } else if (type === 'input') {
          field = { type: 'input', placeholder: placeholder.trim(), value: '' }
        } else if (type === 'textarea') {
          field = { type: 'textarea', placeholder: placeholder.trim(), value: '' }
        }
        processedPrompt.push(field)
        tagPositions.push({ start: match.index, end: match.index + fullMatch.length, field })
      }
    }

    addField(selectRegex, 'select')
    addField(inputRegex, 'input')
    addField(textareaRegex, 'textarea')

    tagPositions.sort((a, b) => a.start - b.start)
  }

  const removeOriginalTags = () => {
    if (!item.prompt) return []

    let lastIndex = 0
    let finalPrompt = []

    tagPositions.forEach(({ start, end, field }) => {
      if (start > lastIndex) {
        const textContent = item.prompt.slice(lastIndex, start)
        const textParts = textContent.split('\n')
        textParts.forEach((part, index) => {
          if (part) finalPrompt.push({ type: 'text', content: part })
          if (index < textParts.length - 1) finalPrompt.push({ type: 'linebreak' })
        })
      }
      finalPrompt.push(field)
      lastIndex = end
    })

    if (lastIndex < item.prompt.length) {
      const textContent = item.prompt.slice(lastIndex)
      const textParts = textContent.split('\n')
      textParts.forEach((part, index) => {
        if (part) finalPrompt.push({ type: 'text', content: part })
        if (index < textParts.length - 1) finalPrompt.push({ type: 'linebreak' })
      })
    }
    return finalPrompt
  }

  addFields()
  return removeOriginalTags()
}

export function getPromptWithProcessedParts(prompt) {
  let finalPrompt = ''
  prompt.forEach((part) => {
    if (part.type === 'text') {
      finalPrompt += part.content
    } else if (part.type === 'select') {
      finalPrompt += part.value || ''
    } else if (part.type === 'input' || part.type === 'textarea') {
      finalPrompt += part.value || part.placeholder || ''
    } else if (part.type === 'linebreak') {
      finalPrompt += '\n'
    }
  })
  return finalPrompt
}
