import { ref } from 'vue'

import { getFromLocalStorage, setToLocalStorage } from '../localStorage'

export function useMenuHideLevel() {
  const menuHideLevel = ref(getFromLocalStorage('menuHideLevel') || 0)

  function changeMenuHideLevel(step) {
    if (menuHideLevel.value + step < 0 || menuHideLevel.value + step > 2) return
    menuHideLevel.value += step
    setToLocalStorage('menuHideLevel', menuHideLevel.value)
  }

  return { menuHideLevel, changeMenuHideLevel }
}
