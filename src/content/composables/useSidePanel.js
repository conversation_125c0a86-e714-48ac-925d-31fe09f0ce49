import { ref } from 'vue'

export function useSidePanel(state) {
  const { sidePanelVisible, saveVisibilityState } = state

  let isCheckingState = false

  const toggleChromePanel = async () => {
    try {
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.error('Chrome API nie jest dostępne')
        return
      }

      const currentState = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ action: 'getSidePanelStatus' }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error getting side panel status:', chrome.runtime.lastError)
            resolve({ isOpen: sidePanelVisible.value })
          } else {
            resolve(response || { isOpen: false })
          }
        })
      })

      if (sidePanelVisible.value !== currentState.isOpen) {
        sidePanelVisible.value = currentState.isOpen
        saveVisibilityState()
      }

      const action = currentState.isOpen ? 'closeSidePanel' : 'openSidePanel'
      const expectedNewState = !currentState.isOpen

      chrome.runtime.sendMessage({ action }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Runtime error:', chrome.runtime.lastError)
          return
        }

        if (response && response.success) {
          if (sidePanelVisible.value !== expectedNewState) {
            sidePanelVisible.value = expectedNewState
            saveVisibilityState()
          }
        } else {
          console.error('Background script error:', response)
        }
      })
    } catch (error) {
      console.error('Error in toggleChromePanel:', error)
    }
  }

  const checkSidePanelStatus = () => {
    if (isCheckingState) return

    if (typeof chrome !== 'undefined' && chrome.runtime) {
      isCheckingState = true

      chrome.runtime.sendMessage({ action: 'getSidePanelStatus' }, (response) => {
        isCheckingState = false

        if (response && typeof response.isOpen === 'boolean') {
          if (sidePanelVisible.value !== response.isOpen) {
            sidePanelVisible.value = response.isOpen
            saveVisibilityState()
          }
        }
      })
    }
  }

  const setupSidePanelListener = () => {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'sidePanelStateChanged') {
          if (sidePanelVisible.value !== message.isOpen) {
            sidePanelVisible.value = message.isOpen
            saveVisibilityState()
          }

          sendResponse({ received: true, currentState: sidePanelVisible.value })
        }

        return true
      })
    }
  }

  const startStatusPolling = () => {
    setTimeout(() => {
      checkSidePanelStatus()
    }, 1000)

    return () => {}
  }

  return {
    toggleChromePanel,
    checkSidePanelStatus,
    setupSidePanelListener,
    startStatusPolling,
  }
}
