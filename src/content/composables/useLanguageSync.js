import { useI18n } from 'vue-i18n'

function isSidePanel() {
  return window.location.pathname.includes('side_panel.html')
}

export function useLanguageSync() {
  const { locale } = useI18n()

  function setLanguage(lang) {
    locale.value = lang
    localStorage.setItem('lang', lang)
    if (window.chrome?.runtime?.sendMessage) {
      try {
        if (isSidePanel()) {
          chrome.runtime.sendMessage({ type: 'LANGUAGE_CHANGED', lang, source: 'side_panel' })
        } else {
          chrome.runtime.sendMessage({ type: 'LANGUAGE_CHANGED', lang, source: 'content' })
        }
      } catch (e) {}
    }
  }

  function listenForLanguageChange() {
    if (window.chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
        if (msg?.type === 'LANGUAGE_CHANGED' && msg.lang) {
          if (
            (isSidePanel() && msg.source === 'side_panel') ||
            (!isSidePanel() && msg.source === 'content')
          ) {
            return
          }
          locale.value = msg.lang
          localStorage.setItem('lang', msg.lang)
        }
      })
    }

    if (isSidePanel() && window.chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
        if (msg?.type === 'LANGUAGE_CHANGED_BROADCAST' && msg.lang) {
          locale.value = msg.lang
          localStorage.setItem('lang', msg.lang)
        }
      })
    }
  }

  return { setLanguage, listenForLanguageChange }
}
