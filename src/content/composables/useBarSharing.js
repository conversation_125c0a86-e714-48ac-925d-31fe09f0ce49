import { storeToRefs } from 'pinia'
import { ref } from 'vue'

import { useAppStateStore } from '../../stores/appState'

export function useBarSharing() {
  const appState = useAppStateStore()
  const { sharePrompt } = storeToRefs(appState)

  const isPromptBarPublic = ref(undefined)
  const isPromptBarListed = ref(undefined)
  const isPromptBarEditable = ref(undefined)

  // Dialog informacyjny
  const isInformationDialogOpen = ref(false)
  const informationDialogLinkItem = ref('')

  const openInformationDialog = (data) => {
    isInformationDialogOpen.value = true
    informationDialogLinkItem.value = data
  }

  const closeInformationDialog = () => {
    isInformationDialogOpen.value = false
  }

  const openSharePromptBarDialog = (returnFromBarIsPublicEdit) => {
    if (returnFromBarIsPublicEdit) {
      appState.closeEditPromptBarDialog()
    }
    sharePrompt.value = ''
    appState.openSharePromptBarDialog()
  }

  const closeSharePromptBarDialog = () => {
    appState.closeSharePromptBarDialog()
    isPromptBarPublic.value = undefined
    isPromptBarListed.value = undefined
    isPromptBarEditable.value = undefined
  }

  const changeIsPublic = (isPublic) => {
    isPromptBarPublic.value = isPublic
    appState.openEditPromptBarDialog()
  }

  const changeIsListed = (isListed) => {
    isPromptBarListed.value = isListed
    appState.openEditPromptBarDialog()
  }

  const changeIsEditable = (isEditable) => {
    isPromptBarEditable.value = isEditable
    appState.openEditPromptBarDialog()
  }

  const updateBars = (bar, property) => {
    if (!bar || !property) return

    appState.updateBarsAfterChange(bar, property)
  }

  return {
    isPromptBarPublic,
    isPromptBarListed,
    isPromptBarEditable,
    isInformationDialogOpen,
    informationDialogLinkItem,
    openInformationDialog,
    closeInformationDialog,
    openSharePromptBarDialog,
    closeSharePromptBarDialog,
    changeIsPublic,
    changeIsListed,
    changeIsEditable,
    updateBars,
  }
}
