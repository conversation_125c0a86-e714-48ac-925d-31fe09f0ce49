import { getAuth, onAuthStateChanged } from 'firebase/auth'
import { doc, getDoc, onSnapshot, setDoc } from 'firebase/firestore'
import { computed, ref, watch } from 'vue'

import { db, settingsRef } from '../firebase.js'

const auth = getAuth()

const sitesWithTags = ref([])
const allTags = ref([])
const isLoading = ref(false)
const isAuthenticated = ref(false)
const currentUser = ref(null)
const isUpdatingFromFirebase = ref(false)
const previousDataHash = ref('')

// Local storage fallback key
const LOCAL_STORAGE_SITES_KEY = 'ai-sites-data'

const loadFromLocalStorage = () => {
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_SITES_KEY)
    if (stored) {
      const data = JSON.parse(stored)
      sitesWithTags.value = data.sitesWithTags || []
      allTags.value = data.allTags || []
    }
  } catch (error) {
    console.warn('Error loading AI sites from localStorage:', error)
    sitesWithTags.value = []
    allTags.value = []
  }
}

const saveToLocalStorage = () => {
  try {
    const data = {
      sitesWithTags: sitesWithTags.value,
      allTags: allTags.value,
      lastUpdated: new Date().toISOString(),
    }
    localStorage.setItem(LOCAL_STORAGE_SITES_KEY, JSON.stringify(data))
  } catch (error) {
    console.warn('Error saving AI sites to localStorage:', error)
  }
}

const generateDataHash = () => {
  return JSON.stringify({
    sitesWithTags: sitesWithTags.value,
    allTags: allTags.value,
  })
}

const saveToFirebase = async (forceUpdate = false) => {
  if (!isAuthenticated.value || !currentUser.value) {
    saveToLocalStorage()
    return
  }

  // Check if data actually changed
  const currentDataHash = generateDataHash()
  if (!forceUpdate && currentDataHash === previousDataHash.value) {
    return
  }

  try {
    const sitesDocRef = doc(settingsRef, 'sites')

    await setDoc(sitesDocRef, {
      sitesWithTags: sitesWithTags.value,
      allTags: allTags.value,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
    })

    // Update the hash after successful save
    previousDataHash.value = currentDataHash

    saveToLocalStorage()
  } catch (error) {
    console.error('💾 Error saving AI sites to Firebase:', error)
    // Fallback to localStorage
    saveToLocalStorage()
  }
}

const loadFromFirebase = async () => {
  if (!isAuthenticated.value || !currentUser.value) {
    loadFromLocalStorage()
    return
  }

  try {
    isLoading.value = true
    const sitesDocRef = doc(settingsRef, 'sites')
    const docSnap = await getDoc(sitesDocRef)

    if (docSnap.exists()) {
      const data = docSnap.data()
      isUpdatingFromFirebase.value = true
      sitesWithTags.value = data.sitesWithTags || []
      allTags.value = data.allTags || []
      // Update the hash after loading from Firebase
      previousDataHash.value = generateDataHash()
      isUpdatingFromFirebase.value = false

      saveToLocalStorage()
    } else {
      console.warn('⚠️ No AI sites data found in Firebase, loading from localStorage')
      loadFromLocalStorage()
      // Set initial hash for localStorage data
      previousDataHash.value = generateDataHash()
    }
  } catch (error) {
    console.error('❌ Error loading AI sites from Firebase:', error)
    loadFromLocalStorage()
    // Set initial hash for localStorage data
    previousDataHash.value = generateDataHash()
  } finally {
    isLoading.value = false
  }
}

// Setup real-time listener for Firebase changes
const setupFirebaseListener = () => {
  if (!isAuthenticated.value || !currentUser.value) return

  const sitesDocRef = doc(settingsRef, 'sites')

  return onSnapshot(
    sitesDocRef,
    (doc) => {
      if (doc.exists()) {
        const data = doc.data()
        if (data.sitesWithTags && data.allTags) {
          isUpdatingFromFirebase.value = true
          sitesWithTags.value = data.sitesWithTags
          allTags.value = data.allTags
          // Update the hash after Firebase listener update
          previousDataHash.value = generateDataHash()
          isUpdatingFromFirebase.value = false
          saveToLocalStorage()
        }
      }
    },
    (error) => {
      console.error('Firebase listener error for AI sites:', error)
    },
  )
}

// Authentication state management
onAuthStateChanged(auth, (user) => {
  currentUser.value = user
  isAuthenticated.value = !!user

  if (user) {
    loadFromFirebase()
    setupFirebaseListener()
  } else {
    loadFromLocalStorage()
  }
})

// Watch for changes and auto-save
watch(
  [sitesWithTags, allTags],
  () => {
    // Don't save if we're updating from Firebase to prevent loops
    if (!isUpdatingFromFirebase.value) {
      saveToFirebase()
    }
  },
  { deep: true },
)

// CRUD operations for sites
const addSite = (siteData) => {
  const newSite = {
    id: siteData.id || `site_${Date.now()}`,
    name: siteData.name,
    url: siteData.url,
    tags: siteData.tags || [],
    ...siteData,
  }

  sitesWithTags.value.push(newSite)

  // Add new tags to allTags if they don't exist
  if (newSite.tags) {
    newSite.tags.forEach((tag) => {
      if (!allTags.value.includes(tag)) {
        allTags.value.push(tag)
      }
    })
    allTags.value.sort()
  }
}

const updateSite = (originalSite, updatedData) => {
  const index = sitesWithTags.value.findIndex((site) => site.id === originalSite.id)
  if (index !== -1) {
    sitesWithTags.value[index] = { ...originalSite, ...updatedData }

    // Update allTags if new tags were added
    if (updatedData.tags) {
      updatedData.tags.forEach((tag) => {
        if (!allTags.value.includes(tag)) {
          allTags.value.push(tag)
        }
      })
      allTags.value.sort()
    }
  }
}

const removeSite = (site) => {
  const index = sitesWithTags.value.findIndex((s) => s.id === site.id)
  if (index !== -1) {
    sitesWithTags.value.splice(index, 1)

    // Clean up unused tags
    cleanupUnusedTags()
  }
}

// Tag management operations
const addTag = (tagName) => {
  if (tagName && !allTags.value.includes(tagName)) {
    allTags.value.push(tagName)
    allTags.value.sort()
  }
}

const removeTag = (tagName) => {
  const index = allTags.value.indexOf(tagName)
  if (index !== -1) {
    allTags.value.splice(index, 1)

    // Remove tag from all sites
    sitesWithTags.value.forEach((site) => {
      if (site.tags) {
        site.tags = site.tags.filter((tag) => tag !== tagName)
      }
    })
  }
}

const cleanupUnusedTags = () => {
  const usedTags = new Set()
  sitesWithTags.value.forEach((site) => {
    if (site.tags) {
      site.tags.forEach((tag) => usedTags.add(tag))
    }
  })

  allTags.value = allTags.value.filter((tag) => usedTags.has(tag))
}

// Computed properties
const sitesCount = computed(() => sitesWithTags.value.length)
const tagsCount = computed(() => allTags.value.length)

export function useAISitesManagement() {
  return {
    // Data
    sitesWithTags: computed(() => sitesWithTags.value),
    allTags: computed(() => allTags.value),
    isLoading: computed(() => isLoading.value),
    isAuthenticated: computed(() => isAuthenticated.value),
    sitesCount,
    tagsCount,

    // Methods
    addSite,
    updateSite,
    removeSite,
    addTag,
    removeTag,
    cleanupUnusedTags,
    loadFromFirebase,
    saveToFirebase,
  }
}
