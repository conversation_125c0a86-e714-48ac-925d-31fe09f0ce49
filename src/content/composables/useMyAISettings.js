import { getAuth, onAuthStateChanged } from 'firebase/auth'
import { collection, doc, getDoc, onSnapshot, setDoc, updateDoc } from 'firebase/firestore'
import { computed, ref, watch } from 'vue'

import { db, settingsRef, usersRef } from '../firebase.js'

const auth = getAuth()

const myAIItems = ref([])
const isLoading = ref(false)
const isAuthenticated = ref(false)
const currentUser = ref(null)
const isUpdatingFromFirebase = ref(false)
const previousDataHash = ref('')

// Local storage fallback key
const LOCAL_STORAGE_KEY = 'ai-tools-myai'

const loadFromLocalStorage = () => {
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_KEY)
    if (stored) {
      myAIItems.value = JSON.parse(stored)
    }
  } catch (error) {
    console.warn('Błąd wczytywania My AI z localStorage:', error)
    myAIItems.value = []
  }
}

const saveToLocalStorage = () => {
  try {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(myAIItems.value))
  } catch (error) {
    console.warn('Błąd zapisywania My AI do localStorage:', error)
  }
}

const generateDataHash = () => {
  return JSON.stringify(myAIItems.value)
}

const saveToFirebase = async (forceUpdate = false) => {
  if (!isAuthenticated.value || !currentUser.value) {
    saveToLocalStorage()
    return
  }

  // Check if data actually changed
  const currentDataHash = generateDataHash()
  if (!forceUpdate && currentDataHash === previousDataHash.value) {
    return
  }

  try {
    const userRef = doc(usersRef, currentUser.value.uid)

    await setDoc(
      userRef,
      {
        myai: myAIItems.value,
        myaiUpdatedAt: new Date().toISOString(),
      },
      { merge: true },
    )

    // Update the hash after successful save
    previousDataHash.value = currentDataHash
  } catch (error) {
    console.error('💾 Error saving My AI to Firebase users collection:', error)
    console.error('💾 User ID:', currentUser.value?.uid)
    console.error('💾 Items count:', myAIItems.value.length)
    // Fallback to localStorage
    saveToLocalStorage()
  }
}

const loadFromFirebase = async () => {
  if (!isAuthenticated.value || !currentUser.value) {
    loadFromLocalStorage()
    return
  }

  try {
    isLoading.value = true

    // First, try to load from the new users collection
    const userRef = doc(usersRef, currentUser.value.uid)
    const userSnap = await getDoc(userRef)

    let dataLoaded = false

    if (userSnap.exists()) {
      const userData = userSnap.data()
      if (userData.myai && Array.isArray(userData.myai)) {
        isUpdatingFromFirebase.value = true
        myAIItems.value = userData.myai
        // Update the hash after loading from Firebase
        previousDataHash.value = generateDataHash()
        isUpdatingFromFirebase.value = false
        saveToLocalStorage()

        dataLoaded = true
      } else {
      }
    } else {
    }

    // If no data in users collection, check the old settings collection for migration
    if (!dataLoaded) {
      const oldSettingsRef = doc(settingsRef, currentUser.value.uid)
      const oldSettingsSnap = await getDoc(oldSettingsRef)

      if (oldSettingsSnap.exists()) {
        const oldData = oldSettingsSnap.data()
        if (oldData.myai && Array.isArray(oldData.myai)) {
          isUpdatingFromFirebase.value = true
          myAIItems.value = oldData.myai
          // Update the hash after migration
          previousDataHash.value = generateDataHash()
          isUpdatingFromFirebase.value = false

          // Save to new location (users collection) - force update for migration
          await saveToFirebase(true)

          // Save to localStorage as backup
          saveToLocalStorage()

          dataLoaded = true

          // Note: We're not removing the old data immediately to ensure safe migration
          // The old data can be cleaned up in a future update after confirming migration success
        } else {
        }
      } else {
      }
    }

    // If still no data found, load from localStorage and save to Firebase if there's data
    if (!dataLoaded) {
      loadFromLocalStorage()
      // Set initial hash for localStorage data
      previousDataHash.value = generateDataHash()
      if (myAIItems.value.length > 0) {
        await saveToFirebase(true) // Force update for initial sync
      }
    }
  } catch (error) {
    console.error('❌ Error loading My AI from Firebase:', error)
    // Fallback to localStorage
    loadFromLocalStorage()
    // Set initial hash for localStorage data
    previousDataHash.value = generateDataHash()
  } finally {
    isLoading.value = false
  }
}

// Setup real-time listener for Firebase changes
const setupFirebaseListener = () => {
  if (!isAuthenticated.value || !currentUser.value) return

  const userRef = doc(usersRef, currentUser.value.uid)

  return onSnapshot(
    userRef,
    (doc) => {
      if (doc.exists()) {
        const data = doc.data()
        if (data.myai && Array.isArray(data.myai)) {
          // Check if data actually changed (more comprehensive than just count)
          const newDataHash = JSON.stringify(data.myai)
          if (newDataHash !== previousDataHash.value) {
            isUpdatingFromFirebase.value = true
            myAIItems.value = data.myai
            previousDataHash.value = newDataHash
            isUpdatingFromFirebase.value = false
            saveToLocalStorage()
          }
        }
      }
    },
    (error) => {
      console.error('Firebase users collection listener error:', error)
    },
  )
}

// Auth state listener
onAuthStateChanged(auth, (user) => {
  if (user) {
    isAuthenticated.value = true
    currentUser.value = user
    loadFromFirebase()
    setupFirebaseListener()
  } else {
    isAuthenticated.value = false
    currentUser.value = null
    loadFromLocalStorage()
  }
})

// Watch for changes and auto-save
watch(
  myAIItems,
  (newValue, oldValue) => {
    // Don't save if we're updating from Firebase to prevent loops
    if (!isUpdatingFromFirebase.value) {
      if (isAuthenticated.value) {
        saveToFirebase()
      } else {
        saveToLocalStorage()
      }
    }
  },
  { deep: true },
)

// Initialize
loadFromLocalStorage()
// Set initial hash
previousDataHash.value = generateDataHash()

export function useMyAISettings() {
  const addToMyAI = (site) => {
    // Zawsze dodaj - pozwól na duplikaty
    myAIItems.value.push({
      id: Date.now() + Math.random(), // unikalny ID dla duplikatów
      url: site.url,
      name: site.name,
      description: site.description || '',
      tags: site.tags || [],
      dateAdded: new Date().toISOString(),
    })
  }

  const removeFromMyAI = (identifier) => {
    // Jeśli identifier to object z id, usuń po id
    // Jeśli to string, usuń po url (dla kompatybilności wstecznej)
    let index = -1

    if (typeof identifier === 'object' && identifier.id) {
      index = myAIItems.value.findIndex((item) => item.id === identifier.id)
    } else {
      const url = typeof identifier === 'string' ? identifier : identifier.url
      index = myAIItems.value.findIndex((item) => item.url === url)
    }

    if (index !== -1) {
      myAIItems.value.splice(index, 1)
    } else {
      console.warn('🗑️ Item not found for removal')
    }
  }

  const updateMyAIItem = (originalItem, updatedSite) => {
    // Znajdź po ID jeśli dostępne, jeśli nie to po URL
    let index = -1

    if (typeof originalItem === 'object' && originalItem.id) {
      index = myAIItems.value.findIndex((item) => item.id === originalItem.id)
    } else {
      // originalItem może być URL (string) - dla kompatybilności wstecznej
      const originalUrl = typeof originalItem === 'string' ? originalItem : originalItem.url
      index = myAIItems.value.findIndex((item) => item.url === originalUrl)
    }

    if (index !== -1) {
      myAIItems.value[index] = {
        ...myAIItems.value[index],
        ...updatedSite,
        dateAdded: myAIItems.value[index].dateAdded, // preserve original date
      }
    }
  }

  const reorderMyAI = (fromIndex, toIndex) => {
    const item = myAIItems.value.splice(fromIndex, 1)[0]
    myAIItems.value.splice(toIndex, 0, item)
  }

  const isMyAIItem = (url) => {
    return myAIItems.value.some((item) => item.url === url)
  }

  const clearMyAI = () => {
    myAIItems.value = []
  }

  const myAIList = computed(() => [...myAIItems.value])
  const myAICount = computed(() => myAIItems.value.length)

  return {
    myAI: myAIList,
    myAICount,
    isLoading,
    isAuthenticated,
    addToMyAI,
    removeFromMyAI,
    updateMyAIItem,
    reorderMyAI,
    isMyAIItem,
    clearMyAI,
    loadFromFirebase,
    saveToFirebase,
  }
}
