import { computed, ref, shallowRef } from 'vue'

/**
 * Optimized component management composable
 * Handles component visibility, ordering, and rendering optimization
 */
export function useComponentManagement() {
  // Use shallowRef for better performance since we replace the entire array
  const components = shallowRef([
    { id: 'app', name: 'House', label: 'APP', visible: true, order: 0 },
    { id: 'aitools', name: 'Robot', label: 'AI Tools', visible: true, order: 1 },
    { id: 'textfield', name: 'Edit', label: 'Text Field', visible: true, order: 2 },
    { id: 'sitesgrid', name: 'Grid', label: 'Sites Grid', visible: true, order: 3 },
  ])

  // Memoized sorted components - only recalculates when components array changes
  const sortedComponents = computed(() => {
    return [...components.value].sort((a, b) => a.order - b.order)
  })

  // Get visible components only - optimized for rendering
  const visibleComponents = computed(() => {
    return sortedComponents.value.filter((component) => component.visible)
  })

  // Component lookup map for O(1) access
  const componentMap = computed(() => {
    const map = new Map()
    components.value.forEach((component) => {
      map.set(component.id, component)
    })
    return map
  })

  // Check if a specific component is visible
  const isComponentVisible = (componentId) => {
    const component = componentMap.value.get(componentId)
    return component?.visible ?? false
  }

  // Get component by ID
  const getComponent = (componentId) => {
    return componentMap.value.get(componentId)
  }

  // Update components array (called from ComponentControlBar)
  const updateComponents = (updatedComponents) => {
    // Validate the updated components
    if (!Array.isArray(updatedComponents)) {
      console.warn('Component management: invalid components array provided')
      return
    }

    // Ensure all required properties exist
    const validComponents = updatedComponents.filter((component) => {
      return (
        component &&
        typeof component.id === 'string' &&
        typeof component.visible === 'boolean' &&
        typeof component.order === 'number'
      )
    })

    if (validComponents.length !== updatedComponents.length) {
      console.warn('Component management: some components were invalid and filtered out')
    }

    // Update the components array
    components.value = validComponents
  }

  // Toggle component visibility
  const toggleComponentVisibility = (componentId) => {
    const component = getComponent(componentId)
    if (component) {
      // Create new array to trigger reactivity
      const newComponents = components.value.map((comp) =>
        comp.id === componentId ? { ...comp, visible: !comp.visible } : comp,
      )
      components.value = newComponents
    }
  }

  // Reorder components
  const reorderComponents = (fromIndex, toIndex) => {
    if (
      fromIndex === toIndex ||
      fromIndex < 0 ||
      toIndex < 0 ||
      fromIndex >= components.value.length ||
      toIndex >= components.value.length
    ) {
      return
    }

    const newComponents = [...components.value]
    const [movedComponent] = newComponents.splice(fromIndex, 1)
    newComponents.splice(toIndex, 0, movedComponent)

    // Update order property
    newComponents.forEach((component, index) => {
      component.order = index
    })

    components.value = newComponents
  }

  // Reset to default components
  const resetComponents = () => {
    components.value = [
      { id: 'app', name: 'House', label: 'APP', visible: true, order: 0 },
      { id: 'aitools', name: 'Robot', label: 'AI Tools', visible: true, order: 1 },
      { id: 'textfield', name: 'Edit', label: 'Text Field', visible: true, order: 2 },
      { id: 'sitesgrid', name: 'Grid', label: 'Sites Grid', visible: true, order: 3 },
    ]
  }

  // Get component statistics
  const getComponentStats = () => {
    const total = components.value.length
    const visible = components.value.filter((c) => c.visible).length
    const hidden = total - visible

    return {
      total,
      visible,
      hidden,
      visibilityRate: total > 0 ? (visible / total) * 100 : 0,
    }
  }

  return {
    // State
    components,

    // Computed
    sortedComponents,
    visibleComponents,
    componentMap,

    // Component queries
    isComponentVisible,
    getComponent,
    getComponentStats,

    // Component mutations
    updateComponents,
    toggleComponentVisibility,
    reorderComponents,
    resetComponents,
  }
}
