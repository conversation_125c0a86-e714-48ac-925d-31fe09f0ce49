import { storeToRefs } from 'pinia'

import { useAppStateStore } from '../../stores/appState'
import { TABS } from '../utils.js'

export function useBarOptions() {
  const appState = useAppStateStore()
  const { activeTab, barSortDirection, selectedBar, selectedBarId } = storeToRefs(appState)

  const getBarsByTabName = (tabName) => {
    return appState.getBarsForTab(tabName)
  }

  const getBarsForCurrentTab = () => {
    return appState.getBarsForCurrentTab()
  }

  const sortBarsByField = (fieldName) => {
    appState.sortBarsByField(fieldName)
  }

  const barSortFunctionsByField = {
    name: (a, b) =>
      a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }) *
      barSortDirection.value,
    createdAt: (a, b) =>
      ((a.createdAt?.seconds || 0) > (b.createdAt?.seconds || 0) ? 1 : -1) * barSortDirection.value,
    destination: (a, b) => {
      if (!a.destination && !b.destination) return 0
      if (!a.destination) return 1
      if (!b.destination) return -1
      return (
        a.destination.localeCompare(b.destination, undefined, {
          numeric: true,
          sensitivity: 'base',
        }) * barSortDirection.value
      )
    },
  }

  const areBarsLoadedByTabName = (tabName) => {
    switch (tabName) {
      case TABS.Popular:
        return appState.arePopularBarsLoaded
      case TABS.Public:
        return appState.arePublicBarsLoaded
      case TABS.MyPrompts:
        return appState.areMyPromptBarsLoaded
      case TABS.Favorites:
        return appState.areAllBarsLoaded
      case TABS.Library:
        return appState.areLibraryBarsLoaded
      case TABS.Team:
        return appState.areTeamBarsLoaded
      case TABS.All:
        return appState.areAllBarsLoaded
      default:
        return false
    }
  }

  const areBarsLoadedForCurrentTab = () => {
    return areBarsLoadedByTabName(activeTab.value)
  }

  const getLabelForBar = (bar, shouldOmitOwner = false) => {
    // For the currently selected bar, use selectedBar data which has resolved owner reference
    // For other bars in dropdown, use bar data (which may not have owner info for team_bars)
    const isCurrentlySelectedBar = bar.id === selectedBarId.value
    const barToUse = isCurrentlySelectedBar && selectedBar.value ? selectedBar.value : bar

    return `${bar.name}${
      !shouldOmitOwner && barToUse?.owner?.name && activeTab.value !== TABS.Popular
        ? ` (by ${barToUse.owner.name})`
        : ''
    }`
  }

  return {
    getBarsByTabName,
    getBarsForCurrentTab,
    sortBarsByField,
    barSortFunctionsByField,
    areBarsLoadedByTabName,
    areBarsLoadedForCurrentTab,
    getLabelForBar,
  }
}
