import { nextTick, ref, watch } from 'vue'

/**
 * Composable for component state persistence
 * Handles both Chrome extension storage and localStorage with fallback mechanisms
 * Provides automatic save/restore, validation, and reset functionality
 */
export function useComponentStatePersistence(componentId, defaultState, options = {}) {
  const {
    debounceMs = 500,
    validateData = null,
    transformOnLoad = null,
    transformOnSave = null,
    enableFirestore = false,
    firestoreHandler = null,
  } = options

  // Storage keys
  const CHROME_STORAGE_KEY = `component_state_${componentId}`
  const LOCAL_STORAGE_KEY = `component_state_${componentId}`
  const STORAGE_VERSION_KEY = `${CHROME_STORAGE_KEY}_version`
  const CURRENT_VERSION = '1.0.0'

  // State
  const isLoading = ref(true)
  const hasError = ref(false)
  const errorMessage = ref('')
  const lastSaved = ref(null)
  const storageType = ref('none') // 'chrome', 'localStorage', 'firestore', 'none'

  // Minimum display time for loading state (prevents flickering)
  const MIN_LOADING_TIME = 500
  const loadingStartTime = Date.now()

  /**
   * Check if Chrome extension storage is available
   */
  const isChromeStorageAvailable = () => {
    return (
      typeof chrome !== 'undefined' &&
      chrome.storage &&
      chrome.storage.local &&
      typeof chrome.storage.local.get === 'function'
    )
  }

  /**
   * Check if localStorage is available
   */
  const isLocalStorageAvailable = () => {
    try {
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch (e) {
      return false
    }
  }

  /**
   * Validate data structure
   */
  const isValidData = (data) => {
    if (!data || typeof data !== 'object') return false

    // Use custom validator if provided
    if (validateData && typeof validateData === 'function') {
      return validateData(data)
    }

    // Basic validation - check if data has expected structure
    return data.hasOwnProperty('version') && data.hasOwnProperty('state')
  }

  /**
   * Create storage wrapper with version and metadata
   */
  const createStorageWrapper = (state) => {
    const transformedState = transformOnSave ? transformOnSave(state) : state
    return {
      version: CURRENT_VERSION,
      state: transformedState,
      savedAt: new Date().toISOString(),
      componentId,
    }
  }

  /**
   * Extract state from storage wrapper
   */
  const extractStateFromWrapper = (wrapper) => {
    if (!wrapper || !wrapper.state) return null
    return transformOnLoad ? transformOnLoad(wrapper.state) : wrapper.state
  }

  /**
   * Save to Chrome storage
   */
  const saveToChromeStorage = async (data) => {
    return new Promise((resolve, reject) => {
      try {
        const storageData = {
          [CHROME_STORAGE_KEY]: data,
          [STORAGE_VERSION_KEY]: CURRENT_VERSION,
        }

        chrome.storage.local.set(storageData, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome storage error: ${chrome.runtime.lastError.message}`))
          } else {
            resolve()
          }
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * Load from Chrome storage
   */
  const loadFromChromeStorage = async () => {
    return new Promise((resolve, reject) => {
      try {
        chrome.storage.local.get([CHROME_STORAGE_KEY, STORAGE_VERSION_KEY], (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome storage error: ${chrome.runtime.lastError.message}`))
          } else {
            resolve(result[CHROME_STORAGE_KEY] || null)
          }
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * Save to localStorage
   */
  const saveToLocalStorage = async (data) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data))
      localStorage.setItem(`${LOCAL_STORAGE_KEY}_version`, CURRENT_VERSION)
    } catch (error) {
      throw new Error(`localStorage error: ${error.message}`)
    }
  }

  /**
   * Load from localStorage
   */
  const loadFromLocalStorage = async () => {
    try {
      const data = localStorage.getItem(LOCAL_STORAGE_KEY)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn(`Failed to load from localStorage: ${error.message}`)
      return null
    }
  }

  /**
   * Clear Chrome storage
   */
  const clearChromeStorage = async () => {
    if (!isChromeStorageAvailable()) return

    return new Promise((resolve) => {
      chrome.storage.local.remove([CHROME_STORAGE_KEY, STORAGE_VERSION_KEY], () => {
        resolve()
      })
    })
  }

  /**
   * Clear localStorage
   */
  const clearLocalStorage = async () => {
    if (!isLocalStorageAvailable()) return

    try {
      localStorage.removeItem(LOCAL_STORAGE_KEY)
      localStorage.removeItem(`${LOCAL_STORAGE_KEY}_version`)
    } catch (error) {
      console.warn(`Failed to clear localStorage: ${error.message}`)
    }
  }

  /**
   * Save state with fallback mechanism
   */
  const saveState = async (state) => {
    const wrapper = createStorageWrapper(state)
    let saved = false
    let lastError = null

    // Check storage quota before saving
    await checkAndCleanupStorage()

    // Try Chrome storage first with retry
    if (isChromeStorageAvailable()) {
      try {
        await retryWithBackoff(() => saveToChromeStorage(wrapper))
        storageType.value = 'chrome'
        saved = true
      } catch (error) {
        console.warn(`Chrome storage save failed after retries: ${error.message}`)
        lastError = error
      }
    }

    // Fallback to localStorage with retry
    if (!saved && isLocalStorageAvailable()) {
      try {
        await retryWithBackoff(() => saveToLocalStorage(wrapper))
        storageType.value = 'localStorage'
        saved = true
      } catch (error) {
        console.warn(`localStorage save failed after retries: ${error.message}`)
        lastError = error
      }
    }

    // Try Firestore if enabled and available with retry
    if (!saved && enableFirestore && firestoreHandler) {
      try {
        await retryWithBackoff(() => firestoreHandler.save(wrapper))
        storageType.value = 'firestore'
        saved = true
      } catch (error) {
        console.warn(`Firestore save failed after retries: ${error.message}`)
        lastError = error
      }
    }

    if (saved) {
      lastSaved.value = new Date()
      hasError.value = false
      errorMessage.value = ''
    } else {
      hasError.value = true
      errorMessage.value = lastError?.message || 'Failed to save state'
      console.error(`Failed to save component state for ${componentId}:`, lastError)
    }

    return saved
  }

  /**
   * Load state with fallback mechanism
   */
  const loadState = async () => {
    let loadedData = null
    let loadedFrom = 'none'

    // Try Chrome storage first with retry
    if (isChromeStorageAvailable()) {
      try {
        const data = await retryWithBackoff(loadFromChromeStorage)
        const migratedData = migrateDataIfNeeded(data)
        if (migratedData && isValidData(migratedData)) {
          loadedData = migratedData
          loadedFrom = 'chrome'
        }
      } catch (error) {
        console.warn(`Chrome storage load failed after retries: ${error.message}`)
      }
    }

    // Fallback to localStorage with retry
    if (!loadedData && isLocalStorageAvailable()) {
      try {
        const data = await retryWithBackoff(loadFromLocalStorage)
        const migratedData = migrateDataIfNeeded(data)
        if (migratedData && isValidData(migratedData)) {
          loadedData = migratedData
          loadedFrom = 'localStorage'
        }
      } catch (error) {
        console.warn(`localStorage load failed after retries: ${error.message}`)
      }
    }

    // Try Firestore if enabled and available with retry
    if (!loadedData && enableFirestore && firestoreHandler) {
      try {
        const data = await retryWithBackoff(() => firestoreHandler.load())
        const migratedData = migrateDataIfNeeded(data)
        if (migratedData && isValidData(migratedData)) {
          loadedData = migratedData
          loadedFrom = 'firestore'
        }
      } catch (error) {
        console.warn(`Firestore load failed after retries: ${error.message}`)
      }
    }

    storageType.value = loadedFrom

    if (loadedData) {
      const extractedState = extractStateFromWrapper(loadedData)
      if (extractedState) {
        hasError.value = false
        errorMessage.value = ''

        // Check storage quota after successful load
        checkAndCleanupStorage()

        return extractedState
      }
    }

    // Return default state if nothing was loaded
    hasError.value = false
    errorMessage.value = ''
    return defaultState
  }

  /**
   * Reset to default state
   */
  const resetToDefaults = async () => {
    // Clear all storage types
    await Promise.all([
      clearChromeStorage(),
      clearLocalStorage(),
      enableFirestore && firestoreHandler?.clear ? firestoreHandler.clear() : Promise.resolve(),
    ])

    storageType.value = 'none'
    lastSaved.value = null
    hasError.value = false
    errorMessage.value = ''

    return defaultState
  }

  /**
   * Initialize persistence with minimum loading time
   */
  const initializePersistence = async () => {
    try {
      isLoading.value = true

      const loadedState = await loadState()

      // Ensure minimum loading time to prevent flickering
      const elapsedTime = Date.now() - loadingStartTime
      if (elapsedTime < MIN_LOADING_TIME) {
        await new Promise((resolve) => setTimeout(resolve, MIN_LOADING_TIME - elapsedTime))
      }

      return loadedState
    } catch (error) {
      console.error(`Failed to initialize persistence for ${componentId}:`, error)
      hasError.value = true
      errorMessage.value = error.message
      return defaultState
    } finally {
      await nextTick()
      isLoading.value = false
    }
  }

  /**
   * Create a debounced save function
   */
  let saveTimeout = null
  const debouncedSave = (state) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout)
    }
    saveTimeout = setTimeout(() => {
      saveState(state)
    }, debounceMs)
  }

  /**
   * Setup automatic persistence for a reactive state
   */
  const setupAutoPersistence = (reactiveState) => {
    watch(
      reactiveState,
      (newState) => {
        if (!isLoading.value) {
          debouncedSave(newState)
        }
      },
      { deep: true },
    )
  }

  /**
   * Retry failed operations with exponential backoff
   */
  const retryWithBackoff = async (operation, maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        if (attempt === maxRetries) {
          throw error
        }

        const delay = baseDelay * Math.pow(2, attempt - 1)
        console.warn(
          `Operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`,
          error.message,
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }
  }

  /**
   * Check storage quota and clean up if needed
   */
  const checkAndCleanupStorage = async () => {
    if (isChromeStorageAvailable()) {
      try {
        // Check Chrome storage usage
        const usage = await new Promise((resolve) => {
          chrome.storage.local.getBytesInUse(null, resolve)
        })

        // Chrome storage limit is typically 5MB
        const CHROME_STORAGE_LIMIT = 5 * 1024 * 1024
        if (usage > CHROME_STORAGE_LIMIT * 0.9) {
          console.warn('Chrome storage usage high, consider cleanup')
        }
      } catch (error) {
        console.warn('Failed to check Chrome storage usage:', error.message)
      }
    }

    if (isLocalStorageAvailable()) {
      try {
        // Estimate localStorage usage
        let totalSize = 0
        for (let key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            totalSize += localStorage[key].length + key.length
          }
        }

        // localStorage limit varies by browser, typically 5-10MB
        const LOCALSTORAGE_LIMIT = 5 * 1024 * 1024
        if (totalSize > LOCALSTORAGE_LIMIT * 0.9) {
          console.warn('localStorage usage high, consider cleanup')
        }
      } catch (error) {
        console.warn('Failed to check localStorage usage:', error.message)
      }
    }
  }

  /**
   * Validate and migrate data from older versions
   */
  const migrateDataIfNeeded = (data) => {
    if (!data || !data.version) {
      // Legacy data without version - attempt to migrate
      if (data && typeof data === 'object') {
        return {
          version: CURRENT_VERSION,
          state: data,
          savedAt: new Date().toISOString(),
          componentId,
        }
      }
      return null
    }

    // Future version migrations can be added here
    return data
  }

  return {
    // State
    isLoading,
    hasError,
    errorMessage,
    lastSaved,
    storageType,

    // Methods
    saveState,
    loadState,
    resetToDefaults,
    initializePersistence,
    setupAutoPersistence,
    debouncedSave,
    retryWithBackoff,
    checkAndCleanupStorage,
    migrateDataIfNeeded,

    // Utilities
    isChromeStorageAvailable,
    isLocalStorageAvailable,
  }
}
