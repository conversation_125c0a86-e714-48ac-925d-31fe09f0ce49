import { storeToRefs } from 'pinia'
import { computed } from 'vue'

import Analytics from '../../Analytics.js'
import { useAppStateStore } from '../../stores/appState'
import { ROLES } from '../firebase'
import { getFromLocalStorage } from '../localStorage'
import { TABS } from '../utils.js'
import { useBarFiltering } from './useBarFiltering'

export function useTabNavigation() {
  const appState = useAppStateStore()
  const { activeTab, userDoc, selectedBarId, selectedBar } = storeToRefs(appState)
  const { barOptions } = useBarFiltering()
  const getTabDescription = (tabName) => {
    switch (tabName) {
      case TABS.Popular:
        return 'Most popular prompts selected for best usability'
      case TABS.Public:
        return 'All public prompts from our community'
      case TABS.MyPrompts:
        return 'Custom prompts made by you'
      case TABS.Favorites:
        return 'Your favorite prompts'
      case TABS.Library:
        return 'Collection of thousands of prompts created by professional AI writers'
      case TABS.All:
        return 'All prompts only for admin users'
      default:
        return ''
    }
  }

  const availableTabs = computed(() => {
    const userRole = userDoc?.value?.role

    if (appState.isLoggedIn && userRole === ROLES.SUPER_ADMIN) {
      return TABS
    }

    const { All, ...tabs } = TABS
    return tabs
  })

  const atTabChange = (name) => {
    Analytics.fireEvent('tab_change', { name })
    const bar = getFromLocalStorage(name) || {}

    if (bar.id) {
      appState.updateActiveBarIdForTab(name, bar.id)
      selectedBarId.value = bar.id
      selectedBar.value = bar
    } else {
      appState.updateActiveBarIdForTab(name, null)
      selectedBarId.value = null
      selectedBar.value = {}
    }

    appState.setTabEmpty(false)
    barOptions.value = appState.getBarsForCurrentTab()
  }

  // Synchronizacja lokalnego stanu z Pinia store
  const handleTabChangeLogic = (name) => {
    appState.setActiveTab(name)
    atTabChange(name)
  }

  return {
    getTabDescription,
    availableTabs,
    atTabChange,
    handleTabChangeLogic,
  }
}
