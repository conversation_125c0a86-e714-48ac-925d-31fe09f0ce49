import { deleteDoc, doc, getDoc } from 'firebase/firestore'
import { storeToRefs } from 'pinia'
import { nextTick, ref } from 'vue'
import { useCurrentUser } from 'vuefire'

import Analytics from '../../Analytics.js'
import { useAppStateStore } from '../../stores/appState'
import {
  barsRef,
  syncBarWithLibraryBars,
  syncBarWithPopularBars,
  syncBarWithPublicBars,
  syncBarWithUserBars,
} from '../firebase'
import { getFromLocalStorage, removeFromLocalStorage } from '../localStorage'
import { convertIdToString, TABS } from '../utils.js'
import { useBarFiltering } from './useBarFiltering'

const setEditTab = ref()

export function usePromptBarManagement() {
  const appState = useAppStateStore()
  const {
    activeTab,
    selectedBarId,
    selectedBar,
    myPromptBars,
    textToCreatePrompt,
    promptBarLanguage,
    selectedLangInPopularDropdown,
    selectedLangInLibraryDropdown,
  } = storeToRefs(appState)
  const { updateBarOptions } = useBarFiltering()
  const user = useCurrentUser()

  const barIdToCopyFrom = ref()
  const barsTabName = ref()

  const initialEditUrl = ref('')

  function editPromptBar() {
    textToCreatePrompt.value = ''
    Analytics.fireEvent('bar_edit', {
      tab_name: activeTab.value,
      prompt_bar_id: selectedBarId.value,
    })
    openEditPromptBarDialog()
  }

  const openEditPromptBarDialog = (barId = undefined, editTab = undefined) => {
    barIdToCopyFrom.value = barId
    setEditTab.value = editTab
    barsTabName.value = undefined
    if (activeTab.value !== TABS.MyPrompts && barId) {
      barsTabName.value = activeTab.value
      appState.setActiveTab(TABS.MyPrompts)
      const barFromStorage = getFromLocalStorage(activeTab.value)
      selectedBar.value = barFromStorage || {}
    }
    nextTick(() => {})
    appState.openEditPromptBarDialog()
  }

  const closeEditPromptBarDialog = () => {
    appState.closeEditPromptBarDialog()
    appState.setNameForGeneratedPrompt('')
  }

  const removePromptBar = async () => {
    const barRef = doc(barsRef, selectedBarId.value)
    if (!barRef) return

    if (selectedBar.value.isPublic) {
      await syncBarWithPublicBars({
        bar: selectedBar.value,
        isPublic: false,
      })
    }

    if (selectedBar.value.isPopular) {
      await syncBarWithPopularBars({
        bar: selectedBar.value,
        isPopular: false,
        language: selectedLangInPopularDropdown.value,
      })
    }

    if (selectedBar.value.isInLibrary) {
      await syncBarWithLibraryBars({
        bar: selectedBar.value,
        isInLibrary: false,
        language: selectedLangInLibraryDropdown.value,
      })
    }

    Analytics.fireEvent('bar_remove', {
      tab_name: activeTab.value,
      prompt_bar_id: selectedBar.value.id,
    })

    await deleteDoc(barRef)
    await syncBarWithUserBars({
      bar: { id: selectedBarId.value },
      userId: user.value.uid,
      shouldRemove: true,
    })

    const barIndex = myPromptBars.value.findIndex((bar) => bar.id === selectedBarId.value)
    myPromptBars.value.splice(barIndex, 1)

    // console.log(new Date().toUTCString() + ' Read-> barsRef: removePromptBar', barRef)

    appState.updateActiveBarIdForTab(activeTab.value, null)
    selectedBarId.value = ''
    selectedBar.value = null

    updateBarOptions()

    if (myPromptBars.value.length === 0) {
      appState.setTabEmpty(true)
    }

    removeFromLocalStorage(activeTab.value)
  }

  return {
    barIdToCopyFrom,
    barsTabName,
    setEditTab,
    initialEditUrl,
    editPromptBar,
    openEditPromptBarDialog,
    closeEditPromptBarDialog,
    removePromptBar,
  }
}

export const getBarById = async (barId) => {
  const barDoc = (await getDoc(doc(barsRef, barId))).data() ?? {}
  barDoc.id = barId
  barDoc.promptMenu = barDoc.prompt_menu ? JSON.parse(barDoc.prompt_menu, convertIdToString) : []
  barDoc.linkMenu = barDoc.links ? JSON.parse(barDoc.links, convertIdToString) : []

  // Resolve owner reference if it exists
  if (barDoc.owner && barDoc.owner.path) {
    try {
      const ownerDoc = await getDoc(barDoc.owner)
      if (ownerDoc.exists()) {
        barDoc.owner = {
          id: ownerDoc.id,
          ...ownerDoc.data(),
        }
      }
    } catch (error) {
      console.warn('Failed to resolve owner reference:', error)
    }
  }

  return barDoc
}
