export const useTagColors = (isDarkTheme = false) => {
  const materialColors = {
    light: {
      Chat: { bg: '#FFF3E0', border: '#FF8F00', text: '#E65100' }, // Warm Orange
      Graphics: { bg: '#E8F5E8', border: '#43A047', text: '#2E7D32' }, // Fresh Green
      Movie: { bg: '#F3E5F5', border: '#8E24AA', text: '#6A1B9A' }, // Rich Purple
      Voice: { bg: '#FCE4EC', border: '#D81B60', text: '#AD1457' }, // Vibrant Pink
      Programming: { bg: '#E3F2FD', border: '#1976D2', text: '#0D47A1' }, // Professional Blue
      Tools: { bg: '#FFF8E1', border: '#FFA000', text: '#FF6F00' }, // Golden Amber
      'AI model hub': { bg: '#FFEBEE', border: '#D32F2F', text: '#C62828' }, // Modern Red
      'Universal AI agent': { bg: '#E0F2F1', border: '#00796B', text: '#004D40' }, // Elegant Teal
    },
    dark: {
      Chat: { bg: '#2E1810', border: '#FF8F00', text: '#FFB74D' }, // Dark Orange
      Graphics: { bg: '#1B2E1B', border: '#43A047', text: '#81C784' }, // Dark Green
      Movie: { bg: '#2A1A2E', border: '#8E24AA', text: '#BA68C8' }, // Dark Purple
      Voice: { bg: '#2E1A24', border: '#D81B60', text: '#F06292' }, // Dark Pink
      Programming: { bg: '#1A2332', border: '#1976D2', text: '#64B5F6' }, // Dark Blue
      Tools: { bg: '#332A1A', border: '#FFA000', text: '#FFCA28' }, // Dark Amber
      'AI model hub': { bg: '#2E1A1A', border: '#D32F2F', text: '#EF5350' }, // Dark Red
      'Universal AI agent': { bg: '#1A2E2A', border: '#00796B', text: '#4DB6AC' }, // Dark Teal
    },
  }

  const getTagColor = (tag) => {
    const colors = isDarkTheme ? materialColors.dark : materialColors.light
    const defaultColor = isDarkTheme
      ? { bg: '#424242', border: '#757575', text: '#E0E0E0' }
      : { bg: '#F5F5F5', border: '#BDBDBD', text: '#424242' }

    return colors[tag] || defaultColor
  }

  const getTagStyle = (tag, isSelected = false) => {
    const colorScheme = getTagColor(tag)

    // Default gray/neutral colors for inactive state
    const defaultColor = isDarkTheme
      ? { bg: '#424242', border: '#757575', text: '#E0E0E0' }
      : { bg: '#F5F5F5', border: '#BDBDBD', text: '#424242' }

    if (isSelected) {
      // Show distinctive colors when selected
      return {
        '--tag-bg': colorScheme.bg,
        '--tag-border': colorScheme.border,
        '--tag-text': colorScheme.text,
        '--tag-font-weight': '600',
      }
    }

    // Show gray/neutral colors by default (inactive state)
    return {
      '--tag-bg': defaultColor.bg,
      '--tag-border': defaultColor.border,
      '--tag-text': defaultColor.text,
      '--tag-font-weight': '500',
    }
  }

  return {
    getTagColor,
    getTagStyle,
  }
}
