import { ElMessage } from 'element-plus'
import { addDoc, collection, doc, getDoc, setDoc } from 'firebase/firestore'
import { getFunctions, httpsCallable } from 'firebase/functions'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useCurrentUser } from 'vuefire'

import { useAppStateStore } from '@/stores/appState'

import { db, getUserBars, syncBarWithUserBars, usersRef } from '../firebase'

export function usePromptBarActions({ selectedLanguage }) {
  const user = useCurrentUser()
  const { t } = useI18n()

  const appState = useAppStateStore()
  const { areMyPromptBarsLoaded, myPromptBars } = storeToRefs(appState)

  const copyMyPromptBar = async ({ selectedBar }) => {
    areMyPromptBarsLoaded.value = false
    try {
      if (!user.value?.uid) {
        ElMessage.error('User not authenticated')
        return
      }
      const barToCopy = { ...selectedBar.value }
      barToCopy.name = `${barToCopy.name} COPY`
      barToCopy.createdAt = new Date()
      barToCopy.owner = doc(usersRef, user.value.uid)
      delete barToCopy.id
      delete barToCopy.linkMenu
      const newBarRef = await addDoc(collection(db, 'bars'), barToCopy)
      const newBarId = newBarRef.id
      const fromUserBarDocRef = doc(db, 'user_bars', user.value.uid)
      const snap = await getDoc(fromUserBarDocRef)
      if (snap.exists()) {
        const userBarsData = snap.data()
        if (userBarsData[selectedBar.value.id]) {
          const copiedUserBar = {
            ...userBarsData[selectedBar.value.id],
            name: `${userBarsData[selectedBar.value.id].name} COPY`,
            createdAt: new Date(),
          }
          await setDoc(
            doc(db, 'user_bars', user.value.uid),
            { [newBarId]: copiedUserBar },
            { merge: true },
          )
        }
      }
      ElMessage.success('Prompt bar copied!')
    } catch (e) {
      ElMessage.error(`Error copying prompt bar: ${e.message}`)
    } finally {
      myPromptBars.value = await getUserBars(user.value.uid)
      areMyPromptBarsLoaded.value = true
    }
  }

  const copyToUser = async ({ selectedUser, selectedBar }) => {
    if (!selectedBar?.value?.id) {
      ElMessage.error(t('messages.noBarSelected'))
      return
    }
    if (!selectedUser.value || selectedUser.value.length === 0) {
      return
    }

    const barToCopy = { ...selectedBar.value }
    barToCopy.createdAt = new Date()
    delete barToCopy.linkMenu
    delete barToCopy.promptMenu
    delete barToCopy.editedAt
    barToCopy.isInLibrary = false
    barToCopy.isPopular = false
    barToCopy.isPublic = false
    barToCopy.sourceId = selectedBar.value.id

    for (const userId of selectedUser.value) {
      try {
        barToCopy.owner = doc(usersRef, user.value.uid)
        delete barToCopy.id
        const newBarRef = await addDoc(collection(db, 'bars'), barToCopy)
        barToCopy.id = newBarRef.id
        await syncBarWithUserBars({ bar: barToCopy, userId })
      } catch (e) {
        ElMessage.error(`${t('copyTab.errors.somethingWentWrong')}: ${e.message}`)
      }
    }
    ElMessage.success(t('copyTab.toSelectedUsers'))
  }

  const fixJsonAfterTranslation = (json) => {
    return json?.result ? json.result.replace(/```json\s*/, '').replace(/\s*```$/, '') : '[]'
  }

  const copyToUserAndTranslate = async ({ selectedUser, selectedBar }) => {
    if (!selectedBar?.value?.id) {
      ElMessage.error(t('messages.noBarSelected'))
      return
    }
    if (!selectedUser.value || selectedUser.value.length === 0) {
      return
    }

    const promptMenuJson = JSON.stringify(selectedBar.value.promptMenu)
    const linksMenuJson = JSON.stringify(selectedBar.value.linkMenu)

    let translatedName = ''
    let translatedTags = ''
    let translatedDescription = ''
    let translatedPromptMenu = ''
    let translatedLinksMenu = ''
    try {
      const results = await Promise.all([
        translateName(selectedBar.value.name),
        translateTags(selectedBar.value.tags),
        translateName(selectedBar.value.description),
        translatePropmpts(promptMenuJson),
        translateLinks(linksMenuJson),
      ])
      translatedName = results[0]
      translatedTags = results[1]
      translatedDescription = results[2]
      translatedPromptMenu = results[3]
      translatedLinksMenu = results[4]
    } catch (e) {
      console.error('Error translating bar: ', e)
      ElMessage.error(`${t('copyTab.errors.somethingWentWrong')}: ${e.message}`)
      return
    }

    const fixedPromptMenu = fixJsonAfterTranslation(translatedPromptMenu)
    const fixedLinksMenu = fixJsonAfterTranslation(translatedLinksMenu)

    const barToCopy = { ...selectedBar.value }
    barToCopy.name = translatedName.result
    barToCopy.tags = translatedTags.result?.split(',') || []
    barToCopy.description = translatedDescription.result || ''
    barToCopy.prompt_menu = fixedPromptMenu
    barToCopy.links = fixedLinksMenu
    barToCopy.createdAt = new Date()
    delete barToCopy.linkMenu
    delete barToCopy.promptMenu
    delete barToCopy.editedAt
    barToCopy.isInLibrary = false
    barToCopy.isPopular = false
    barToCopy.isPublic = false
    barToCopy.sourceId = selectedBar.value.id

    try {
      for (const userId of selectedUser.value) {
        try {
          barToCopy.owner = doc(usersRef, user.value.uid)
          delete barToCopy.id
          const newBarRef = await addDoc(collection(db, 'bars'), barToCopy)
          barToCopy.id = newBarRef.id
          await syncBarWithUserBars({ bar: barToCopy, userId })
        } catch (e) {
          ElMessage.error(`${t('copyTab.errors.somethingWentWrong')}: ${e.message}`)
        }
      }
      ElMessage.success(t('copyTab.toSelectedUsers'))
    } catch (e) {
      console.error('Error copying bar: ', e)
      ElMessage.error(`${t('copyTab.errors.somethingWentWrong')}: ${e.message}`)
    }
  }

  const translatePropmpts = async (prompts) => {
    if (!prompts) return ''
    const functions = getFunctions()
    const callableFunction = httpsCallable(functions, 'askAIGoogleGemini', { timeout: 360000 })
    const prompt = `Przetłumacz mi na język ${selectedLanguage.value} wartości z label, prompt, description dla tego jsona:\n${prompts}\nZwróć całego json z przetłumaczonymi wartościami. Zwróć tylko przetłumaczoną strukturę, nie dodawaj niczego innego.`
    const response = await callableFunction({
      prompt: prompt,
      language: selectedLanguage.value,
    })
    return response.data
  }

  const translateLinks = async (links) => {
    if (!links) return ''
    const functions = getFunctions()
    const callableFunction = httpsCallable(functions, 'askAIGoogleGemini', { timeout: 360000 })
    const prompt = `Przetłumacz mi na język ${selectedLanguage.value} wartości z pola label oraz pola link (jeśli zawiera tekst) dla tego jsona:\n${links}\nZwróć całego json z przetłumaczonymi wartościami. Zwróć tylko przetłumaczoną strukturę, nie dodawaj niczego innego. Nie tłumacz adresów url jeśli gdzieś występują.`
    const response = await callableFunction({
      prompt: prompt,
      language: selectedLanguage.value,
    })
    return response.data
  }

  const translateName = async (name) => {
    if (!name) return ''
    const functions = getFunctions()
    const callableFunction = httpsCallable(functions, 'askAIGoogleGemini')
    const prompt = `Przetłumacz mi na język ${selectedLanguage.value} ten tekst: ${name}. Zwróć tylko przetłumaczony tekst, nie dodawaj niczego innego.`
    const response = await callableFunction({
      prompt: prompt,
      language: selectedLanguage.value,
    })
    return response.data
  }

  const translateTags = async (tags) => {
    if (!tags) return ''
    const functions = getFunctions()
    const callableFunction = httpsCallable(functions, 'askAIGoogleGemini')
    const prompt = `Przetłumacz mi na język ${selectedLanguage.value} zachowując strukturę: ${tags}. Zwróć tylko przetłumaczoną strukturę, nie dodawaj niczego innego.`
    const response = await callableFunction({
      prompt: prompt,
      language: selectedLanguage.value,
    })
    return response.data
  }

  return {
    copyMyPromptBar,
    copyToUser,
    copyToUserAndTranslate,
    translatePropmpts,
    translateName,
    translateTags,
    translateLinks,
  }
}
