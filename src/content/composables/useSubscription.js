import { storeToRefs } from 'pinia'

import Analytics from '../../Analytics.js'
import { useAppStateStore } from '../../stores/appState'

export function useSubscription() {
  const appState = useAppStateStore()
  const { isSubscriptionDialogOpen, activeTab } = storeToRefs(appState)

  function openSubscriptionDialog(type = '') {
    Analytics.fireEvent('subscription_dialog', { type, tab_name: activeTab.value })
    appState.openSubscriptionDialog()
  }

  return {
    isSubscriptionDialogOpen,
    openSubscriptionDialog,
  }
}
