import { isEqual } from 'lodash'
import { storeToRefs } from 'pinia'
import { ref, watch } from 'vue'

import { useAppStateStore } from '../../stores/appState'
import { setToLocalStorage } from '../localStorage'
import { TABS } from '../utils'

const queryArrayFilter = ref([])
const barOptions = ref([])

export function useBarFiltering() {
  const appState = useAppStateStore()
  const { selectRef, selectedBarId } = storeToRefs(appState)

  const filterMethod = (query) => {
    const currentQuery = query.trim()
    if (!currentQuery) {
      queryArrayFilter.value = []
      barOptions.value = appState.getBarsForCurrentTab()
      return
    }

    const queryArray = currentQuery.split(' ').filter((queryItem) => queryItem.trim() !== '')
    queryArrayFilter.value = queryArray
    const allBars = appState.getBarsForCurrentTab()

    let matchingBarsByName = []
    let matchingBarsByTags = []

    allBars.forEach((bar) => {
      let nameMatch = false
      let tagsMatch = false

      queryArray.forEach((queryItem) => {
        if (bar.name.toLowerCase().includes(queryItem.toLowerCase())) {
          nameMatch = true
        }
        if (
          bar.tags &&
          bar.tags.some((tag) => tag.toLowerCase().includes(queryItem.toLowerCase()))
        ) {
          tagsMatch = true
        }
      })

      if (nameMatch) {
        matchingBarsByName.push(bar)
      } else if (tagsMatch) {
        matchingBarsByTags.push(bar)
      }
    })

    matchingBarsByName = [...new Set(matchingBarsByName)]
    matchingBarsByTags = [...new Set(matchingBarsByTags)]

    const combinedMatchingBars = [...matchingBarsByName, ...matchingBarsByTags]

    barOptions.value = combinedMatchingBars
  }

  const onTagClick = (ev, tag) => {
    ev.preventDefault()
    ev.stopPropagation()

    if (!selectRef.value || !selectRef.value[0]) return

    let query = selectRef.value[0].states.inputValue

    if (query.includes(tag)) {
      query = query.replace(tag, '').trim()
    } else {
      query += ` ${tag}`
    }

    selectedBarId.value = query
    selectRef.value[0].onInput({ target: { value: query } })

    filterMethod(query)
  }

  const getStyle = (tag, type) => {
    let match = false

    if (Array.isArray(queryArrayFilter.value)) {
      queryArrayFilter.value.forEach((queryItem) => {
        if (tag.toLowerCase().includes(queryItem.toLowerCase())) {
          match = true
        }
      })
    }
    // Zwracanie stylu na podstawie flagi match
    if (match) {
      if (type === 'bar') {
        return {
          color: '#ffd21f',
          textDecoration: 'underline',
          fontWeight: 'bold',
        }
      }
      if (type === 'tag') {
        return {
          backgroundColor: '#ffd21f',
          color: 'black',
        }
      }
    }

    return {}
  }

  const updateBarOptions = () => {
    barOptions.value = appState.getBarsForCurrentTab()
  }

  watch(
    () => appState.selectedBar,
    (newValue, oldValue) => {
      if (!newValue || isEqual(newValue, oldValue)) return

      if (appState.activeTab === TABS.MyPrompts) {
        appState.myPromptBars = appState.myPromptBars.map((bar) =>
          bar.id === appState.selectedBar.id ? appState.selectedBar : bar,
        )
      } else if (appState.activeTab === TABS.Public) {
        appState.publicBars = appState.publicBars.map((bar) =>
          bar.id === appState.selectedBar.id ? appState.selectedBar : bar,
        )
      }

      barOptions.value = appState.getBarsForCurrentTab()

      setToLocalStorage(appState.activeTab, appState.selectedBar)
    },
  )

  return {
    queryArrayFilter,
    barOptions,
    filterMethod,
    onTagClick,
    getStyle,
    updateBarOptions,
  }
}
