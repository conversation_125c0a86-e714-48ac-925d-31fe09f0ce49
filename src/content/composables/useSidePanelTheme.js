import { computed, ref, watch } from 'vue'

/**
 * Side panel theme management composable
 * <PERSON><PERSON> theme switching, localStorage persistence, and document theme application
 * Specifically designed for the Chrome extension side panel
 */
export function useSidePanelTheme() {
  // Available theme options (only Light and Dark)
  const THEME_OPTIONS = [
    { value: 'light', label: 'Light', icon: '<PERSON>' },
    { value: 'dark', label: 'Dark', icon: 'Moon' },
  ]

  // Current selected theme
  const selectedTheme = ref(
    localStorage.getItem('sidePanel_selectedTheme') ||
      (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'),
  )

  // Computed property for dark theme boolean
  const isDarkTheme = computed(() => selectedTheme.value === 'dark')

  // Apply theme to document for Element Plus and general styling
  const applyThemeToDocument = (theme) => {
    const htmlElement = document.documentElement

    // Remove existing theme classes
    htmlElement.classList.remove('dark', 'light')

    // Add new theme class
    if (theme === 'dark') {
      htmlElement.classList.add('dark')
    } else {
      htmlElement.classList.add('light')
    }

    // Set data attribute for additional styling hooks
    htmlElement.setAttribute('data-theme', theme)
  }

  // Save theme to localStorage
  const saveThemeToStorage = (theme) => {
    try {
      localStorage.setItem('sidePanel_selectedTheme', theme)
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }

  // Load theme from localStorage
  const loadThemeFromStorage = () => {
    try {
      const savedTheme = localStorage.getItem('sidePanel_selectedTheme')
      if (savedTheme && THEME_OPTIONS.some((option) => option.value === savedTheme)) {
        return savedTheme
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
    }
    return 'light' // Default fallback
  }

  // Set theme and persist to storage
  const setTheme = (theme) => {
    if (!THEME_OPTIONS.some((option) => option.value === theme)) {
      console.warn('Invalid theme value:', theme)
      return
    }

    selectedTheme.value = theme
    saveThemeToStorage(theme)
  }

  // Initialize theme from localStorage
  const initializeTheme = () => {
    const savedTheme = loadThemeFromStorage()
    selectedTheme.value = savedTheme
  }

  // Watch for theme changes and apply them
  watch(
    selectedTheme,
    (newTheme) => {
      applyThemeToDocument(newTheme)
    },
    { immediate: true },
  )

  // Initialize theme management
  const initializeThemeManagement = () => {
    initializeTheme()
    // Apply initial theme
    applyThemeToDocument(selectedTheme.value)
  }

  // Cleanup theme management (no cleanup needed for simplified version)
  const cleanupThemeManagement = () => {
    // No cleanup needed since we removed media query listeners
  }

  return {
    // Theme options
    THEME_OPTIONS,

    // Current state
    selectedTheme,
    isDarkTheme,

    // Actions
    setTheme,

    // Lifecycle
    initializeThemeManagement,
    cleanupThemeManagement,
  }
}
