export const setPhotopeaLinks = () => {
  const excludedImages = ['aipl-image', 'photopea-logo']
  const photopeaLogo =
    'data:image/svg+xml;base64,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'

  // Throttle function to limit execution frequency
  const throttle = (func, limit) => {
    let inThrottle
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  }

  // Use WeakMap to store processed images
  const processedImages = new WeakMap()

  // Create IntersectionObserver with options for better performance
  const imageObserver = new IntersectionObserver(
    (entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target
          if (
            excludedImages.some((className) => img.classList.contains(className)) ||
            processedImages.has(img)
          ) {
            return
          }

          if (img.complete) {
            requestIdleCallback(() => checkAndAddLink(img))
          } else {
            img.addEventListener('load', () => checkAndAddLink(img), { once: true })
          }
          observer.unobserve(img)
        }
      })
    },
    {
      rootMargin: '50px 0px', // Preload images slightly before they enter viewport
      threshold: 0.1,
    },
  )

  async function checkAndAddLink(img) {
    if (processedImages.has(img)) return
    processedImages.set(img, true)

    const boundingBox = img.getBoundingClientRect()
    if (boundingBox.width < 100 && boundingBox.height < 100) return

    img.classList.add('photo-edit-link')

    // Use blob cache if available
    let base64
    try {
      const response = await fetch(img.src, { cache: 'force-cache' })
      const blob = await response.blob()
      base64 = await new Promise((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result)
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.error('Error converting image to base64:', error, img)
      return
    }

    const baseToLink = `{"files": ["${base64}"]}`
    const encodedLink = encodeURIComponent(baseToLink)
    const maxBase64Length = 2000000 // 2mln symbols

    if (encodedLink.length > maxBase64Length) return

    const photopeaLink = addPhotopeaBtn(img, encodedLink)
    const parent = img.parentElement

    photopeaLink.onclick = (event) => {
      event.stopPropagation()
    }

    // Make sure parent has position relative
    if (getComputedStyle(parent).position === 'static') {
      parent.style.position = 'relative'
    }

    if (!parent.querySelector('.photopea-edit-link')) {
      parent.appendChild(photopeaLink)

      // Add hover events directly to the parent
      parent.addEventListener('mouseenter', () => {
        photopeaLink.style.display = 'block'
        img.style.pointerEvents = 'none'
      })

      parent.addEventListener('mouseleave', () => {
        photopeaLink.style.display = 'none'
        img.style.pointerEvents = 'auto'
      })
    }
  }

  // Throttled findImages function
  const findImages = throttle(() => {
    document.querySelectorAll('img').forEach((img) => {
      if (!processedImages.has(img)) {
        imageObserver.observe(img)
      }
    })
  }, 1000)

  // Optimized mutation observer
  const mutationObserver = new MutationObserver(
    throttle((mutations) => {
      const newImages = new Set()
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeName === 'IMG') {
            newImages.add(node)
          }
          if (node.querySelectorAll) {
            node.querySelectorAll('img').forEach((img) => newImages.add(img))
          }
        })
      })
      newImages.forEach((img) => {
        if (!processedImages.has(img)) {
          imageObserver.observe(img)
        }
      })
    }, 100),
  )

  // Start observing with optimized options
  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false,
  })

  // Initial scan using requestIdleCallback
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      requestIdleCallback(findImages)
    })
  } else {
    requestIdleCallback(findImages)
  }

  // Function to create Photopea button
  const addPhotopeaBtn = (img, encodedLink) => {
    const link = document.createElement('a')
    link.href = `https://www.photopea.com#${encodedLink}`
    link.target = '_blank'
    link.rel = 'noopener noreferrer'
    link.className = 'photopea-edit-link'
    link.style.cssText = `
      display: none;
      position: absolute;
      top: ${img.height - 45}px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      cursor: pointer;
      pointer-events: auto;
      background: rgba(0,0,0,0.7);
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 4px;
      transition: opacity 0.2s ease;
      color: #fff;
      font-weight: bold;
    `
    const editText = document.createElement('span')
    editText.innerHTML = 'Edit in '
    editText.style.cssText = `
      font-size: 12px;
      color: #fff;
      font-weight: bold;
      opacity: 0.9;
    `
    const imgElement = document.createElement('img')
    imgElement.src = photopeaLogo
    imgElement.alt = 'Edit in Photopea'
    imgElement.className = 'photopea-logo'
    imgElement.style.cssText = `
      height: 21px;
      display: inline-block;
      margin-top: -3px;
    `

    const text = document.createElement('div')
    text.innerHTML = 'Powered by AI Prompt Lab'
    text.style.cssText = `
      font-size: 8px;
      color: rgb(255, 255, 255);
      font-weight: bold;
      line-height: 11px;
      opacity: 0.7;
      text-align: center;
    `
    link.appendChild(editText)
    link.appendChild(imgElement)
    link.appendChild(text)
    return link
  }
}
