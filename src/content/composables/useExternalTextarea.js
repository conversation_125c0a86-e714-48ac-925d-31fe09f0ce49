import { storeToRefs } from 'pinia'
import { nextTick, ref } from 'vue'

import { chatSite } from '@/content/sites_index'
import { useAppStateStore } from '@/stores/appState'

// Global registry for internal components (shared across Vue instances)
if (!window.__aiPromptLabInternalComponents) {
  window.__aiPromptLabInternalComponents = new Map()
}

export function useExternalTextarea() {
  const appState = useAppStateStore()
  const outsideButton = ref(null)
  const { outsideTextareaPrompt: outsideTextarea } = storeToRefs(appState)

  // Internal component registry for AI integration (local instance)
  const internalComponents = ref(new Map())

  // Access global registry
  const globalInternalComponents = window.__aiPromptLabInternalComponents

  const getInternalTextareaSelector = (sel = 'textarea') => {
    // Combine local and global components
    const allComponents = [
      ...Array.from(internalComponents.value.values()),
      ...Array.from(globalInternalComponents.values()),
    ]

    // First, try to find active internal AI components
    const activeComponents = allComponents.filter(
      (comp) => comp && comp.isAIActive && comp.isAIActive(),
    )

    if (activeComponents.length > 0) {
      // Return the most recently focused component
      const activeComponent = activeComponents[activeComponents.length - 1]
      const textareaElement = activeComponent.getTextareaElement
        ? activeComponent.getTextareaElement()
        : null
      return textareaElement
    }

    // Fallback: look for any AI-enabled textarea components
    const aiTextareas = document.querySelectorAll(
      '.ai-prompt-textarea textarea, .ai-prompt-textarea .el-textarea__inner',
    )
    return aiTextareas.length > 0 ? aiTextareas[0] : null
  }

  const getOutsideSelectorFromChatSite = (sel) => {
    // First check for internal components (TextFieldComponent, etc.)
    if (sel === 'textarea') {
      const internalTextarea = getInternalTextareaSelector(sel)
      if (internalTextarea) {
        return internalTextarea
      }
    }

    // Then check external chat sites
    if (!chatSite || !chatSite.selector || !chatSite.selector[sel]) {
      return null
    }

    let outsideSelector = document.querySelector(chatSite.selector[sel])

    if (chatSite.selectorAlters && chatSite.selectorAlters[sel]) {
      for (let i = 0; i < chatSite.selectorAlters[sel].length; i++) {
        outsideSelector = outsideSelector || document.querySelector(chatSite.selectorAlters[sel][i])
      }
    }

    if (chatSite.selectorShadows && chatSite.selectorShadows[sel]) {
      for (let i = 0; i < chatSite.selectorShadows[sel].length; i++) {
        outsideSelector = outsideSelector.shadowRoot.querySelector(chatSite.selectorShadows[sel][i])
      }
    }

    return outsideSelector
  }

  const getOutsideTextareaValue = (el, elOld) => {
    if (el === elOld) return

    let myListener = function (event) {
      let newPromptValue = ''
      if (event.target.value !== undefined) {
        newPromptValue = event.target.value
      } else {
        newPromptValue = event.target.innerHTML
        // Handle Bard hack specifically - check if *after* getting innerHTML it should be empty
        if (event.target.innerHTML === '<p><br></p>') {
          setTimeout(() => {
            // Double check after timeout
            if (el.innerHTML === '<p><br></p>') {
              appState.setOutsideTextareaPrompt('') // Update store to empty
            }
          }, 50) // Shorter timeout might be sufficient
          // Don't update the store immediately for the Bard hack, wait for the timeout check
          return // Exit listener early for Bard hack case before timeout
        }
      }
      // Update ONLY the Pinia store
      appState.setOutsideTextareaPrompt(newPromptValue)
    }

    if (elOld) elOld.removeEventListener('input', myListener)
    // Ensure the listener is correctly attached
    if (el) {
      el.addEventListener('input', myListener)
    }
  }

  const updateOutsideTextareaPromptInitially = (outsideTextarea) => {
    let initialValue = ''
    if (!outsideTextarea) return // Guard clause

    if (
      outsideTextarea instanceof HTMLTextAreaElement ||
      outsideTextarea instanceof HTMLInputElement
    ) {
      initialValue = outsideTextarea.value
    } else if (outsideTextarea.innerHTML) {
      // Check if innerHTML exists
      let content = outsideTextarea.innerHTML
      // Apply same cleaning logic as in listener
      initialValue = content
        .replace(/<p><br><\/p>/g, '\n\n')
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p><p>/g, '\n')
        .replace(/<[^>]+>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .trim()
    }
    // Update ONLY the Pinia store with the initial value
    appState.setOutsideTextareaPrompt(initialValue)
  }

  // Internal component management
  const registerInternalComponent = (componentId, componentRef) => {
    if (componentRef && componentId) {
      // Register in both local and global registries
      internalComponents.value.set(componentId, componentRef)
      globalInternalComponents.set(componentId, componentRef)
    }
  }

  const unregisterInternalComponent = (componentId) => {
    if (componentId) {
      // Unregister from both local and global registries
      if (internalComponents.value.has(componentId)) {
        internalComponents.value.delete(componentId)
      }
      if (globalInternalComponents.has(componentId)) {
        globalInternalComponents.delete(componentId)
      }
    }
  }

  const setupInternalComponentMonitoring = (componentRef) => {
    if (!componentRef || !componentRef.getAIContent) return

    // Set up content monitoring for internal components
    const updateContent = () => {
      const content = componentRef.getAIContent()
      appState.setOutsideTextareaPrompt(content)
    }

    // Initial content update
    updateContent()

    return updateContent
  }

  return {
    outsideTextarea,
    outsideButton,
    getOutsideSelectorFromChatSite,
    getInternalTextareaSelector,
    getOutsideTextareaValue,
    updateOutsideTextareaPromptInitially,
    registerInternalComponent,
    unregisterInternalComponent,
    setupInternalComponentMonitoring,
    internalComponents,
  }
}
