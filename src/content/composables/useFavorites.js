import { storeToRefs } from 'pinia'
import { computed, ref, watch } from 'vue'

import { useAppStateStore } from '@/stores/appState'

import { setUserData } from '../firebase'
import { getFromLocalStorage, removeFromLocalStorage } from '../localStorage'
import { TABS } from '../utils'

const FAVORITES_STORAGE_KEY = 'ai-tools-favorites'

const favoriteSites = ref([])

const loadFavorites = () => {
  try {
    const stored = localStorage.getItem(FAVORITES_STORAGE_KEY)
    if (stored) {
      favoriteSites.value = JSON.parse(stored)
    }
  } catch (error) {
    console.warn('Błąd wczytywania ulubionych:', error)
    favoriteSites.value = []
  }
}

const saveFavorites = () => {
  try {
    localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favoriteSites.value))
  } catch (error) {
    console.warn('Błąd zapisywania ulubionych:', error)
  }
}

loadFavorites()

watch(favoriteSites, saveFavorites, { deep: true })

export function useFavorites() {
  const appState = useAppStateStore()
  const { selectedBar, userDoc } = storeToRefs(appState)

  const isFavorite = computed(() => {
    if (!userDoc?.value || !appState.hasSubscription) {
      return false
    }
    if (!selectedBar.value) {
      return false
    }
    return userDoc.value?.favorites?.includes(selectedBar.value.id)
  })

  const toggleFavorite = async () => {
    if (!userDoc?.value) {
      return
    }
    const favorites = userDoc.value.favorites || []

    if (favorites.includes(selectedBar.value.id)) {
      const savedBarFromFavoritesTab = getFromLocalStorage(TABS.Favorites)
      if (savedBarFromFavoritesTab?.id === selectedBar.value.id) {
        removeFromLocalStorage(TABS.Favorites)
      }
    }

    favorites.includes(selectedBar.value.id)
      ? favorites.splice(favorites.indexOf(selectedBar.value.id), 1)
      : favorites.push(selectedBar.value.id)

    await setUserData(userDoc.value.id, { favorites })
  }

  const addFavorite = (site) => {
    if (!isFavoriteItem(site.url)) {
      favoriteSites.value.push({
        url: site.url,
        name: site.name,
        description: site.description,
        tags: site.tags || [],
        dateAdded: new Date().toISOString(),
      })
    }
  }

  const removeFavorite = (url) => {
    const index = favoriteSites.value.findIndex((fav) => fav.url === url)
    if (index !== -1) {
      favoriteSites.value.splice(index, 1)
    }
  }

  const isFavoriteItem = (url) => {
    return favoriteSites.value.some((fav) => fav.url === url)
  }

  const clearFavorites = () => {
    favoriteSites.value = []
  }

  const updateFavorite = (originalUrl, updatedSite) => {
    const index = favoriteSites.value.findIndex((fav) => fav.url === originalUrl)
    if (index !== -1) {
      favoriteSites.value[index] = {
        ...favoriteSites.value[index],
        ...updatedSite,
        dateAdded: favoriteSites.value[index].dateAdded, // preserve original date
      }
    }
  }

  const reorderFavorites = (fromIndex, toIndex) => {
    const item = favoriteSites.value.splice(fromIndex, 1)[0]
    favoriteSites.value.splice(toIndex, 0, item)
  }

  const favoritesList = computed(() => [...favoriteSites.value])

  const favoritesCount = computed(() => favoriteSites.value.length)

  return {
    isFavorite,
    toggleFavorite,
    favorites: favoritesList,
    favoritesCount,
    addFavorite,
    removeFavorite,
    isFavoriteItem,
    clearFavorites,
    updateFavorite,
    reorderFavorites,
  }
}
