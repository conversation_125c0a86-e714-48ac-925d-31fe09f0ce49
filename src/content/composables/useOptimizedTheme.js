import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

import { useThemeDetection } from './useThemeDetection.js'

/**
 * Optimized theme management composable
 * Consolidates theme detection, source switching, and Chrome extension communication
 */
export function useOptimizedTheme() {
  const { isDarkTheme: localTheme, detectTheme } = useThemeDetection()

  // Core theme state
  const isDarkTheme = ref(false)
  const useMainPageTheme = ref(true)

  // Observers and intervals
  let themeObserver = null
  let mediaQueryListener = null

  // Initialize theme from local detection
  const initializeTheme = () => {
    detectTheme()
    isDarkTheme.value = localTheme.value
  }

  // Update theme based on current source preference
  const updateThemeFromSource = () => {
    if (!useMainPageTheme.value) {
      isDarkTheme.value = localTheme.value
    }
    // If using main page theme, wait for Chrome extension message
  }

  // Toggle between main page theme and local theme
  const toggleThemeSource = () => {
    useMainPageTheme.value = !useMainPageTheme.value

    updateThemeFromSource()
    return useMainPageTheme.value
  }

  // Handle theme update from Chrome extension
  const handleThemeFromMainPage = (newTheme) => {
    if (useMainPageTheme.value) {
      isDarkTheme.value = newTheme
    }
  }

  // Set up theme observers
  const setupThemeObservers = () => {
    // DOM mutation observer for theme changes
    const observer = new MutationObserver(() => {
      detectTheme()
      updateThemeFromSource()
    })

    // Observe document element
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: [
        'class',
        'data-theme',
        'data-mode',
        'data-color-scheme',
        'theme',
        'data-color-mode',
      ],
    })

    // Observe body element
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class', 'style'],
    })

    themeObserver = observer

    // Media query listener for system preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleMediaChange = () => {
      detectTheme()
      updateThemeFromSource()
    }

    mediaQuery.addEventListener('change', handleMediaChange)
    mediaQueryListener = { mediaQuery, handler: handleMediaChange }
  }

  // Clean up observers
  const cleanupThemeObservers = () => {
    if (themeObserver) {
      themeObserver.disconnect()
      themeObserver = null
    }

    if (mediaQueryListener) {
      mediaQueryListener.mediaQuery.removeEventListener('change', mediaQueryListener.handler)
      mediaQueryListener = null
    }
  }

  // Computed properties for theme classes (only the ones actually used)
  const containerThemeClasses = computed(() => ({
    'container--dark': isDarkTheme.value,
    'container--light': !isDarkTheme.value,
  }))

  // Initialize theme management
  const initializeThemeManagement = () => {
    initializeTheme()
    setupThemeObservers()
  }

  // Cleanup theme management
  const cleanupThemeManagement = () => {
    cleanupThemeObservers()
  }

  return {
    // Core state
    isDarkTheme,
    useMainPageTheme,

    // Theme source management
    toggleThemeSource,
    handleThemeFromMainPage,

    // Theme classes
    containerThemeClasses,

    // Lifecycle
    initializeThemeManagement,
    cleanupThemeManagement,

    // Manual updates
    updateThemeFromSource,
    initializeTheme,
  }
}
