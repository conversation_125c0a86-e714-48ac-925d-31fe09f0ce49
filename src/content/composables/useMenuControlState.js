import { ref } from 'vue'

export function useMenuControlState() {
  const mainAppVisible = ref(true)
  const chatButtonVisible = ref(true)
  const sidePanelVisible = ref(false)
  const menuHideLevel = ref(0)
  const currentSite = ref('')

  const getSiteIdentifier = () => {
    return window.location.hostname.replace(/[^a-zA-Z0-9.-]/g, '_')
  }

  const saveVisibilityState = () => {
    if (!currentSite.value) return

    const stateKey = `menuControlState_${currentSite.value}`
    const state = {
      mainAppVisible: mainAppVisible.value,
      chatButtonVisible: chatButtonVisible.value,
      sidePanelVisible: sidePanelVisible.value,
      menuHideLevel: menuHideLevel.value,
      timestamp: Date.now(),
    }
    localStorage.setItem(stateKey, JSON.stringify(state))
  }

  const loadVisibilityState = () => {
    if (!currentSite.value) return

    const stateKey = `menuControlState_${currentSite.value}`
    const savedState = localStorage.getItem(stateKey)

    if (savedState) {
      try {
        const state = JSON.parse(savedState)
        mainAppVisible.value = state.mainAppVisible ?? true
        chatButtonVisible.value = state.chatButtonVisible ?? true
        menuHideLevel.value = state.menuHideLevel ?? 0

        const appElement = document.getElementById('_appAIPM')
        const chatElement = document.getElementById('_chatButton')

        if (appElement) {
          appElement.style.display = mainAppVisible.value ? 'block' : 'none'
        }
        if (chatElement) {
          chatElement.style.display = chatButtonVisible.value ? 'block' : 'none'
        }
      } catch (e) {
        console.warn('Failed to load visibility state:', e)
      }
    }
  }

  const cleanupOldStates = () => {
    const now = Date.now()
    const thirtyDaysAgo = now - 30 * 24 * 60 * 60 * 1000

    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (key && key.startsWith('menuControlState_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          if (data.timestamp && data.timestamp < thirtyDaysAgo) {
            localStorage.removeItem(key)
          }
        } catch (e) {
          localStorage.removeItem(key)
        }
      }
    }
  }

  const initializeState = () => {
    currentSite.value = getSiteIdentifier()

    const savedMenuHideLevel = localStorage.getItem('menuHideLevel')
    if (savedMenuHideLevel) {
      menuHideLevel.value = parseInt(savedMenuHideLevel) || 0
    }

    loadVisibilityState()

    if (Math.random() < 0.1) {
      cleanupOldStates()
    }
  }

  return {
    mainAppVisible,
    chatButtonVisible,
    sidePanelVisible,
    menuHideLevel,
    currentSite,
    saveVisibilityState,
    loadVisibilityState,
    initializeState,
  }
}
