import { doc, getDoc } from 'firebase/firestore'
import { isEqual } from 'lodash'
import { storeToRefs } from 'pinia'
import { computed, watch } from 'vue'
import { firestoreDefaultConverter, useDocument } from 'vuefire'

import { useAppStateStore } from '../../stores/appState'
import { barsRef } from '../firebase'
import {
  getFromPluginStorageLocal,
  onChangeInPluginStorageLocal,
  removeFromPluginStorageLocal,
  setToLocalStorage,
} from '../localStorage'
import { convertIdToString, TABS } from '../utils.js'
import { useBarFiltering } from './useBarFiltering'

export function useStorageManagement() {
  const appState = useAppStateStore()
  const {
    selectedBarId,
    activeTab,
    myPromptBars,
    publicBars,
    popularBars,
    libraryBars,
    sharedBarId,
  } = storeToRefs(appState)
  const { updateBarOptions } = useBarFiltering()
  let { selectedBar } = storeToRefs(appState)
  const selectedBarSource = computed(() => {
    if (!selectedBarId.value) return null

    return doc(barsRef, selectedBarId.value).withConverter({
      fromFirestore: (snapshot) => {
        const data = firestoreDefaultConverter.fromFirestore(snapshot)
        data.promptMenu = JSON.parse(data.prompt_menu, convertIdToString)
        data.linkMenu = data.links ? JSON.parse(data.links, convertIdToString) : []
        return data
      },
      toFirestore: firestoreDefaultConverter.toFirestore,
    })
  })

  const reactiveBar = useDocument(selectedBarSource)
  watch(reactiveBar, (newBarData) => {
    if (newBarData && selectedBarId.value) {
      // For team bars, let the App.vue watch handle selectedBar updates
      // to avoid conflicts with team bars real-time synchronization
      if (appState.activeTab === 'Team') {
        // Only update if the bar data is significantly different
        // (e.g., different structure, not just property updates)
        if (!selectedBar.value || selectedBar.value.id !== newBarData.id) {
          selectedBar.value = newBarData
        }
      } else {
        selectedBar.value = newBarData
      }
    }
  })

  async function fetchDataFromPluginStorage(key) {
    try {
      const result = await getFromPluginStorageLocal(key)

      return result
    } catch (error) {
      return null
    }
  }

  async function getBarById(barId) {
    const barDoc = (await getDoc(doc(barsRef, barId))).data() ?? {}
    barDoc.id = barId
    barDoc.promptMenu = barDoc.prompt_menu ? JSON.parse(barDoc.prompt_menu, convertIdToString) : []
    barDoc.linkMenu = barDoc.links ? JSON.parse(barDoc.links, convertIdToString) : []

    return barDoc
  }

  const setBarFromSharedBar = async (id) => {
    selectedBarId.value = id
    const bar = await getBarById(id)
    if (!selectedBarId.value || !bar?.isPublic) {
      return
    }
    appState.setActiveTab(TABS.Public)
    publicBars.value = [bar]
    updateBarOptions()
    selectedBar.value = bar
    setToLocalStorage(appState.activeTab, selectedBar.value)
    removeFromPluginStorageLocal('sharedBar')
    sharedBarId.value = null
  }

  const handlePluginStorageChange = async (key, oldValue, newValue) => {
    if (key === 'editDateByBarId' && activeTab.value === TABS.MyPrompts) {
      if (!selectedBarId.value || !selectedBar.value || isEqual(oldValue, newValue)) return

      const { editedAt } = selectedBar.value
      const editedAtString = new Date(
        editedAt.seconds * 1000 + editedAt.nanoseconds / 1000000,
      ).toISOString()
      if (selectedBarId.value && newValue[selectedBarId.value] > editedAtString) {
        setTimeout(async () => {
          selectedBar.value = await getBarById(selectedBarId.value)
          const barIndex = myPromptBars.value.findIndex((bar) => bar.id === selectedBarId.value)
          myPromptBars.value[barIndex] = selectedBar.value
          setToLocalStorage(TABS.MyPrompts, selectedBar.value)
        }, 0)
      }
    }
  }

  // Setup watches for shared bar ID
  watch(
    () => sharedBarId.value,
    (newValue, oldValue) => {
      if (newValue && newValue !== oldValue) {
        setBarFromSharedBar(sharedBarId.value)
      }
    },
  )

  // Initialize storage change listener
  onChangeInPluginStorageLocal(handlePluginStorageChange)

  return {
    fetchDataFromPluginStorage,
    setBarFromSharedBar,
    handlePluginStorageChange,
    selectedBarSource,
  }
}
