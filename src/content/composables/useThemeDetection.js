import { ref } from 'vue'

export function useThemeDetection() {
  const isDarkTheme = ref(false)

  const detectTheme = () => {
    const isDarkMode =
      document.body.classList.contains('dark-theme') ||
      document.body.classList.contains('b_dark') ||
      document.body.classList.contains('dark') ||
      document.body.getAttribute('style')?.includes('background-color: rgb(24,') ||
      document.documentElement.classList.contains('dark') ||
      document.documentElement.getAttribute('data-mode') === 'dark' ||
      document.documentElement.getAttribute('data-theme') === 'dark' ||
      document.documentElement.getAttribute('data-color-scheme') === 'dark' ||
      document.documentElement.getAttribute('data-color-scheme') === '@dark' ||
      document.documentElement.getAttribute('data-ds-dark-theme') === 'dark' ||
      document.documentElement.getAttribute('theme') === 'dark' ||
      document.documentElement.getAttribute('data-color-mode') === 'dark' ||
      document.documentElement.getAttribute('style')?.includes('dark') ||
      (window.location.hostname.includes('genspark') &&
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches) ||
      document.documentElement.classList.contains('dark')

    isDarkTheme.value = isDarkMode
  }

  return {
    isDarkTheme,
    detectTheme,
  }
}
