import { ref } from 'vue'

export function useElementControls(state) {
  const { mainAppVisible, chatButtonVisible, menuHideLevel, saveVisibilityState } = state

  const toggleMainApp = () => {
    const appElement = document.getElementById('_appAIPM')
    if (!appElement) return

    if (mainAppVisible.value) {
      appElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
      appElement.style.opacity = '0'
      appElement.style.transform = 'scale(0.95) translateY(-10px)'
      setTimeout(() => {
        appElement.style.display = 'none'
        mainAppVisible.value = false
        saveVisibilityState()
      }, 300)
    } else {
      appElement.style.display = 'block'
      appElement.style.opacity = '0'
      appElement.style.transform = 'scale(0.95) translateY(-10px)'
      appElement.style.transition =
        'opacity 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)'

      requestAnimationFrame(() => {
        appElement.style.opacity = '1'
        appElement.style.transform = 'scale(1) translateY(0)'
      })

      mainAppVisible.value = true
      saveVisibilityState()
    }
  }

  const toggleChatButton = () => {
    const chatElement = document.getElementById('_chatButton')
    if (!chatElement) return

    if (chatButtonVisible.value) {
      chatElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
      chatElement.style.opacity = '0'
      chatElement.style.transform = 'scale(0.9) translateX(20px)'
      setTimeout(() => {
        chatElement.style.display = 'none'
        chatButtonVisible.value = false
        saveVisibilityState()
      }, 300)
    } else {
      chatElement.style.display = 'block'
      chatElement.style.opacity = '0'
      chatElement.style.transform = 'scale(0.9) translateX(20px)'
      chatElement.style.transition =
        'opacity 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)'

      requestAnimationFrame(() => {
        chatElement.style.opacity = '1'
        chatElement.style.transform = 'scale(1) translateX(0)'
      })

      chatButtonVisible.value = true
      saveVisibilityState()
    }
  }

  const applyMenuHideLevel = () => {
    const appElement = document.getElementById('_appAIPM')
    if (!appElement) return

    const tabsElement = appElement.querySelector('.menu-tabs')
    const smallMenuElement = appElement.querySelector('.small-menu')

    if (tabsElement) {
      if (menuHideLevel.value >= 1) {
        tabsElement.classList.add('hide-header')
      } else {
        tabsElement.classList.remove('hide-header')
      }
    }

    if (smallMenuElement) {
      if (menuHideLevel.value >= 1) {
        smallMenuElement.classList.add('menu-hidden')
      } else {
        smallMenuElement.classList.remove('menu-hidden')
      }
    }

    const barContainers = appElement.querySelectorAll('.bar-container')
    barContainers.forEach((container) => {
      if (menuHideLevel.value === 2) {
        container.classList.add('hide-bar')
      } else {
        container.classList.remove('hide-bar')
      }
    })
  }

  const changeMenuHideLevel = (step) => {
    if (menuHideLevel.value + step < 0 || menuHideLevel.value + step > 2) return
    menuHideLevel.value += step

    localStorage.setItem('menuHideLevel', menuHideLevel.value.toString())
    applyMenuHideLevel()
    saveVisibilityState()
  }

  return {
    toggleMainApp,
    toggleChatButton,
    changeMenuHideLevel,
    applyMenuHideLevel,
  }
}
