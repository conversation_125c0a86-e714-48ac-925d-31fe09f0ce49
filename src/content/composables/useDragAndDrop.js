import { ref } from 'vue'

export function useDragAndDrop() {
  const isDragging = ref(false)
  const position = ref({ right: 10, top: 50 })
  const isOnLeftSide = ref(false)
  let dragStarted = false

  const startDrag = (event, isCollapsed) => {
    if (!isCollapsed) return

    dragStarted = false
    const startX = event.clientX || (event.touches && event.touches[0].clientX)
    const startY = event.clientY || (event.touches && event.touches[0].clientY)

    const handleDrag = (e) => {
      e.preventDefault()

      const clientX = e.clientX || (e.touches && e.touches[0].clientX)
      const clientY = e.clientY || (e.touches && e.touches[0].clientY)

      const deltaX = Math.abs(clientX - startX)
      const deltaY = Math.abs(clientY - startY)

      if (!dragStarted && (deltaX > 5 || deltaY > 5)) {
        dragStarted = true
        isDragging.value = true
      }

      if (!isDragging.value) return

      const newTop = Math.max(5, Math.min(95, (clientY / window.innerHeight) * 100))
      position.value.top = newTop

      const screenCenter = window.innerWidth / 2
      isOnLeftSide.value = clientX < screenCenter
    }

    const stopDrag = () => {
      if (isDragging.value) {
        if (!isCollapsed) {
          const expandedHeight = 240
          const windowHeight = window.innerHeight
          const maxTop = ((windowHeight - expandedHeight - 30) / windowHeight) * 100
          position.value.top = Math.min(position.value.top, Math.max(10, maxTop))
        }

        setTimeout(() => {
          if (isOnLeftSide.value) {
            position.value.right = null
          } else {
            position.value.right = 10
          }
        }, 50)

        localStorage.setItem(
          'menuControlPosition',
          JSON.stringify({
            top: position.value.top,
            isOnLeftSide: isOnLeftSide.value,
          }),
        )
      }

      setTimeout(() => {
        isDragging.value = false
        dragStarted = false
      }, 100)

      document.removeEventListener('mousemove', handleDrag)
      document.removeEventListener('mouseup', stopDrag)
      document.removeEventListener('touchmove', handleDrag, { passive: false })
      document.removeEventListener('touchend', stopDrag)
    }

    document.addEventListener('mousemove', handleDrag)
    document.addEventListener('mouseup', stopDrag)
    document.addEventListener('touchmove', handleDrag, { passive: false })
    document.addEventListener('touchend', stopDrag)
  }

  const loadSavedPosition = () => {
    const savedPosition = localStorage.getItem('menuControlPosition')
    if (savedPosition) {
      const parsed = JSON.parse(savedPosition)
      position.value.top = parsed.top
      isOnLeftSide.value = parsed.isOnLeftSide
      if (parsed.isOnLeftSide) {
        position.value.right = null
      }
    }
  }

  return {
    isDragging,
    position,
    isOnLeftSide,
    startDrag,
    loadSavedPosition,
  }
}
