import { ref, watch } from 'vue'

const isSidePanel = () => {
  return (
    window.location.href.includes('side_panel.html') ||
    window.location.pathname.includes('side_panel.html')
  )
}

// Storage constants
const STORAGE_KEY = 'selectedProfileId'
const DEFAULT_PROFILE = 'default'

/**
 * Get profile from localStorage with fallback
 */
function getStoredProfile() {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored || DEFAULT_PROFILE
  } catch (error) {
    console.warn('Failed to load profile from localStorage:', error)
    return DEFAULT_PROFILE
  }
}

// Global reactive state - initialized with current localStorage value
// This ensures the initial state is always correct
const selectedProfileId = ref(getStoredProfile())
const savedProfiles = ref([])

// Global watcher to auto-save profile changes to localStorage
watch(selectedProfileId, (newProfileId) => {
  saveProfileToStorage(newProfileId)
})

/**
 * Save profile to localStorage
 */
function saveProfileToStorage(profileId) {
  try {
    localStorage.setItem(STORAGE_KEY, profileId)
  } catch (error) {
    console.warn('Failed to save profile to localStorage:', error)
  }
}

/**
 * Composable for profile synchronization across components and contexts
 * Handles localStorage persistence and Chrome extension message communication
 */
export function useProfileSync() {
  /**
   * Set profile and sync across all contexts
   */
  function setProfile(profileId) {
    console.log(`useProfileSync: Setting profile to: ${profileId}`)
    selectedProfileId.value = profileId
    saveProfileToStorage(profileId)

    // Send message to other contexts through Chrome extension
    if (window.chrome?.runtime?.sendMessage) {
      try {
        const source = isSidePanel() ? 'side_panel' : 'content'
        console.log(`useProfileSync: Sending profile change message: ${profileId} from ${source}`)
        chrome.runtime.sendMessage({
          type: 'PROFILE_CHANGED',
          profileId,
          source,
        })
      } catch (e) {
        console.warn('Failed to send profile change message:', e)
      }
    }
  }

  /**
   * Update saved profiles data
   */
  function updateSavedProfiles(profiles) {
    savedProfiles.value = profiles
  }

  /**
   * Listen for profile changes from other contexts
   */
  function listenForProfileChange() {
    if (window.chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
        if (msg?.type === 'PROFILE_CHANGED' && msg.profileId) {
          console.log(
            `useProfileSync: Received PROFILE_CHANGED message: ${msg.profileId} from ${msg.source}`,
          )

          // Don't update if message came from same context to prevent loops
          const currentSource = isSidePanel() ? 'side_panel' : 'content'
          if (msg.source === currentSource) {
            console.log(`useProfileSync: Ignoring message from same context: ${currentSource}`)
            return
          }

          // Update local state without triggering sync
          console.log(`useProfileSync: Updating profile from external message: ${msg.profileId}`)
          selectedProfileId.value = msg.profileId
          saveProfileToStorage(msg.profileId)
        }
      })
    }

    // Listen for broadcast messages from background script (for side panel)
    if (isSidePanel() && window.chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
        if (msg?.type === 'PROFILE_CHANGED_BROADCAST' && msg.profileId) {
          console.log(`useProfileSync: Received PROFILE_CHANGED_BROADCAST: ${msg.profileId}`)
          selectedProfileId.value = msg.profileId
          saveProfileToStorage(msg.profileId)
        }
      })
    }
  }

  /**
   * Initialize profile sync - now ensures current value is fresh
   */
  function initializeProfileSync() {
    // Force refresh from storage to handle any race conditions
    const currentStoredProfile = getStoredProfile()
    if (selectedProfileId.value !== currentStoredProfile) {
      console.log(
        `useProfileSync: Correcting profile from ${selectedProfileId.value} to ${currentStoredProfile}`,
      )
      selectedProfileId.value = currentStoredProfile
    }

    // Set up message listeners
    listenForProfileChange()
  }

  /**
   * Force reload profile from localStorage
   */
  function forceReloadProfile() {
    const storedProfile = getStoredProfile()
    console.log(`useProfileSync: Force reloading profile: ${storedProfile}`)
    selectedProfileId.value = storedProfile
  }

  return {
    // State
    selectedProfileId,
    savedProfiles,

    // Actions
    setProfile,
    updateSavedProfiles,
    initializeProfileSync,
    forceReloadProfile,

    // Utils
    getStoredProfile,
  }
}
