import { onMounted, onUnmounted, ref } from 'vue'

/**
 * Composable for Chrome extension communication
 * Handles side panel lifecycle, heartbeat, and theme communication
 */
export function useChromeExtension() {
  const isClosing = ref(false)

  let heartbeatInterval = null

  // Check if Chrome extension API is available
  const isChromeExtension = () => {
    return typeof chrome !== 'undefined' && chrome.runtime
  }

  // Send message to background script with error handling
  const sendMessage = async (message) => {
    if (!isChromeExtension() || isClosing.value) {
      return { success: false, error: 'Chrome API not available or closing' }
    }

    try {
      const response = await chrome.runtime.sendMessage(message)
      return { success: true, data: response }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // Side panel lifecycle methods
  const sendSidePanelOpened = async () => {
    const result = await sendMessage({ action: 'sidePanelOpened' })
    if (result.success) {
    }
    return result
  }

  const sendSidePanelClosed = async () => {
    if (isClosing.value) return { success: false, error: 'Already closing' }

    isClosing.value = true

    const result = await sendMessage({ action: 'sidePanelClosed' })
    if (result.success) {
    }
    return result
  }

  // Heartbeat functionality
  const sendHeartbeat = async () => {
    if (isClosing.value) return { success: false, error: 'Closing' }

    const result = await sendMessage({ action: 'sidePanelHeartbeat' })
    if (!result.success && chrome.runtime.lastError) {
    }
    return result
  }

  const startHeartbeat = (intervalMs = 3000) => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    heartbeatInterval = setInterval(sendHeartbeat, intervalMs)
  }

  const stopHeartbeat = () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      heartbeatInterval = null
    }
  }

  // Theme communication
  const requestCurrentTheme = async () => {
    const result = await sendMessage({ action: 'requestCurrentTheme' })
    if (!result.success) {
    }
    return result
  }

  // Message listener setup
  const setupMessageListener = (messageHandler) => {
    if (!isChromeExtension()) return () => {}

    const listener = (message, sender, sendResponse) => {
      const handled = messageHandler(message, sender, sendResponse)
      return handled // Return true if response is async
    }

    chrome.runtime.onMessage.addListener(listener)

    // Return cleanup function
    return () => {
      if (chrome.runtime.onMessage) {
        chrome.runtime.onMessage.removeListener(listener)
      }
    }
  }

  // Window event handlers
  const handleBeforeUnload = () => {
    sendSidePanelClosed()
  }

  const handleUnload = () => {
    sendSidePanelClosed()
  }

  // Lifecycle management
  const initializeExtension = (messageHandler) => {
    // Set up message listener
    const removeMessageListener = setupMessageListener(messageHandler)

    // Notify that side panel opened
    sendSidePanelOpened()

    // Start heartbeat
    startHeartbeat()

    // Request current theme
    requestCurrentTheme()

    // Set up window event listeners
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)

    // Return cleanup function
    return () => {
      removeMessageListener()
      stopHeartbeat()
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
    }
  }

  const cleanup = () => {
    stopHeartbeat()
    sendSidePanelClosed()
  }

  return {
    // State
    isClosing,

    // Utilities
    isChromeExtension,
    sendMessage,

    // Side panel lifecycle
    sendSidePanelOpened,
    sendSidePanelClosed,

    // Heartbeat
    sendHeartbeat,
    startHeartbeat,
    stopHeartbeat,

    // Theme
    requestCurrentTheme,

    // Message handling
    setupMessageListener,

    // Lifecycle
    initializeExtension,
    cleanup,
  }
}
