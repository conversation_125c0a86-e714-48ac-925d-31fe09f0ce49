const sites_list = [
  // Example
  // {
  //   url: 'example.com',
  //   name: 'Example', // short name
  //   displayName: 'Name of Example', // whole name
  //   selector: {
  //     main: '#example',
  //     chat: '#example',
  //     textarea: '#example',
  //     button: '#example',
  //     lastQuestion: '#example',
  //     lastAnswer: '#example',
  //     isAnswerGenerated: '#example',
  //   },
  //   // alternantive selectors (in case if chat owners changed element selector in new version but they keeping them both online)
  //   selectorAlters: {
  //     main: '#example',
  //     chat: '#example',
  //     textarea: '#example',
  //     button: '#example',
  //     lastQuestion: '#example',
  //     lastAnswer: '#example',
  //     isAnswerGenerated: '#example',
  //   },
  //   // in case you needed to get elements in shadow root (the main element is form base selector and every layer deeper in shadowroot is below this comment)
  //   selectorShadows: {
  //     chat: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //     textarea: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //     button: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //     lastQuestion: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //     lastAnswer: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //     isAnswerGenerated: [
  //       '#level-1',
  //       '#level-2',
  //     ],
  //   },
  // },
  {
    url: 'https://chatgpt.com',
    name: 'ChatGPT',
    displayName: 'Chat GPT',
    selector: {
      main: 'body .relative > .relative > .relative',
      chat: 'main.relative',
      textarea: '#prompt-textarea',
      button: 'div:has(#prompt-textarea) ~ button',
      lastQuestion:
        'main.relative .text-sm .text-token-text-primary:nth-last-child(2) .text-message>div',
      lastAnswer:
        'main.relative .text-sm .text-token-text-primary:nth-last-child(1) .text-message>div',
      isAnswerGenerated: '#prompt-textarea ~ button > span > svg',
    },
  },

  {
    url: 'https://gemini.google.com',
    name: 'Bard',
    displayName: 'Google Gemini',
    selector: {
      main: '.chat-container',
      chat: '.chat-history .conversation-container:last-child',
      textarea: 'rich-textarea .textarea',
      button: '.send-button-container button',
      lastQuestion: '.chat-history .conversation-container:last-child user-query .query-text',
      lastAnswer:
        '.chat-history .conversation-container:last-child model-response .model-response-text div',
      isAnswerGenerated:
        '.chat-history .conversation-container:last-child model-response .model-response-text div',
    },
    // alternantive selectors (in case if chat owners changed element selector in new version but they keeping them both online)
    selectorAlters: {
      textarea: ['mat-input-0'],
    },
  },

  // {
  //   url: 'https://bing.com/',
  //   name: 'Bing',
  //   displayName: 'Microsoft Bing',
  //   selector: {
  //     main: '#b_header',
  //     chat: '.cib-serp-main',
  //     textarea: '.cib-serp-main',
  //     button: '.cib-serp-main',
  //     lastQuestion: '.cib-serp-main',
  //     lastAnswer: '.cib-serp-main',
  //     isAnswerGenerated: '.cib-serp-main',
  //   },
  //   // in case you needed to get elements in shadow root (the base one is above and every layer deeper in shadowroot is below this comment)
  //   selectorShadows: {
  //     chat: [
  //       '#cib-conversation-main',
  //       '#cib-chat-main cib-chat-turn:last-of-type',
  //       'cib-message-group[source="user"]',
  //       'cib-message',
  //       '.content.text-message-content div',
  //     ],
  //     textarea: ['#cib-action-bar-main', 'cib-text-input', '.text-area'],
  //     button: ['#cib-action-bar-main', '.control.submit button'],
  //     lastQuestion: [
  //       '#cib-conversation-main',
  //       '#cib-chat-main cib-chat-turn:last-of-type',
  //       'cib-message-group[source="user"]',
  //       'cib-message',
  //       '.content.text-message-content div',
  //     ],
  //     lastAnswer: [
  //       '#cib-conversation-main',
  //       '#cib-chat-main cib-chat-turn:last-of-type',
  //       'cib-message-group[source="bot"]',
  //       'cib-message:last-of-type',
  //       '.content > div > div',
  //     ],
  //     isAnswerGenerated: [
  //       '#cib-action-bar-main',
  //       'cib-typing-indicator',
  //       '#stop-responding-button',
  //     ],
  //   },
  // },

  {
    url: 'https://copilot.microsoft.com',
    name: 'Copilot',
    displayName: 'Microsoft Copilot',
    selector: {
      main: 'body',
      chat: 'main .h-dvh .max-w-chat',
      textarea: 'textarea#userInput',
      button: 'button.rounded-submitButton',
      lastQuestion: 'div[data-content="user-message"]:last-child',
      lastAnswer: 'div[data-content="ai-message"]:last-child > div',
      isAnswerGenerated: 'button.rounded-submitButton',
    },
  },

  {
    url: 'https://www.perplexity.ai',
    allowOnSubdomains: ['home', 'perplexity.ai/search'],
    name: 'Perplexity',
    displayName: 'Perplexity',
    selector: {
      main: 'main',
      chat: '#ppl-message-scroll-target>.relative>div:nth-child(2)',
      textarea: '#ask-input',
      button: 'main .relative div:has(textarea) ~ button',
      lastQuestion:
        '#ppl-message-scroll-target>.relative>div:nth-child(2)>div:last-child .pb-md>div:first-child .my-md>.text-textMain',
      lastAnswer:
        '#ppl-message-scroll-target>.relative>div:nth-child(2)>div:last-child .pb-md>div:last-child .break-words>div>.prose.inline',
      isAnswerGenerated:
        '#ppl-message-scroll-target>.relative>div:nth-child(2)>div:last-child .pb-md>div:last-child .col-span-6>div:nth-child(2) h2>svg[data-icon="align-left"]',
    },
    selectorAlters: {
      textarea: ['.relative textarea.text-textMain'],
      button: ['.relative > .absolute > .text-textMain'],
    },
  },

  {
    url: 'https://claude.ai',
    allowOnSubdomains: ['claude.ai/chat', 'claude.ai/new'],
    excludeOnSubdomains: ['claude.ai/login'],
    name: 'Claude',
    displayName: 'Claude',
    selector: {
      main: 'body > div.flex > div:has(>main)',
      chat: 'body > .flex > main > div .flex-1.flex.flex-col.gap-3.px-4.max-w-3xl.mx-auto.w-full.pt-1',
      textarea: 'body > div.flex fieldset .ProseMirror.break-words',
      button: 'body > div.flex fieldset div:has(.ProseMirror.break-words) ~ button',
      lastQuestion:
        'body > div.flex > main > div .flex-1.flex.flex-col.gap-3.px-4.max-w-3xl.mx-auto.w-full.pt-1 > div:nth-last-child(3)',
      lastAnswer:
        'body > div.flex > main > div .flex-1.flex.flex-col.gap-3.px-4.max-w-3xl.mx-auto.w-full.pt-1 > div:nth-last-child(2)',
      isAnswerGenerated: '',
    },
    selectorAlters: {
      main: ['body > div.flex > .flex-1'],
      chat: [
        'body > div.flex > main > div .flex-1.flex.flex-col.gap-3.px-4.max-w-3xl.mx-auto.w-full.pt-1',
      ],
      textarea: ['body > div.flex fieldset .ProseMirror.break-words'],
      button: [
        'body > div.flex fieldset div:has(.ProseMirror.break-words) ~ button',
        'body > div.flex fieldset div:has(.ProseMirror.break-words) ~ button[disabled]',
      ],
    },
  },
  {
    url: 'https://vespa-engine-colpali-vespa-visual-retrieval.hf.space',
    name: 'Vespa',
    displayName: 'Vespa',
    selector: {
      main: 'body > main > div:nth-child(2)',
      chat: 'body > main',
      textarea: 'body form[hx-get="/fetch_results"] .awesomplete input.awesomplete',
      button: 'body form[hx-get="/fetch_results"] button[type="submit"]',
      lastQuestion: '',
      lastAnswer: '',
      isAnswerGenerated: '',
    },
  },
  {
    url: 'https://ideogram.ai',
    name: 'Ideogram',
    displayName: 'Ideogram',
    selector: {
      main: 'body > #root > div:last-child',
      chat: 'body > #root',
      textarea: '.MuiInputBase-root',
      button: '.MuiButtonBase-root',
    },
  },
  {
    url: 'https://lmarena.ai',
    name: 'LMArena',
    displayName: 'LMArena',
    selector: {
      main: 'body > div main',
      chat: 'ol.flex-col-reverse',
      textarea: 'form textarea',
      button: 'form button',
      lastQuestion: '',
      lastAnswer: '',
      isAnswerGenerated: '',
    },
  },
  {
    url: 'https://aistudio.google.com',
    name: 'AIStudio',
    displayName: 'AI Studio',
    selector: {
      main: 'body > app-root',
      chat: 'ms-chunk-editor .chat-container',
      textarea: 'ms-autosize-textarea > textarea.textarea',
      button: 'run-button button.run-button',
      lastQuestion: 'ms-chunk-editor',
      lastAnswer: 'ms-chunk-editor .chat-container .chat-view-container .chunk-editor-main',
      isAnswerGenerated:
        'run-button button.run-button[disabled] > .inner > .run-button-content > .label',
    },
  },
  {
    url: 'https://chat.deepseek.com',
    name: 'DeepSeek',
    displayName: 'DeepSeek',
    selector: {
      main: 'body > #root > .ds-theme',
      chat: '._03210fb.scrollable',
      textarea: 'textarea#chat-input',
      button: '._3172d9f[role="button"]',
      lastQuestion: '._83421f9 .c08e6e93',
      lastAnswer: '._9a2f8e4 ._9a8d0e1',
      isAnswerGenerated: '._7436101[aria-disabled="true"]',
    },
  },
  {
    url: 'https://chat.qwen.ai',
    name: 'Qwen',
    displayName: 'Qwen',
    selector: {
      main: '#app-container',
      chat: '#chat-container',
      textarea: 'textarea#chat-input',
      button: 'form .el-button[type="button"]',
      lastQuestion: '.chat-container .chat-message:last-of-type .chat-message-question',
      lastAnswer: '.chat-container .chat-message:last-of-type .chat-message-answer',
      isAnswerGenerated: 'textarea#chat-input:disabled',
    },
    selectorAlters: {
      textarea: ['#chat-input'],
      button: ['#chat-input'],
    },
  },
  {
    url: 'https://you.com',
    name: 'You',
    displayName: 'You',
    selector: {
      main: '._1d9u1t04._1d9u1t00',
      chat: '._1d9u1t04._1d9u1t00',
      textarea: '#search-input-textarea',
      button: '._1975xbj1 ._1975xbj6 button',
      lastQuestion: '.chat-container .chat-message:last-of-type .chat-message-question',
      lastAnswer: '.chat-container .chat-message:last-of-type .chat-message-answer',
      isAnswerGenerated: '#search-input-textarea:disabled',
    },
    selectorAlters: {
      textarea: ['textarea[name="query"]'],
      button: ['form._1975xbj1 button[type="submit"]'],
    },
  },
  {
    url: 'https://grok.com/',
    name: 'Grok',
    displayName: 'Grok',
    selector: {
      main: '.\\@container\\/mainview main',
      chat: '.\\@container\\/mainview main > .flex.flex-col',
      textarea: 'form textarea',
      button: 'form button[type="submit"]',
      lastQuestion: '.message-bubble.bg-surface-l2.border .whitespace-pre-wrap',
      lastAnswer: '.message-bubble.w-full.max-w-none .response-content-markdown',
      isAnswerGenerated: 'body',
    },
    selectorAlters: {
      main: ['main'],
      chat: ['div.flex.flex-col.items-center.h-full', 'div.flex.flex-col.max-w-3xl'],
      textarea: [
        'form .query-bar textarea',
        'form .query-bar .w-full.px-2.pt-5.mb-5.bg-transparent',
      ],
      button: [
        'form .query-bar button[type="submit"]',
        'form .query-bar .group.flex.flex-col.justify-center.rounded-full',
      ],
      lastQuestion: [
        '.message-bubble.rounded-3xl.text-primary.min-h-7.prose .whitespace-pre-wrap',
        '.message-bubble.bg-surface-l2.border .whitespace-pre-wrap',
        '.group.flex.flex-col .message-bubble .whitespace-pre-wrap',
      ],
      lastAnswer: [
        '.message-bubble.w-full.max-w-none .response-content-markdown',
        '.message-bubble.rounded-3xl.text-primary.min-h-7.prose.w-full.max-w-none',
        '.group.flex.flex-col .message-bubble .response-content-markdown',
      ],
      isAnswerGenerated: [
        'form .query-bar button[type="submit"][disabled]',
        'form .query-bar button[aria-label="Wyślij"][disabled]',
        'form button[type="submit"][disabled]',
      ],
    },
  },
  {
    url: 'https://bolt.new',
    name: 'Bolt',
    displayName: 'Bolt',
    selector: {
      main: 'body',
      chat: '._Chat_1dk13_5 section[aria-label="Chat"]',
      textarea: 'textarea',
      button: '._Chat_1dk13_5',
      lastQuestion: '._Chat_1dk13_5 section[aria-label="Chat"] > div:last-child',
      lastAnswer: '._Chat_1dk13_5 section[aria-label="Chat"] > div:last-child',
      isAnswerGenerated: '._Chat_1dk13_5 textarea:disabled',
    },
  },
  {
    url: 'https://lovable.dev',
    name: 'Lovable',
    displayName: 'Lovable',
    selector: {
      main: 'body',
      chat: 'main.container-home .relative.w-full',
      textarea: '#chatinput',
      button: '#chatinput-send-message-button',
      lastQuestion: '.grid.w-full.grid-cols-1:last-child .group.relative.flex.flex-col:last-child',
      lastAnswer: '.grid.w-full.grid-cols-1:last-child .group.relative.flex.flex-col:last-child',
      isAnswerGenerated: '#chatinput-send-message-button[disabled]',
    },
    selectorAlters: {
      main: ['body > div.flex.min-h-0.flex-1.flex-col'],
      chat: ['section.mb-\\[20px\\].flex.w-full.flex-col.items-center.justify-center'],
      textarea: ['textarea[placeholder*="Ask Lovable"]'],
      button: ['button[type="submit"]'],
    },
  },
  {
    url: 'https://chat.mistral.ai/chat',
    name: 'Mistral',
    displayName: 'Mistral',
    selector: {
      main: 'main.h-dvh',
      chat: 'main .@container/layout',
      textarea: 'textarea[placeholder*="Chat"], textarea[name="message.text"]',
      button: 'button[type="submit"][aria-label*="Send"]',
      lastQuestion: 'main .flex-col > div:nth-last-child(2)',
      lastAnswer: 'main .flex-col > div:last-child',
      isAnswerGenerated: 'button[type="submit"]:disabled',
    },
    selectorAlters: {
      main: ['body > main'],
      chat: ['main .relative .@container/layout'],
      textarea: ['form textarea', 'textarea[placeholder*="Zapytaj"]'],
      button: ['form button[type="submit"]', 'button[aria-label*="question"]'],
    },
  },
  {
    url: 'https://manus.im',
    name: 'Manus',
    displayName: 'Manus',
    selector: {
      main: 'body',
      chat: '.simplebar-mask',
      textarea: '.simplebar-content textarea[placeholder]',
      button: 'button.w-8.h-8.rounded-full:has(svg)',
      lastQuestion: '.flex.flex-col.w-full .overflow-y-auto',
      lastAnswer: '.flex.flex-col.w-full .overflow-y-auto',
      isAnswerGenerated: 'button.cursor-not-allowed',
    },
  },
  {
    url: 'https://www.genspark.ai/',
    name: 'Genspark',
    displayName: 'Genspark',
    selector: {
      main: '.index-layout-content',
      chat: '.flex.flex-col.md\\:h-screen .justify-between',
      textarea: 'textarea[name="query"].search-input',
      button: '.enter-icon-wrapper',
      lastQuestion: '',
      lastAnswer: '',
      isAnswerGenerated: 'textarea[name="query"]:disabled',
    },
  },
  {
    url: 'https://www.freepik.com',
    allowOnSubdomains: ['www.freepik.com/pikaso/assistant'],
    name: 'Freepik',
    displayName: 'Freepik AI Assistant',
    selector: {
      main: '#app',
      chat: '#anim-wrapper',
      textarea:
        'textarea.conversation-input-textarea, textarea#videoPromptValue, div[data-tour="mode-selector"] div[contenteditable="true"]',
      button: '[data-cy="assistant-nav-button"]',
      lastQuestion: '#anim-wrapper .text-message>div:nth-last-child(2)',
      lastAnswer: '#anim-wrapper .text-message>div:nth-last-child(1)',
      isAnswerGenerated: 'textarea:disabled',
    },
    selectorAlters: {
      main: ['#anim-wrapper'],
      chat: ['.main-container-assistant'],
      button: ['button[type="submit"]'],
    },
  },
  {
    url: 'https://jules.google.com/task',
    name: 'Jules',
    displayName: 'Jules',
    selector: {
      main: '.main-content-inner',
      chat: 'swebot-task-creation-view .welcome-container',
      textarea: 'textarea.prompt-editor, textarea[formcontrolname="userInput"]',
      button: 'swebot-task-creation-view .connect-button, .start-task-button',
      lastQuestion: 'swebot-task-creation-view .welcome-text',
      lastAnswer: 'swebot-task-creation-view .onboarding-text',
      isAnswerGenerated: 'swebot-task-creation-view .training-text',
    },
  },
  {
    url: 'https://notebooklm.google.com',
    name: 'NotebookLM',
    displayName: 'NotebookLM',
    selector: {
      main: '.app-body > div > div',
      chat: '.chat-panel-content',
      textarea: 'textarea.query-box-input',
      button: 'button[_ngcontent-ng-c3705208686]',
      lastQuestion: '.chat-panel-content > div:last-child .user-message',
      lastAnswer: '.chat-panel-content > div:last-child .ai-message',
      isAnswerGenerated: 'button[type="submit"][disabled]',
    },
    selectorAlters: {
      main: ['labs-tailwind-root'],
    },
  },
  {
    url: 'https://www.canva.com',
    name: 'Canva',
    displayName: 'Canva',
    selector: {
      main: '#\\:r0\\:1',
      chat: '.BMOCzQ._8CKO_A.EC2pjw._6Mu4Ow.uN3EIA.bu_JgA',
      textarea: 'form textarea[placeholder]',
      button: '.vxQy1w > button[type="submit"]',
      lastQuestion: '.IZ4ahw .lJVQMg',
      lastAnswer: '.SwlpcA .AakfMw',
      isAnswerGenerated: 'button[type="submit"][aria-disabled="true"]',
    },
  },
]

let location = window.location.href
let chatSite = {}

for (let site of sites_list) {
  if (location.startsWith(site.url)) {
    chatSite = site
  } else if (location.includes('side_panel.html')) {
    chatSite = {
      url: location,
      name: 'Side Panel',
      displayName: 'Side Panel',
      selector: {
        textarea: '#ai-prompt-lab-text-field',
      },
    }
  }
}

const avaibleSitesUrls = sites_list.map((s) => s.url)
const avaibleSitesNames = sites_list.map((s) => s.name)
const avaibleSitesUrlAndName = sites_list.map((s) => {
  return {
    url: s.url,
    name: s.displayName,
    id: s.name,
  }
})

export { chatSite, avaibleSitesUrlAndName }
