/**
 * Browser-based migration utility for sites data
 * This can be called from browser console or components
 */

import { doc, getDoc, setDoc } from 'firebase/firestore'

import { settingsRef } from '../firebase.js'
import { allTags, sitesWithTags } from '../sitesWithTags.js'

/**
 * Migrate sites data to Firestore
 * @returns {Promise<boolean>} Success status
 */
export async function migrateSitesToFirestore() {
  try {
    console.log('🚀 Starting migration of sites data to Firestore...')

    // Check if data already exists
    const sitesDocRef = doc(settingsRef, 'sites')
    const existingDoc = await getDoc(sitesDocRef)

    if (existingDoc.exists()) {
      const existingData = existingDoc.data()
      console.log('⚠️  Sites data already exists in Firestore')
      console.log('📅 Last updated:', existingData.lastUpdated)
      console.log(
        '📊 Current sites count:',
        existingData.totalSites || existingData.sitesWithTags?.length || 'unknown',
      )

      const shouldOverwrite = confirm(
        'Sites data already exists in Firestore. Do you want to overwrite it?',
      )
      if (!shouldOverwrite) {
        console.log('❌ Migration cancelled by user')
        return false
      }
    }

    const sitesData = {
      sitesWithTags,
      allTags,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
      migratedAt: new Date().toISOString(),
      totalSites: sitesWithTags.length,
      totalTags: allTags.length,
      source: 'browser-migration',
    }

    console.log(`📤 Uploading ${sitesData.totalSites} sites and ${sitesData.totalTags} tags...`)

    await setDoc(sitesDocRef, sitesData)

    console.log('✅ Sites data successfully migrated to Firestore!')
    console.log('📍 Document location: settings/sites')
    console.log('📊 Migrated data:')
    console.log('  - Sites:', sitesData.totalSites)
    console.log('  - Tags:', sitesData.totalTags)
    console.log('  - Version:', sitesData.version)

    return true
  } catch (error) {
    console.error('❌ Error migrating sites data to Firestore:', error)
    return false
  }
}

/**
 * Verify migration by reading data from Firestore
 * @returns {Promise<boolean>} Verification status
 */
export async function verifyMigration() {
  try {
    console.log('🔍 Verifying migration...')

    const sitesDocRef = doc(settingsRef, 'sites')
    const docSnap = await getDoc(sitesDocRef)

    if (!docSnap.exists()) {
      console.error('❌ No sites data found in Firestore')
      return false
    }

    const data = docSnap.data()
    const isValid =
      data.sitesWithTags &&
      Array.isArray(data.sitesWithTags) &&
      data.allTags &&
      Array.isArray(data.allTags)

    if (isValid) {
      console.log('✅ Migration verification successful!')
      console.log('📊 Found data:')
      console.log('  - Sites:', data.sitesWithTags.length)
      console.log('  - Tags:', data.allTags.length)
      console.log('  - Last updated:', data.lastUpdated)
      console.log('  - Version:', data.version)
      return true
    } else {
      console.error('❌ Invalid data structure in Firestore')
      return false
    }
  } catch (error) {
    console.error('❌ Error verifying migration:', error)
    return false
  }
}

// Make functions available globally for console access
if (typeof window !== 'undefined') {
  window.migrateSitesToFirestore = migrateSitesToFirestore
  window.verifyMigration = verifyMigration
}
