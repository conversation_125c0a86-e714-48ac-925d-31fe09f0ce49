@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  button,
  [type='button'] {
    background-color: var(--el-button-bg-color, var(--el-color-transparent));
  }
  input[type='text'] {
    color: var(--el-input-text-color, var(--el-text-color-regular));
    border: none;
  }
}
@layer components {
  .app-parent {
    display: flex;
    flex-direction: column;
  }
  #_appPerplexity {
    order: 1;
    position: sticky;
    top: 54px;
    z-index: 1000;
  }
  .app-parent > div:last-child {
    order: 100;
  }
  .chatButton-parent > .rounded-full {
    border-radius: 32px;
  }
  #_chatButton {
  }

  #_chatButton > div > div {
    margin: 0 2em;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}
