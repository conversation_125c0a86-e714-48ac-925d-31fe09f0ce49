<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sites Migration Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      .button:hover {
        background: #0056b3;
      }
      .button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      .data-preview {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        max-height: 400px;
        overflow-y: auto;
      }
      pre {
        margin: 0;
        white-space: pre-wrap;
      }
      .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }
      .site-card {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        margin: 5px 0;
      }
      .site-name {
        font-weight: bold;
        color: #007bff;
      }
      .site-tags {
        font-size: 0.9em;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 Sites Data Migration Test</h1>
      <p>
        This page tests the migration of sites data to Firestore and verifies the SitesGrid
        component functionality.
      </p>

      <div class="actions">
        <button id="migrateBtn" class="button">1. Migrate Sites to Firestore</button>
        <button id="verifyBtn" class="button">2. Verify Migration</button>
        <button id="loadBtn" class="button">3. Test Load from Firestore</button>
        <button id="testComponentBtn" class="button">4. Test Component Loading</button>
        <button id="clearBtn" class="button">Clear Status</button>
      </div>

      <div id="status"></div>
    </div>

    <div class="grid">
      <div class="container">
        <h2>📊 Static Data (Original)</h2>
        <div id="staticData"></div>
      </div>

      <div class="container">
        <h2>☁️ Firestore Data (Migrated)</h2>
        <div id="firestoreData"></div>
      </div>
    </div>

    <div class="container">
      <h2>🧪 Component Test</h2>
      <div id="componentTest">
        <p>Click "Test Component Loading" to simulate how SitesGrid.vue loads data.</p>
      </div>
    </div>

    <script type="module">
      let statusDiv = document.getElementById('status')
      let staticDataDiv = document.getElementById('staticData')
      let firestoreDataDiv = document.getElementById('firestoreData')
      let componentTestDiv = document.getElementById('componentTest')

      function addStatus(message, type = 'info') {
        const div = document.createElement('div')
        div.className = `status ${type}`
        div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`
        statusDiv.appendChild(div)
        statusDiv.scrollTop = statusDiv.scrollHeight
      }

      function showData(container, title, data, isStatic = false) {
        const sitesArray = isStatic ? data : data.sitesWithTags
        const tagsArray = isStatic ? [] : data.allTags

        container.innerHTML = `
                <h3>${title}</h3>
                <div class="status info">
                    Sites: ${sitesArray?.length || 0} | Tags: ${tagsArray?.length || 0}
                    ${
                      data.lastUpdated
                        ? `<br>Last Updated: ${new Date(data.lastUpdated).toLocaleString()}`
                        : ''
                    }
                </div>
                <div class="data-preview">
                    ${
                      sitesArray
                        ?.slice(0, 5)
                        .map(
                          (site) => `
                        <div class="site-card">
                            <div class="site-name">${site.name}</div>
                            <div>${site.url}</div>
                            <div class="site-tags">Tags: ${site.tags?.join(', ') || 'None'}</div>
                        </div>
                    `,
                        )
                        .join('') || 'No sites data'
                    }
                </div>
            `
      }

      function clearStatus() {
        statusDiv.innerHTML = ''
      }

      async function loadStaticData() {
        try {
          addStatus('Loading static data...', 'info')
          const { sitesWithTags, allTags } = await import('./content/sitesWithTags.js')
          showData(staticDataDiv, 'Static Data Loaded', sitesWithTags, true)
          addStatus(`Static data loaded: ${sitesWithTags.length} sites`, 'success')
          return { sitesWithTags, allTags }
        } catch (error) {
          addStatus(`Error loading static data: ${error.message}`, 'error')
          return null
        }
      }

      async function testMigration() {
        try {
          addStatus('Starting migration...', 'info')

          // First load static data to show comparison
          await loadStaticData()

          // Import the migration utility
          const { migrateSitesToFirestore } = await import(
            './content/utils/migrateSitesToFirestore.js'
          )

          addStatus('Migration utility loaded', 'success')

          const result = await migrateSitesToFirestore()

          if (result) {
            addStatus('✅ Migration completed successfully!', 'success')
          } else {
            addStatus('❌ Migration failed', 'error')
          }
        } catch (error) {
          addStatus(`Migration error: ${error.message}`, 'error')
          console.error('Migration error:', error)
        }
      }

      async function testVerification() {
        try {
          addStatus('Starting verification...', 'info')

          const { verifyMigration } = await import('./content/utils/migrateSitesToFirestore.js')

          const result = await verifyMigration()

          if (result) {
            addStatus('✅ Verification passed!', 'success')
          } else {
            addStatus('❌ Verification failed', 'error')
          }
        } catch (error) {
          addStatus(`Verification error: ${error.message}`, 'error')
          console.error('Verification error:', error)
        }
      }

      async function testLoad() {
        try {
          addStatus('Testing data load from Firestore...', 'info')

          // Import Firebase functions
          const { doc, getDoc } = await import('firebase/firestore')
          const { settingsRef } = await import('./content/firebase.js')

          const sitesDocRef = doc(settingsRef, 'sites')
          const docSnap = await getDoc(sitesDocRef)

          if (docSnap.exists()) {
            const data = docSnap.data()
            addStatus(
              `✅ Data loaded successfully! Found ${data.sitesWithTags?.length || 0} sites and ${
                data.allTags?.length || 0
              } tags`,
              'success',
            )

            showData(firestoreDataDiv, 'Firestore Data Loaded', data)
          } else {
            addStatus('❌ No data found in Firestore', 'error')
            firestoreDataDiv.innerHTML =
              '<div class="status error">No data found in Firestore</div>'
          }
        } catch (error) {
          addStatus(`Load test error: ${error.message}`, 'error')
          console.error('Load test error:', error)
        }
      }

      async function testComponentLoading() {
        try {
          addStatus('Testing component data loading simulation...', 'info')

          // Simulate the SitesGrid.vue loading process
          const { doc, getDoc } = await import('firebase/firestore')
          const { settingsRef } = await import('./content/firebase.js')

          let sitesWithTags = []
          let allTags = []
          let isLoadingSites = true
          let sitesLoadError = null

          componentTestDiv.innerHTML = `
                    <div class="status info">🔄 Loading sites data...</div>
                `

          try {
            const sitesDocRef = doc(settingsRef, 'sites')
            const docSnap = await getDoc(sitesDocRef)

            if (docSnap.exists()) {
              const data = docSnap.data()
              sitesWithTags = data.sitesWithTags || []
              allTags = data.allTags || []
              addStatus('✅ Component simulation: Data loaded from Firestore', 'success')
            } else {
              addStatus(
                '⚠️ Component simulation: No Firestore data, falling back to static import',
                'warning',
              )
              const { sitesWithTags: staticSites, allTags: staticTags } = await import(
                './content/sitesWithTags.js'
              )
              sitesWithTags = staticSites
              allTags = staticTags
              sitesLoadError = 'Using fallback data - consider running migration'
            }
          } catch (error) {
            addStatus(
              '⚠️ Component simulation: Error loading from Firestore, using fallback',
              'warning',
            )
            sitesLoadError = error.message

            try {
              const { sitesWithTags: staticSites, allTags: staticTags } = await import(
                './content/sitesWithTags.js'
              )
              sitesWithTags = staticSites
              allTags = staticTags
            } catch (fallbackError) {
              addStatus('❌ Component simulation: Failed to load fallback data', 'error')
              sitesLoadError = 'Failed to load both Firestore and fallback data'
            }
          } finally {
            isLoadingSites = false
          }

          // Show component simulation results
          componentTestDiv.innerHTML = `
                    <div class="status ${sitesLoadError ? 'warning' : 'success'}">
                        Loading: ${isLoadingSites ? 'Yes' : 'No'}<br>
                        Sites loaded: ${sitesWithTags.length}<br>
                        Tags loaded: ${allTags.length}<br>
                        ${sitesLoadError ? `Error: ${sitesLoadError}` : 'No errors'}
                    </div>
                    <div class="data-preview">
                        <h4>Component would render:</h4>
                        ${sitesWithTags
                          .slice(0, 3)
                          .map(
                            (site) => `
                            <div class="site-card">
                                <div class="site-name">${site.name}</div>
                                <div>${site.url}</div>
                                <div class="site-tags">Tags: ${site.tags?.join(', ')}</div>
                            </div>
                        `,
                          )
                          .join('')}
                        <div style="margin-top: 10px;">
                            <strong>Available tags:</strong> ${allTags.join(', ')}
                        </div>
                    </div>
                `

          addStatus('✅ Component loading simulation completed', 'success')
        } catch (error) {
          addStatus(`Component test error: ${error.message}`, 'error')
          console.error('Component test error:', error)
        }
      }

      // Event listeners
      document.getElementById('migrateBtn').addEventListener('click', testMigration)
      document.getElementById('verifyBtn').addEventListener('click', testVerification)
      document.getElementById('loadBtn').addEventListener('click', testLoad)
      document.getElementById('testComponentBtn').addEventListener('click', testComponentLoading)
      document.getElementById('clearBtn').addEventListener('click', clearStatus)

      // Initial setup
      addStatus('🚀 Test page loaded successfully', 'success')
      addStatus('📋 Follow the numbered buttons to test the migration process', 'info')

      // Load static data on page load
      loadStaticData()
    </script>
  </body>
</html>
