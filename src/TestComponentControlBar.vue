<template>
  <div class="test-container">
    <h1>ComponentControlBar Test</h1>
    <p>This is a simplified test environment for the ComponentControlBar component.</p>

    <div class="test-section">
      <h2>Theme: {{ isDarkTheme ? 'Dark' : 'Light' }}</h2>
      <button @click="toggleTheme" class="theme-toggle">Toggle Theme</button>
    </div>

    <div class="test-section">
      <h2>ComponentControlBar</h2>
      <ComponentControlBar
        :isDarkTheme="isDarkTheme"
        @update:components="handleComponentsUpdate"
        @update:theme="handleThemeUpdate"
      />
    </div>

    <div class="test-section">
      <h2>Components State</h2>
      <pre>{{ JSON.stringify(components, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h2>Console Output</h2>
      <div class="console-output">
        <div v-for="(log, index) in consoleLogs" :key="index" class="log-entry">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'

import ComponentControlBar from './content/components/ComponentControlBar.vue'

const isDarkTheme = ref(false)
const components = ref([])
const consoleLogs = ref([])

// Capture console logs
const originalConsoleLog = console.log
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

console.log = (...args) => {
  consoleLogs.value.push(`[LOG] ${args.join(' ')}`)
  originalConsoleLog(...args)
}

console.error = (...args) => {
  consoleLogs.value.push(`[ERROR] ${args.join(' ')}`)
  originalConsoleError(...args)
}

console.warn = (...args) => {
  consoleLogs.value.push(`[WARN] ${args.join(' ')}`)
  originalConsoleWarn(...args)
}

const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
}

const handleComponentsUpdate = (updatedComponents) => {
  components.value = updatedComponents
}

const handleThemeUpdate = (newTheme) => {
  isDarkTheme.value = newTheme
}
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

.theme-toggle {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.theme-toggle:hover {
  background: #0056b3;
}

.console-output {
  max-height: 300px;
  overflow-y: auto;
  background: #000;
  color: #00ff00;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 2px;
  word-break: break-all;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
