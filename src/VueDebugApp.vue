<template>
  <div id="side-panel-content" class="prompt-manager">
    <!-- Top navigation bar with horizontal layout -->
    <div class="top-navigation-bar" :class="topBarThemeClasses">
      <SmallMenu v-if="showSmallMenu" :isAdmin="isAdmin" :menuHideLevel="menuHideLevel" />
    </div>
    <div class="component-control-wrapper">
      <ComponentControlBar
        ref="componentControlBarRef"
        :isDarkTheme="isDarkTheme"
        @update:components="handleComponentsUpdate"
        @profile-data-updated="handleProfileDataUpdate"
      />
    </div>
    <!-- Optimized component rendering with v-show for better performance -->
    <template v-for="component in visibleComponents" :key="component.id">
      <div v-if="component.id === 'app'" id="manage-bar" class="component-section">
        <App :showSmallMenu="selectedProfileId === 'search'" />
      </div>

      <div v-else-if="component.id === 'aitools'" id="ai-tools-section" class="component-section">
        <AiTools :showLogo="selectedProfileId === 'search'" />
      </div>

      <div
        v-else-if="component.id === 'sitesgrid'"
        id="sites-grid-section"
        class="component-section"
      >
        <SitesGrid :isDarkTheme="isDarkTheme" />
      </div>

      <div
        v-else-if="component.id === 'textfield'"
        id="text-field-section"
        class="component-section"
      >
        <div class="text-field-debug-section">
          <TextFieldComponent
            ref="textFieldComponentRef"
            :isDarkTheme="isDarkTheme"
            :enableAIIntegration="true"
            :insertContent="testInsertContent"
            :showActionButtons="true"
            label=""
            :placeholder="t('sidePanel.textFieldPlaceholder')"
            @ai-content-change="handleTextFieldAIContentChange"
            @insert="handleInsert"
            @copy-success="handleCopySuccess"
            @copy-error="handleCopyError"
          />
        </div>
      </div>
    </template>

    <!-- Profile Selection Popup -->
    <ProfileSelectionPopup
      :visible="showProfilePopup"
      :selectedProfileId="selectedProfileId"
      :savedProfiles="savedProfiles"
      @close="closeProfilePopup"
      @profile-selected="handleProfileSelection"
    />

    <!-- Side Panel Welcome Popup -->
    <SidePanelPopup
      ref="sidePanelPopupRef"
      :isDarkTheme="isDarkTheme"
      :isLoading="false"
      @close="handleSidePanelPopupClose"
      @profile-changed="handleSidePanelProfileChanged"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Component imports
import App from './content/App.vue'
import AiTools from './content/components/AiTools.vue'
import ComponentControlBar from './content/components/ComponentControlBar.vue'
import ProfileSelectionPopup from './content/components/ProfileSelectionPopup.vue'
import SidePanelPopup from './content/components/SidePanelPopup.vue'
import SitesGrid from './content/components/SitesGrid.vue'
import SmallMenu from './content/components/SmallMenu.vue'
import TextFieldComponent from './content/components/TextFieldComponent.vue'
// Composable imports
import { useChromeExtension } from './content/composables/useChromeExtension.js'
import { useComponentManagement } from './content/composables/useComponentManagement.js'
import { useExternalTextarea } from './content/composables/useExternalTextarea.js'
import { useMenuHideLevel } from './content/composables/useMenuHideLevel.js'
import { useOptimizedTheme } from './content/composables/useOptimizedTheme.js'
import { useProfileSync } from './content/composables/useProfileSync.js'
import { useSidePanelTheme } from './content/composables/useSidePanelTheme.js'
// Store imports
import { useAppStateStore } from './stores/appState'

const { t } = useI18n()

// Component performance optimization
defineOptions({
  name: 'VueDebugApp',
  inheritAttrs: false,
})

// ============================================================================
// COMPOSABLES SETUP
// ============================================================================

// Chrome extension communication
const chromeExtension = useChromeExtension()

// Theme management
const {
  isDarkTheme,
  useMainPageTheme,
  toggleThemeSource,
  handleThemeFromMainPage,
  containerThemeClasses,
  initializeThemeManagement,
  cleanupThemeManagement,
  updateThemeFromSource,
} = useOptimizedTheme()

// Side panel theme management
const {
  initializeThemeManagement: initializeSidePanelTheme,
  cleanupThemeManagement: cleanupSidePanelTheme,
} = useSidePanelTheme()

// Profile synchronization management
const { selectedProfileId, savedProfiles, setProfile, updateSavedProfiles, initializeProfileSync } =
  useProfileSync()

// Component management
const { visibleComponents, updateComponents, isComponentVisible, getComponentStats } =
  useComponentManagement()

// External textarea integration for AI tools
const {
  registerInternalComponent,
  unregisterInternalComponent,
  setupInternalComponentMonitoring,
  getOutsideSelectorFromChatSite,
  internalComponents,
} = useExternalTextarea()

// App state for SmallMenu
const appState = useAppStateStore()
const { isAdmin } = storeToRefs(appState)

// Menu hide level for SmallMenu
const { menuHideLevel } = useMenuHideLevel()

// SmallMenu visibility control
const showSmallMenu = ref(true)

// TextFieldComponent ref for AI integration
const textFieldComponentRef = ref(null)

// Profile popup state
const showProfilePopup = ref(false)
const componentControlBarRef = ref(null)

// Side panel popup ref
const sidePanelPopupRef = ref(null)

// Theme classes for top navigation bar
const topBarThemeClasses = computed(() => ({
  'top-bar--dark': isDarkTheme.value,
  'top-bar--light': !isDarkTheme.value,
}))

// ============================================================================
// EVENT HANDLERS
// ============================================================================

// Handle component updates from ComponentControlBar
const handleComponentsUpdate = (updatedComponents) => {
  updateComponents(updatedComponents)
}

// ============================================================================
// PROFILE POPUP HANDLERS
// ============================================================================

// Handle keyboard shortcut for profile popup
const handleGlobalKeydown = (event) => {
  // Open profile popup with "/" key
  if (event.key === '/' && !event.ctrlKey && !event.metaKey && !event.altKey) {
    // Only trigger if not typing in an input field
    const activeElement = document.activeElement
    const isInputField =
      activeElement &&
      (activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true')

    if (!isInputField && !showProfilePopup.value) {
      event.preventDefault()
      openProfilePopup()
    }
  }
}

// Open profile popup
const openProfilePopup = () => {
  showProfilePopup.value = true
}

// Close profile popup
const closeProfilePopup = () => {
  showProfilePopup.value = false
}

// Handle profile selection from popup
const handleProfileSelection = (profileId) => {
  setProfile(profileId)
  // Trigger profile change in ComponentControlBar
  if (componentControlBarRef.value && componentControlBarRef.value.handleProfileChanged) {
    componentControlBarRef.value.handleProfileChanged(profileId)
  }
}

// Handle profile data updates from ComponentControlBar
const handleProfileDataUpdate = (data) => {
  setProfile(data.selectedProfileId)
  updateSavedProfiles(data.savedProfiles)
}

// ============================================================================
// SIDE PANEL POPUP HANDLERS
// ============================================================================

// Handle side panel popup close
const handleSidePanelPopupClose = () => {
  // Popup handles its own localStorage state
  // No additional action needed here
}

// Handle profile change from side panel popup
const handleSidePanelProfileChanged = (profileId) => {
  // Forward the profile change to the ComponentControlBar
  if (componentControlBarRef.value) {
    // This will trigger the same profile change logic as the main ProfileSwitcher
    setProfile(profileId)
  }
}

// TextFieldComponent AI Integration
const handleTextFieldAIContentChange = (content) => {
  // This will be called when TextFieldComponent content changes
  // The content is already synced via the component's internal logic
}

// Test data for button functionality
const testInsertContent = ref(' [WSTAWIONA ZAWARTOŚĆ] ')

// Button event handlers
const handleInsert = (data) => {}

const handleCopySuccess = (copiedText) => {}

const handleCopyError = (error) => {
  console.error('Copy failed:', error)
}

const setupTextFieldAIIntegration = () => {
  if (textFieldComponentRef.value) {
    const componentId = textFieldComponentRef.value.aiComponentId

    registerInternalComponent(componentId, textFieldComponentRef.value)
    setupInternalComponentMonitoring(textFieldComponentRef.value)
  }
}

const cleanupTextFieldAIIntegration = () => {
  if (textFieldComponentRef.value) {
    const componentId = textFieldComponentRef.value.aiComponentId
    unregisterInternalComponent(componentId)
  }
}

// Test function to debug prompt insertion
const testPromptInsertion = () => {
  // Test getOutsideSelectorFromChatSite
  const textarea = getOutsideSelectorFromChatSite('textarea')
  if (textarea && textarea.value !== undefined) {
    const originalValue = textarea.value
    textarea.value = originalValue + ' [TEST PROMPT INSERTED]'

    // Trigger events
    const inputEvent = new Event('input', { bubbles: true })
    textarea.dispatchEvent(inputEvent)

    const changeEvent = new Event('change', { bubbles: true })
    textarea.dispatchEvent(changeEvent)
  }
}

// Test direct insertion into TextFieldComponent
const testDirectInsertion = () => {
  if (textFieldComponentRef.value) {
    // Try using setAIContent method
    if (textFieldComponentRef.value.setAIContent) {
      textFieldComponentRef.value.setAIContent('Test prompt wstawiony bezpośrednio!')
    }

    // Try getting textarea element directly
    const textareaElement = textFieldComponentRef.value.getTextareaElement?.()
    if (textareaElement) {
      textareaElement.value = textareaElement.value + ' [DIRECT INSERT]'

      // Trigger events
      const inputEvent = new Event('input', { bubbles: true })
      textareaElement.dispatchEvent(inputEvent)

      const changeEvent = new Event('change', { bubbles: true })
      textareaElement.dispatchEvent(changeEvent)
    }
  }
}

// Test button functionality
const testButtonFunctionality = () => {
  if (textFieldComponentRef.value) {
    // Test insert content with timestamp
    testInsertContent.value = ` [TEST WSTAWIENIA ${new Date().toLocaleTimeString()}] `

    // Get current content
    const currentContent = textFieldComponentRef.value.getAIContent?.() || ''

    // Test setting content for copy functionality
    if (!currentContent.trim()) {
      textFieldComponentRef.value.setAIContent?.('Przykładowy tekst do skopiowania')
    }
  }
}

// ============================================================================
// CHROME EXTENSION MESSAGE HANDLING
// ============================================================================

// Handle messages from Chrome extension background script
const handleChromeMessage = (message, sender, sendResponse) => {
  switch (message.action) {
    case 'closeSidePanelWindow':
      chromeExtension.sendSidePanelClosed()
      sendResponse({ success: true })

      // Close window after a short delay
      setTimeout(() => {
        window.close()
      }, 100)
      return true

    case 'updateThemeFromMainPage':
      handleThemeFromMainPage(message.isDarkTheme)
      sendResponse({ success: true })
      return true

    default:
      return false
  }
}

// ============================================================================
// LIFECYCLE MANAGEMENT
// ============================================================================

// Cleanup function for Chrome extension
let chromeExtensionCleanup = null

// ============================================================================
// COMPONENT LIFECYCLE
// ============================================================================

onMounted(() => {
  // Initialize theme management
  initializeThemeManagement()

  // Initialize side panel theme management
  initializeSidePanelTheme()

  // Initialize profile synchronization BEFORE other components
  // This ensures the correct profile is loaded from localStorage
  initializeProfileSync()

  // Force reload profile to ensure consistency
  // This is especially important for side panel reopening
  const { forceReloadProfile } = useProfileSync()
  forceReloadProfile()

  // Initialize Chrome extension communication
  chromeExtensionCleanup = chromeExtension.initializeExtension(handleChromeMessage)

  // Add global keyboard event listener for profile popup
  document.addEventListener('keydown', handleGlobalKeydown)

  // Request current theme from main page if using Chrome extension
  if (chromeExtension.isChromeExtension() && useMainPageTheme.value) {
    chromeExtension.requestCurrentTheme()
  }

  // Set up TextFieldComponent AI integration when it becomes available
  nextTick(() => {
    setupTextFieldAIIntegration()
  })
})

onUnmounted(() => {
  // Cleanup theme management
  cleanupThemeManagement()

  // Cleanup side panel theme management
  cleanupSidePanelTheme()

  // Cleanup AI integration
  cleanupTextFieldAIIntegration()

  // Remove global keyboard event listener
  document.removeEventListener('keydown', handleGlobalKeydown)

  // Cleanup Chrome extension
  if (chromeExtensionCleanup) {
    chromeExtensionCleanup()
    chromeExtensionCleanup = null
  }

  // Final cleanup
  chromeExtension.cleanup()
})

// Watch for TextFieldComponent ref changes to set up AI integration
watch(textFieldComponentRef, (newRef, oldRef) => {
  if (oldRef) {
    cleanupTextFieldAIIntegration()
  }
  if (newRef) {
    nextTick(() => {
      setupTextFieldAIIntegration()
    })
  }
})

// Watch for component visibility changes to manage AI integration
watch(visibleComponents, (newComponents) => {
  const textFieldVisible = newComponents.some((comp) => comp.id === 'textfield' && comp.visible)
  if (textFieldVisible) {
    nextTick(() => {
      setupTextFieldAIIntegration()
    })
  } else {
    cleanupTextFieldAIIntegration()
  }
})
</script>

<style scoped>
.top-navigation-bar {
  @apply bg-neutral-200 dark:bg-neutral-800;
}

.component-section {
  @apply p-2;
}
</style>
