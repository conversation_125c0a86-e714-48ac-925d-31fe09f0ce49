import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'

import en from './lang/en.json'
import pl from './lang/pl.json'
import LocalStorageApp from './LocalStorageApp.vue'

import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

// Create i18n instance
const localeObj = new Intl.Locale(navigator.language)
const locale = localStorage.getItem('lang') || localeObj.language || 'en'

function customRule(choice, choicesLength) {
  if (choice === 0) {
    return 0
  }
  if (choice === 1) {
    return 1
  }
  const teen = choice > 10 && choice < 20

  if (!teen && choice % 10 >= 2 && choice % 10 <= 4) {
    return 2
  }

  return choicesLength < 4 ? 2 : 3
}

const i18n = createI18n({
  locale,
  pluralizationRules: {
    pl: customRule,
  },
  messages: {
    en,
    pl,
  },
})

// Create and configure the Vue app
const app = createApp(LocalStorageApp)

// Use plugins
app.use(ElementPlus)
app.use(i18n)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Configure for development
app.config.devtools = true

// Mount the app
const vueInstance = app.mount('#app')

if (typeof window !== 'undefined') {
  // Set development flag
  window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = window.__VUE_DEVTOOLS_GLOBAL_HOOK__ || {}

  // Expose Vue app for DevTools
  window.__VUE__ = app
  window.vueApp = app
  window.vueInstance = vueInstance

  // Mark as development mode
  if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    window.__VUE_DEVTOOLS_GLOBAL_HOOK__.emit('app:init', app, 3, {})
  }
}

// Apply theme class to html element
function updateThemeClass() {
  const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  document.documentElement.classList.toggle('dark', isDark)
}

window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateThemeClass)
updateThemeClass()
