import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'
import VueSocialSharing from 'vue3-social-sharing'
import { VueFire, VueFireAuth } from 'vuefire'

import { firebaseApp } from './content/firebase'
import en from './lang/en.json'
import fr from './lang/fr.json'
import pl from './lang/pl.json'
import VueDebugApp from './VueDebugApp.vue'

import './content/style.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

if (prefersDark) {
  document.documentElement.classList.add('dark')
} else {
  document.documentElement.classList.remove('dark')
}

const localeObj = new Intl.Locale(navigator.language)
const locale = localStorage.getItem('lang') || localeObj.language || 'en'
const i18n = createI18n({
  locale,
  pluralizationRules: {
    pl: customRule,
  },
  messages: {
    en,
    pl,
    fr,
  },
})
function customRule(choice, choicesLength) {
  if (choice === 0) {
    return 0
  }
  if (choice === 1) {
    return 1
  }
  const teen = choice > 10 && choice < 20

  if (!teen && choice % 10 >= 2 && choice % 10 <= 4) {
    return 2
  }

  return choicesLength < 4 ? 2 : 3
}
const pinia = createPinia()
const app = createApp(VueDebugApp)

// Use the shared Pinia instance
app.use(pinia) // <-- Use the shared instance
app.use(VueFire, {
  firebaseApp,
  modules: [VueFireAuth()],
})
app.use(ElementPlus)
app.use(i18n)
app.use(VueSocialSharing)
// use element-plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#_appAIPM')
