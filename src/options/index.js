import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import { createApp } from 'vue'

import './style.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './Options.vue'

const app = createApp(App)
app.use(ElementPlus)
// use element-plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#app')

let elem = document.querySelector('html')
if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
  elem.classList.add('dark')
}
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (event) => {
  elem.classList.toggle(event.matches ? 'dark' : 'light')
})
