{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "runtime": "nodejs18", "gen2": true}], "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "database": {"port": 9000}, "hosting": {"port": 5000}, "pubsub": {"port": 8085}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "extensions": {"firestore-send-email": "firebase/firestore-send-email@0.1.28", "firestore-stripe-payments": "invertase/firestore-stripe-payments@0.3.6"}, "hosting": {"public": "www", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}}