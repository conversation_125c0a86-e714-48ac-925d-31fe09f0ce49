import path from 'path'

import { crx } from '@crxjs/vite-plugin'
import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

import manifest from './src/manifest.js'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const isBuild = command === 'build'
  const isProduction = mode === 'production'
  console.log('isProduction', isProduction, 'isBuild', isBuild)

  return {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      emptyOutDir: true,
      outDir: 'AI-Prompt-Lab',
      rollupOptions: {
        ...(isBuild
          ? {
              input: {
                index: 'src/content/index.js',
                options: 'options.html',
                offscreen: 'offscreen.html',
                side_panel: 'side_panel.html',
                local_storage: 'local_storage.html',
                vue_debug: 'vue-debug.html',
              },
            }
          : {
              output: {
                manualChunks(id) {
                  const nodeModulesIndex = id.indexOf('node_modules')
                  if (nodeModulesIndex !== -1) {
                    const startIndex = nodeModulesIndex + 'node_modules/'.length
                    const endIndex = id.indexOf('/', startIndex)
                    return id.substring(startIndex, endIndex)
                  }
                  return null
                },
                chunkFileNames: 'assets/chunk-[hash].js',
              },
            }),
        // output: {
        //   chunkFileNames: 'assets/chunk-[hash].js',
        // },
      },
      // assetsInlineLimit: '2048',
      chunkSizeWarningLimit: 1024,
    },
    plugins: [crx({ manifest }), vue()],
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false', // Disable hydration mismatch details
    },
  }
})
