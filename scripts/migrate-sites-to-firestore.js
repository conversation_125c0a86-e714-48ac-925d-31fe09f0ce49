#!/usr/bin/env node

/**
 * Migration script to upload sites data to Firestore
 * Run this script once to migrate data from sitesWithTags.js to Firestore
 *
 * Usage: node scripts/migrate-sites-to-firestore.js
 */
import { doc, setDoc } from 'firebase/firestore'

import { settingsRef } from '../src/content/firebase.js'
import { allTags, sitesWithTags } from '../src/content/sitesWithTags.js'

async function migrateSitesToFirestore() {
  try {
    console.log('Starting migration of sites data to Firestore...')

    const sitesData = {
      sitesWithTags,
      allTags,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
      migratedAt: new Date().toISOString(),
      totalSites: sitesWithTags.length,
      totalTags: allTags.length,
    }

    console.log(`Migrating ${sitesData.totalSites} sites and ${sitesData.totalTags} tags...`)

    const sitesDocRef = doc(settingsRef, 'sites')
    await setDoc(sitesDocRef, sitesData)

    console.log('✅ Sites data successfully migrated to Firestore!')
    console.log('📍 Document location: settings/sites')
    console.log('📊 Data structure:')
    console.log('  - sitesWithTags: Array of site objects')
    console.log('  - allTags: Array of tag strings')
    console.log('  - lastUpdated: ISO timestamp')
    console.log('  - version: Data version')
    console.log('  - migratedAt: Migration timestamp')
    console.log('  - totalSites: Number of sites')
    console.log('  - totalTags: Number of tags')

    return true
  } catch (error) {
    console.error('❌ Error migrating sites data to Firestore:', error)
    console.error('Make sure you have:')
    console.error('1. Valid Firebase configuration in environment variables')
    console.error('2. Proper Firestore permissions')
    console.error('3. Network connectivity')
    return false
  }
}

// Run the migration
migrateSitesToFirestore()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Migration completed successfully!')
      console.log('You can now update SitesGrid.vue to load data from Firestore.')
    } else {
      console.log('\n💥 Migration failed!')
      process.exit(1)
    }
  })
  .catch((error) => {
    console.error('\n💥 Migration script error:', error)
    process.exit(1)
  })
