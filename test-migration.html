<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sites Migration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .data-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sites Data Migration Test</h1>
        <p>This page tests the migration of sites data to Firestore and verifies the functionality.</p>
        
        <div class="actions">
            <button id="migrateBtn" class="button">Migrate Sites to Firestore</button>
            <button id="verifyBtn" class="button">Verify Migration</button>
            <button id="loadBtn" class="button">Test Load from Firestore</button>
            <button id="clearBtn" class="button">Clear Status</button>
        </div>
        
        <div id="status"></div>
        <div id="dataPreview"></div>
    </div>

    <script type="module">
        // This script will test the migration functionality
        // Note: This requires the Firebase configuration to be available
        
        let statusDiv = document.getElementById('status');
        let dataPreviewDiv = document.getElementById('dataPreview');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            statusDiv.appendChild(div);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        function showData(title, data) {
            dataPreviewDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="data-preview">
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }
        
        function clearStatus() {
            statusDiv.innerHTML = '';
            dataPreviewDiv.innerHTML = '';
        }
        
        async function testMigration() {
            try {
                addStatus('Starting migration test...', 'info');
                
                // Import the migration utility
                const { migrateSitesToFirestore } = await import('./src/content/utils/migrateSitesToFirestore.js');
                
                addStatus('Migration utility loaded', 'success');
                
                const result = await migrateSitesToFirestore();
                
                if (result) {
                    addStatus('Migration completed successfully!', 'success');
                } else {
                    addStatus('Migration failed', 'error');
                }
                
            } catch (error) {
                addStatus(`Migration error: ${error.message}`, 'error');
                console.error('Migration error:', error);
            }
        }
        
        async function testVerification() {
            try {
                addStatus('Starting verification test...', 'info');
                
                const { verifyMigration } = await import('./src/content/utils/migrateSitesToFirestore.js');
                
                const result = await verifyMigration();
                
                if (result) {
                    addStatus('Verification passed!', 'success');
                } else {
                    addStatus('Verification failed', 'error');
                }
                
            } catch (error) {
                addStatus(`Verification error: ${error.message}`, 'error');
                console.error('Verification error:', error);
            }
        }
        
        async function testLoad() {
            try {
                addStatus('Testing data load from Firestore...', 'info');
                
                // Import Firebase functions
                const { doc, getDoc } = await import('firebase/firestore');
                const { settingsRef } = await import('./src/content/firebase.js');
                
                const sitesDocRef = doc(settingsRef, 'sites');
                const docSnap = await getDoc(sitesDocRef);
                
                if (docSnap.exists()) {
                    const data = docSnap.data();
                    addStatus(`Data loaded successfully! Found ${data.sitesWithTags?.length || 0} sites and ${data.allTags?.length || 0} tags`, 'success');
                    
                    showData('Loaded Data', {
                        sitesCount: data.sitesWithTags?.length || 0,
                        tagsCount: data.allTags?.length || 0,
                        lastUpdated: data.lastUpdated,
                        version: data.version,
                        sampleSites: data.sitesWithTags?.slice(0, 3) || [],
                        allTags: data.allTags || []
                    });
                } else {
                    addStatus('No data found in Firestore', 'error');
                }
                
            } catch (error) {
                addStatus(`Load test error: ${error.message}`, 'error');
                console.error('Load test error:', error);
            }
        }
        
        // Event listeners
        document.getElementById('migrateBtn').addEventListener('click', testMigration);
        document.getElementById('verifyBtn').addEventListener('click', testVerification);
        document.getElementById('loadBtn').addEventListener('click', testLoad);
        document.getElementById('clearBtn').addEventListener('click', clearStatus);
        
        // Initial status
        addStatus('Test page loaded. Click buttons to test migration functionality.', 'info');
        addStatus('Note: Make sure you have valid Firebase configuration and are connected to the internet.', 'info');
    </script>
</body>
</html>
