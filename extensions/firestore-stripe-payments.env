ALLOWED_EVENT_TYPES=com.stripe.v1.product.created,com.stripe.v1.product.updated,com.stripe.v1.product.deleted,com.stripe.v1.price.created,com.stripe.v1.price.updated,com.stripe.v1.price.deleted,com.stripe.v1.checkout.session.completed,com.stripe.v1.customer.subscription.created,com.stripe.v1.customer.subscription.updated,com.stripe.v1.customer.subscription.deleted,com.stripe.v1.payment_intent.processing,com.stripe.v1.payment_intent.succeeded,com.stripe.v1.payment_intent.canceled,com.stripe.v1.payment_intent.payment_failed
CREATE_CHECKOUT_SESSION_MIN_INSTANCES=0
CUSTOMERS_COLLECTION=customers
DELETE_STRIPE_CUSTOMERS=Do not delete
EVENTARC_CHANNEL=projects/chat-gpt-assistant-3671a/locations/us-central1/channels/firebase
LOCATION=europe-central2
PRODUCTS_COLLECTION=products
STRIPE_API_KEY=projects/${param:PROJECT_NUMBER}/secrets/firestore-stripe-payments-STRIPE_API_KEY/versions/latest
STRIPE_CONFIG_COLLECTION=configuration
STRIPE_WEBHOOK_SECRET=projects/${param:PROJECT_NUMBER}/secrets/firestore-stripe-payments-STRIPE_WEBHOOK_SECRET/versions/latest
SYNC_USERS_ON_CREATE=Do not sync