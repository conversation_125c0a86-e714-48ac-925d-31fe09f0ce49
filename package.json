{"name": "ai-prompt-lab", "version": "1.0.71", "author": "", "description": "", "type": "module", "license": "ISC", "keywords": [], "engines": {"node": ">=18"}, "scripts": {"dev": "vite -c vite.default.config.js", "prod": "vite -c vite.default.config.js --mode production", "build": "vite build -c vite.default.config.js && npm run remove-links && npm run compress", "build:signInWithPopup": "vite build -c vite.signInWithPopup.config.js", "build:dev": "vite build -c vite.default.config.js --mode development && npm run remove-links && npm run compress", "build:dev:signInWithPopup": "vite build -c vite.signInWithPopup.config.js --mode development", "emulators": "firebase emulators:start --only firestore,functions,auth,extensions --import=./emulator-data --export-on-exit", "e2e:emulators": "firebase emulators:start --only firestore,functions,auth,extensions --import=./e2e-emulator-data", "e2e:test": "playwright test", "e2e:run": "concurrently \"npm run emulators\" \"npm run e2e:test\"", "remove-links": "npx badlinks AI-Prompt-Lab -r", "compress": "zip -vr package/AI-Prompt-Lab.zip AI-Prompt-Lab/ -x '*.DS_Store'", "release": "npm version patch --force && npm run build", "preview": "vite preview", "fmt": "prettier --write \"**/*.{vue,js,json,css,scss,md}\"", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@invertase/firestore-stripe-payments": "^0.0.7", "@vueuse/core": "^13.1.0", "@vueuse/integrations": "^13.1.0", "change-case": "^5.4.4", "element-plus": "^2.9.1", "firebase": "^9.23.0", "lodash": "^4.17.21", "pinia": "^3.0.1", "stripe": "^17.2.1", "vue": "^3.3.4", "vue-i18n": "^10.0.7", "vue3-social-sharing": "^1.3.0", "vuefire": "^3.2.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.28", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@playwright/test": "^1.50.1", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.14", "badlinks": "^1.0.9", "concurrently": "^9.1.2", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.4.25", "prettier": "^3.0.2", "tailwindcss": "^3.3.2", "vite": "^4.3.9", "vite-plugin-zip-pack": "^1.0.5"}, "lint-staged": {"**/*.{vue,js,json,css,scss,md}": "npm run fmt"}}