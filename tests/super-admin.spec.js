import path from 'path'

import { chromium } from 'playwright'

import { user } from './accounts'
import { expect, test } from './fixtures'
import {
  checkCurrentPromptBarsForLanguage,
  closeCookieBannerIfVisible,
  getPromptLocator,
  getTabLocator,
  isButtonWithToolipDisabled,
  isButtonWithTooltipEnabled,
  login,
  openTestPage,
  removePromptBarByName,
  switchFilterLanguage,
  testPageName,
} from './functions'

test('admin can copy prompt bar to user', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).toBeVisible()
  await page.locator('.el-select-dropdown').getByText('Workout', { exact: true }).click()
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.locator('#tab-Translation').click()

  await page.getByTestId('user-select-for-copy').click()
  await page.locator('.el-scrollbar').getByText('E2E+PRE').click()
  await page.getByTestId('user-select-for-copy').click()
  await page.getByTestId('copy-prompt-bar-button').click()
  await page.getByText('Copy prompt bar').click()
  await expect(page.getByText('Copied to selected users')).toBeVisible()

  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  //premium user

  const pathToExtension = path.join(process.cwd(), 'AI-Prompt-Lab')

  const context = await chromium.launchPersistentContext('', {
    channel: 'chromium',
    args: [`--disable-extensions-except=${pathToExtension}`, `--load-extension=${pathToExtension}`],
  })
  const page2 = await context.newPage()
  await openTestPage(page2)
  await login(page2, user.premium)

  await getTabLocator(page2, 'My Prompts').click()
  await removePromptBarByName(page2, 'Workout')

  await context.close()
})

test('user can see popular and library prompt bars', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await getTabLocator(page, 'My Prompts').click()

  const pathToExtension = path.join(process.cwd(), 'AI-Prompt-Lab')

  const context = await chromium.launchPersistentContext('', {
    channel: 'chromium',
    args: [`--disable-extensions-except=${pathToExtension}`, `--load-extension=${pathToExtension}`],
  })
  const page2 = await context.newPage()
  await openTestPage(page2)
  await login(page2, user.standard)

  const isCookieBannerVisible2 = await page2.getByText('We use cookies').isVisible()
  if (isCookieBannerVisible2) {
    await page2.getByText('Reject non-essential').click()
  }
  await getTabLocator(page2, 'Library').click()
  await page2.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page2.getByText('Music').first()).toBeVisible()
  await page2.getByText('Music').first().click()

  await page2.getByText('Generate song').click()
  await expect(page2.getByText('rock').first()).toBeVisible()
  await page2.getByText('Guitar tabs').first().click()

  // user 1
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Music').first()).toBeVisible()
  await page.getByText('Music', { exact: true }).click()
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByText('rock').nth(2).dblclick()
  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')
  await promptBarEditLocator.locator('input').nth(2).fill('hard rock')

  await page.getByText('Guitar tabs').nth(2).click()
  await page.getByText('Guitar tabs').nth(2).dblclick()
  await promptBarEditLocator.locator('input').nth(5).fill('Guitar chords')
  await promptBarEditLocator.locator('button').first().click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  // user2
  // update view
  await getTabLocator(page2, 'Popular').click()
  await getTabLocator(page2, 'Library').click()

  await expect(page2.getByText('Generate song')).toBeVisible()
  await expect(page2.getByText('Generate lyrics')).toBeVisible()
  await expect(page2.getByText('Piano sheet').first()).toBeVisible()

  await page2.getByText('Generate song').click()

  await context.close()

  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByText('hard rock').nth(1).dblclick()
  await promptBarEditLocator.locator('input').nth(2).fill('rock')
  await promptBarEditLocator.locator('button').first().click()

  await page.getByText('Guitar chords').nth(1).click()
  await page.getByText('Guitar chords').nth(1).dblclick()
  await promptBarEditLocator.locator('input').nth(5).fill('Guitar tabs')
  await promptBarEditLocator.locator('button').first().click()

  await page.getByTestId('close-edit-prompt-bar-dialog').click()
})

test('user can use finder', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await expect(getPromptLocator(page)).toBeVisible()
  await getPromptLocator(page).fill('running')
  await page.getByText('Finder').hover()
  await expect(page.getByText('Find prompts in the library')).toBeVisible()
  await page.getByText('Finder').click()
  await expect(page.getByText('Finder:')).toBeVisible()
  await expect(page.getByTestId('better-prompt-result')).toBeVisible()
  await page.getByText('Find in library').click()
  const promptBarDropdownListLocator = page.getByTestId('my-prompt-bar-dropdown')
  await expect(promptBarDropdownListLocator.first().getByText('pushups').first()).toBeVisible()

  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Workout', { exact: true })).toBeVisible()
  await page.getByText('Workout', { exact: true }).click()

  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('language-select-dropdown').click()
  await page.getByTestId('language-select-option-in-edit-mode').getByText('Polish').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await page.getByText('Account').click()
  await page.getByTestId('interface-language-selector').click()
  await page.getByTestId('interface-language-selector-option').getByText('Polish').click()

  await page.getByText('Znajdź w bibliotece').click()
  await expect(page.getByText('Workout', { exact: true })).toBeVisible()
  await page.getByTestId('close-ai-result-button').click()
  await getPromptLocator(page).fill('music')
  await page.getByText('Wyszukiwarka').click()
  await expect(page.getByText('Finder:')).toBeVisible()
  await expect(page.getByTestId('better-prompt-result')).toBeVisible()
  await expect(page.getByText('Brak wyników', { exact: true })).toBeVisible()

  await getTabLocator(page, 'Moje Prompty').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()

  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('language-select-dropdown').click()
  await page.getByTestId('language-select-option-in-edit-mode').getByText('Angielski').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()
})

test('user can use AI buttons', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await getTabLocator(page, 'My Prompts').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Workout').first()).toBeVisible()
  await page.getByText('Workout').first().click()

  await expect(getPromptLocator(page)).toBeVisible()
  await getPromptLocator(page).fill('workout')
  await page.getByTestId('select-bar-in-my-prompts-to-activate-ai-button').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await expect(
    page.locator('.prompt-recursiveMenu').nth(4).getByText(testPageName).nth(1),
  ).toBeVisible()

  await page.getByTestId('add-ai-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()

  await page.getByTestId('add-prompt-bar-dialog-cancel-button').click()

  await page.getByTestId('add-ai-prompt-button-default').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await expect(
    page.locator('.prompt-recursiveMenu', { hasText: 'Effective Workout' }).first(),
  ).toBeVisible()

  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(page.getByText(testPageName).nth(3)).toBeVisible()

  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')

  await page.getByText('Effective Workout').nth(1).click()
  await expect(page.getByText('Effective Workout').nth(1)).toBeVisible()
  await page.getByText('Effective Workout').nth(1).hover()
  await promptBarEditLocator.locator('.el-button--danger').nth(5).click({ force: true })
  await page.getByRole('button', { name: 'OK' }).click({ force: true })

  await page.getByText(testPageName).nth(3).click()
  await page.getByText(testPageName).nth(3).hover()
  await promptBarEditLocator.locator('.el-button--danger').nth(4).click({ force: true })
  await page.getByRole('button', { name: 'OK' }).click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click()
})

test('Free user can see popular prompt bars and can not use premium prompts', async ({ page }) => {
  await openTestPage(page)

  await closeCookieBannerIfVisible(page)

  await getTabLocator(page, 'Popular').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Music').first()).toBeVisible()
  await page.getByText('Music').first().click()

  await expect(page.getByText('Generate song')).toBeVisible()
  await expect(page.getByText('Generate lyrics')).toBeVisible()
  await expect(page.getByText('Guitar tabs').first()).toBeVisible()
  await expect(page.getByText('Piano sheet').first()).toBeVisible()

  await page.getByText('Piano sheet').first().click()
  await expect(page.getByText('Subscription - AI Prompt Lab')).toBeVisible()
  await page.getByText('Close').click()

  await page.getByText('Guitar tabs').first().click()
  await expect(getPromptLocator(page).getByText('Generate guitar tabs')).toBeVisible()
})

test('super admin can set visible in popular and library prompt bars', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await getTabLocator(page, 'My Prompts').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Workout', { exact: true })).toBeVisible()
  await page.getByText('Workout', { exact: true }).click()

  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(page.getByTestId('visible-in-popular-switch')).toBeVisible()
  await expect(page.getByTestId('visible-in-library-switch')).toBeVisible()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()
})

test('AI buttons have correct logic for disabled state', async ({ page }) => {
  await openTestPage(page)

  const panel = page.locator('.prompt-manager')

  const betterPromptButton = panel.getByRole('button', { name: 'Better Prompt' })
  const frameworksButton = panel.getByRole('button', { name: 'Frameworks' })
  const addBookmarkButton = page.getByTestId('select-bar-in-my-prompts-to-activate-ai-button')
  const addAiPromptBarButton = page.getByTestId('add-ai-prompt-bar-button')
  const addAiPromptButtonDefault = page.getByTestId('add-ai-prompt-button-default')

  const loginRequiredText = 'Please login to use this feature.'

  //Is not logged in
  await isButtonWithTooltipEnabled(page, betterPromptButton, loginRequiredText)
  await isButtonWithTooltipEnabled(page, frameworksButton, loginRequiredText)
  await isButtonWithTooltipEnabled(page, addBookmarkButton, loginRequiredText)
  await isButtonWithTooltipEnabled(page, addAiPromptBarButton, loginRequiredText)
  await isButtonWithTooltipEnabled(page, addAiPromptButtonDefault, loginRequiredText)

  await login(page, user.superadmin)

  //Is logged in but no prompt text and no active bar
  await isButtonWithToolipDisabled(page, betterPromptButton, 'Enter prompt text to activate')
  await isButtonWithToolipDisabled(page, frameworksButton, 'Enter prompt text to activate')
  await isButtonWithToolipDisabled(page, addBookmarkButton, 'Select bar in My Prompts to activate')
  await isButtonWithTooltipEnabled(page, addAiPromptBarButton, 'Add a new prompt bar')
  await isButtonWithToolipDisabled(
    page,
    addAiPromptButtonDefault,
    'Enter prompt text and select bar in My Prompts to activate',
  )

  //Is logged in and has prompt text and no active bar
  await getPromptLocator(page).fill('running')

  await isButtonWithTooltipEnabled(page, betterPromptButton, 'Improve your prompt using AI')
  await isButtonWithTooltipEnabled(
    page,
    frameworksButton,
    'Apply different prompt frameworks using AI',
  )
  await isButtonWithToolipDisabled(page, addBookmarkButton, 'Select bar in My Prompts to activate')
  await isButtonWithTooltipEnabled(page, addAiPromptBarButton, 'Add a new prompt bar')
  await isButtonWithToolipDisabled(page, addAiPromptButtonDefault, 'Add new Prompt')

  //Is logged in and has prompt text and has active bar
  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Workout', { exact: true })).toBeVisible()
  await page.getByText('Workout', { exact: true }).click()
  await getTabLocator(page, 'Popular').click()

  await isButtonWithTooltipEnabled(page, betterPromptButton, 'Improve your prompt using AI')
  await isButtonWithTooltipEnabled(
    page,
    frameworksButton,
    'Apply different prompt frameworks using AI',
  )
  await isButtonWithTooltipEnabled(page, addBookmarkButton, 'Add new Bookmark')
  await isButtonWithTooltipEnabled(page, addAiPromptBarButton, 'Add a new prompt bar')
  await isButtonWithTooltipEnabled(page, addAiPromptButtonDefault, 'Add new Prompt')

  //test if button add bookmark is enabled in another tabs
  await getTabLocator(page, 'Library').click()
  await isButtonWithTooltipEnabled(page, addBookmarkButton, 'Add new Bookmark')
  await getTabLocator(page, 'Public').click()
  await isButtonWithTooltipEnabled(page, addBookmarkButton, 'Add new Bookmark')
  await getTabLocator(page, 'My Prompts').click()
  await isButtonWithTooltipEnabled(page, addBookmarkButton, 'Add new Bookmark')
  await getTabLocator(page, 'Favorites').click()
  await isButtonWithTooltipEnabled(page, addBookmarkButton, 'Add new Bookmark')

  //Is logged in and has active bar but no text in prompt textarea
  await getPromptLocator(page).fill('')

  await betterPromptButton.isDisabled()
  await frameworksButton.isDisabled()
  await addBookmarkButton.isEnabled()
  await addAiPromptBarButton.isEnabled()
  await addAiPromptButtonDefault.isDisabled()
})

test('super admin can change prompt bar language and see in Popular and Library tabs', async ({
  page,
}) => {
  await openTestPage(page)
  await login(page, user.superadmin)

  await page.getByTestId('interface-language-selector').click()
  await expect(
    page.getByTestId('interface-language-selector-option').getByText('Polish'),
  ).toBeVisible()
  await page.getByTestId('interface-language-selector-option').getByText('Polish').click()

  await getTabLocator(page, 'Popularne').click()
  await checkCurrentPromptBarsForLanguage(page)

  await getTabLocator(page, 'Biblioteka').click()
  await checkCurrentPromptBarsForLanguage(page)

  await page.getByText('Konto').click()

  await page.getByTestId('interface-language-selector').click()
  await expect(
    page.getByTestId('interface-language-selector-option').getByText('Angielski'),
  ).toBeVisible()
  await page.getByTestId('interface-language-selector-option').getByText('Angielski').click()

  await getTabLocator(page, 'My Prompts').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('Workout', { exact: true })).toBeVisible()
  await page.getByText('Workout', { exact: true }).click()

  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('language-select-dropdown').click()
  await page.getByTestId('language-select-option-in-edit-mode').getByText('Polish').click()
  await expect(page.getByText('Bar language updated', { exact: true })).toBeVisible()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'Popular').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await switchFilterLanguage(page, 'Polish')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).toBeVisible()
  await page.locator('.el-select-dropdown').getByText('Workout', { exact: true }).click()

  await getTabLocator(page, 'Library').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await switchFilterLanguage(page, 'Polish')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).toBeVisible()
  await page.locator('.el-select-dropdown').getByText('Workout', { exact: true }).click()

  //change to not visible in popular and library
  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(page.getByTestId('visible-in-popular-switch')).toBeVisible()
  await page.getByTestId('visible-in-popular-switch').click()
  await expect(page.getByText('Bar removed from popular', { exact: true })).toBeVisible()
  await expect(page.getByTestId('visible-in-library-switch')).toBeVisible()
  await page.getByTestId('visible-in-library-switch').click()
  await expect(page.getByText('Bar removed from library', { exact: true })).toBeVisible()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'Popular').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await switchFilterLanguage(page, 'Polish')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).not.toBeVisible()
  await switchFilterLanguage(page, 'English')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).not.toBeVisible()

  await getTabLocator(page, 'Library').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await switchFilterLanguage(page, 'Polish')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).not.toBeVisible()
  await switchFilterLanguage(page, 'English')
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).not.toBeVisible()

  await getTabLocator(page, 'My Prompts').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).toBeVisible()

  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('language-select-dropdown').click()
  await page.getByTestId('language-select-option-in-edit-mode').getByText('English').click()
  await expect(page.getByText('Bar language updated', { exact: true })).toBeVisible()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()
})
