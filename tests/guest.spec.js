import { expect, test } from './fixtures'
import { getTabLocator, openTestPage } from './functions'

test.beforeEach(async ({ page }) => {
  await openTestPage(page)
})

test('app has tabs', async ({ page }) => {
  await expect(getTabLocator(page, 'Popular')).toBeVisible()
  await expect(getTabLocator(page, 'Library')).toBeVisible()
  await expect(getTabLocator(page, 'Public')).toBeVisible()
  await expect(getTabLocator(page, 'My Prompts')).toBeVisible()
  await expect(getTabLocator(page, 'Favorites')).toBeVisible()
})

test('tabs have tooltips', async ({ page }) => {
  const popularTooltipText = 'Most popular prompts selected for best usability'
  await getTabLocator(page, 'Popular').hover()
  await expect(page.getByText(popularTooltipText)).toBeVisible()

  const myPromptsTooltipText = 'Custom prompts made by you'
  await getTabLocator(page, 'My Prompts').hover()
  await expect(page.getByText(myPromptsTooltipText)).toBeVisible()

  const favoritesTooltipText = 'Your favorite prompts'
  await getTabLocator(page, 'Favorites').hover()
  await expect(page.getByText(favoritesTooltipText)).toBeVisible()
})

test('tabs have content', async ({ page }) => {
  const noBarSelectedText = 'No bar selected. Please select bar from above.'
  const createAccountOrLoginText = 'Create an account or login to get access to current tab'

  // Popular
  await getTabLocator(page, 'Popular').click()
  await expect(page.getByText(noBarSelectedText)).toBeVisible()

  // Library
  await getTabLocator(page, 'Library').click()
  await expect(page.getByText(createAccountOrLoginText)).toBeVisible()
  await expect(page.locator('#pane-Library').getByRole('button', { name: 'Sign In' })).toBeVisible()

  // My Prompts
  await getTabLocator(page, 'My Prompts').click()
  await expect(page.getByText(createAccountOrLoginText)).toBeVisible()
  await expect(
    page.locator('[id="pane-My Prompts"]').getByRole('button', { name: 'Sign In' }),
  ).toBeVisible()

  // Favorites
  await getTabLocator(page, 'Favorites').click()
  await expect(page.getByText(createAccountOrLoginText)).toBeVisible()
  await expect(
    page.locator('#pane-Favorites').getByRole('button', { name: 'Sign In' }),
  ).toBeVisible()
})

test('small menu buttons should be visible', async ({ page }) => {
  await expect(page.getByRole('button', { name: 'Buy Subscription' })).toBeVisible()
  await expect(page.getByRole('button', { name: 'Sign In / Sign Up' })).toBeVisible()
})

test('tabs could be hidden and shown', async ({ page }) => {
  await getTabLocator(page, 'Popular').click()
  await page.locator('#pane-Popular').getByTestId('button-hide-menu').click()
  const element = await page.locator('.el-tabs__header.is-top')
  await expect(element.getByText('Favorites')).not.toBeVisible()

  await page.locator('#pane-Popular').getByTestId('button-show-menu').click()
  await expect(element.getByText('Favorites')).toBeVisible()
})
