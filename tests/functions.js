import { expect } from './fixtures'

export const testPageName = 'ChatGPT'

export const getPromptLocator = (page, pageName = testPageName) => {
  if (pageName === 'Perplexity') {
    return page.locator('#ask-input')
  }
  return page.locator('#prompt-textarea')
}

export const closeCookieBannerIfVisible = async (page, pageName = testPageName) => {
  const cookieLocator =
    pageName === 'Perplexity'
      ? page.getByLabel('Cookie Policy').getByText('Cookie Policy')
      : page.getByText('We use cookies')
  const closeButtonLocator =
    pageName === 'Perplexity'
      ? page.getByRole('button', { name: 'Necessary Cookies' })
      : page.getByRole('button', { name: 'Reject non-essential' })
  const isCookieBannerVisible = await cookieLocator.isVisible()
  if (isCookieBannerVisible) {
    await closeButtonLocator.click()
  }
}

export const login = async (page, user) => {
  await page.getByRole('button', { name: 'Sign In / Sign Up' }).click()
  await expect(page.locator('#tab-signIn')).toBeVisible()
  await page.getByTestId('sign-in-email-input').fill(user.email)
  await page.getByTestId('sign-in-password-input').fill(user.password)
  await page.getByTestId('sign-in-button').click()
  await expect(page.getByText('Hello ' + user.name)).toBeVisible()
  await page.getByRole('button', { name: 'Account' }).click()

  await closeCookieBannerIfVisible(page)
}

export const addDefaultPromptBar = async (page) => {
  await page.getByTestId('add-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })
}

export const addPromptBar = async (page, title) => {
  await page.getByTestId('add-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-input').fill(title)
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })
}

export const openChatGpt = async (page) => {
  await page.goto('https://chatgpt.com')

  const isLoginPageVisible = await page.getByText('Get started').isVisible()
  if (isLoginPageVisible) {
    await page.goto('https://chatgpt.com')
  }

  await expect(getTabLocator(page, 'Popular')).toBeVisible({ timeout: 10000 })

  const isPopupVisible = await page.getByText('Bring your ideas to life with images').isVisible()
  if (isPopupVisible) {
    await page.getByTestId('close-button').click()
  }
}

export const openPerplexity = async (page) => {
  await page.goto('https://www.perplexity.ai')

  await expect(getTabLocator(page, 'Popular')).toBeVisible({ timeout: 10000 })
}

export const openTestPage = async (page, pageName = testPageName) => {
  if (pageName === 'Perplexity') {
    await openPerplexity(page)
  } else if (pageName === 'ChatGPT') {
    await openChatGpt(page)
  }
}

export const getTabLocator = (page, tab) => {
  return page.getByRole('tab', { name: tab })
}

export const addPromptBarWithDestination = async (page, title, destination) => {
  await page.getByTestId('add-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-input').fill(title)
  await page.getByTestId('add-prompt-bar-destination-dialog-select').click({ force: true })
  const elementDestination = page.getByTestId('ai-option-destination')
  await elementDestination.getByText(destination).click()
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })
}

export const addPromptBarWithDefaultPrompts = async (page, title, index) => {
  await page.getByTestId('add-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-input').fill(title)
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()

  await expect(page.getByTestId('prompts-dropdown-edit-prompt-bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-folder-button').click()
  await expect(page.getByText('Add new folder')).toBeVisible()
  await page.getByTestId('add-prompt-bar-prompt-button').click()
  await expect(page.getByText('Add new Prompt')).toBeVisible()
  await page.getByTestId('add-prompt-bar-bookmark-button').click()
  await expect(page.getByText('Add new Bookmark')).toBeVisible()

  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')

  await promptBarEditLocator.getByText('New Prompt').dblclick({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(2)
    .fill('New_prompt_' + index)

  await promptBarEditLocator.getByText(testPageName).dblclick({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(3)
    .fill('New_bookmark_' + index)

  await promptBarEditLocator.getByText('New folder').dblclick({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(1)
    .fill('New_folder_' + index)
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })
}

export const addDuplicatedNameOfPromptBar = async (page) => {
  await page.getByTestId('add-prompt-bar-button').click()
  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()
  await expect(page.getByText('Name already exists')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-cancel-button').click()
}

export const removeDefaultPromptBar = async (page) => {
  await page.getByTestId('delete-prompt-bar-button').click()
  await expect(page.getByText('Are you sure to delete this?')).toBeVisible()
  await page.locator('.el-popconfirm').getByRole('button', { name: 'Remove' }).click()
}

export const removePromptBarByName = async (page, name) => {
  await page.getByTestId('my-prompt-bar-dropdown').click({ force: true })

  await expect(
    page
      .getByTestId('my-prompt-bar-dropdown')
      .locator('.el-select-dropdown')
      .getByText(name)
      .first(),
  ).toBeVisible()
  await page
    .getByTestId('my-prompt-bar-dropdown')
    .locator('.el-select-dropdown')
    .getByText(name)
    .first()
    .click({ force: true })

  await page.getByTestId('delete-prompt-bar-button').click()
  await expect(page.getByText('Are you sure to delete this?')).toBeVisible()
  await page.locator('.el-popconfirm').getByRole('button', { name: 'Remove' }).click()
  await expect(page.getByText('Are you sure to delete this?')).not.toBeVisible()
}

export const clickVisibleByTestId = async (page, testId) => {
  await expect(page.getByTestId(testId)).toBeVisible()
  await page.getByTestId(testId).click()
}

export const switchToPublicMode = async (page) => {
  await expect(page.getByTestId('is-public-switch')).not.toHaveClass(/is-checked/)
  await page.getByTestId('is-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
}

export const switchToPrivateMode = async (page) => {
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await page.getByTestId('is-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('is-public-switch')).not.toHaveClass(/is-checked/)
}

export const switchToAvailableInPublicSearch = async (page) => {
  await expect(page.getByTestId('available-in-public-switch')).not.toHaveClass(/is-checked/)
  await page.getByTestId('available-in-public-switch').click()
  await page.waitForTimeout(500)
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
}

export const switchToNotAvailableInPublicSearch = async (page) => {
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await page.getByTestId('available-in-public-switch').click()
  await page.waitForTimeout(500)
  await expect(page.getByTestId('available-in-public-switch')).not.toHaveClass(/is-checked/)
}

export const switchAllowOthersToEdit = async (page) => {
  await expect(page.getByTestId('not-editable-by-others-switch')).not.toHaveClass(/is-checked/)
  await page.getByTestId('not-editable-by-others-switch').click()
  await page.waitForTimeout(500)
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
}

export const switchToNotAllowOthersToEdit = async (page) => {
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
  await page.getByTestId('not-editable-by-others-switch').click()
  await page.waitForTimeout(500)
  await expect(page.getByTestId('not-editable-by-others-switch')).not.toHaveClass(/is-checked/)
}

export const editPromptAndAddDefaultPrompts = async (page) => {
  await page.getByTestId('edit-prompt-bar-button').click()

  await expect(page.getByTestId('prompts-dropdown-edit-prompt-bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-folder-button').click()
  await expect(page.getByText('Add new folder')).toBeVisible()
  await page.getByTestId('add-prompt-bar-prompt-button').click()
  await expect(page.getByText('Add new Prompt')).toBeVisible()
  await page.getByTestId('add-prompt-bar-bookmark-button').click()
  await expect(page.getByText('Add new Bookmark')).toBeVisible()

  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')

  await promptBarEditLocator.getByText('New Prompt').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(2).fill('New_prompt')

  await promptBarEditLocator.getByText(testPageName).dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(3).fill('New_bookmark')

  await promptBarEditLocator.getByText('New folder').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(1).fill('New_folder')
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })

  await page.getByTestId('close-edit-prompt-bar-dialog').click()
}

export const chceckVisibleDefaultPrompts = async (page) => {
  await getTabLocator(page, 'My Prompts').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('A_Prompt_Bar_title')).toBeVisible()
  await page.getByText('A_Prompt_Bar_title').click()

  await expect(page.getByText('New_folder')).toBeVisible()
  await expect(page.getByText('New_prompt')).toBeVisible()
  await expect(page.getByText('New_bookmark')).toBeVisible()
}

export const switchFilterLanguage = async (page, language) => {
  await page.getByTestId('language-filter-dropdown').click()
  await page.getByTestId('language-filter-dropdown').getByRole('option', { name: language }).click()
}

export const expectAvailableFeatures = async (locator, features = []) => {
  for (const feature of features) {
    const featureLocator = locator.getByText(feature)
    await expect(featureLocator).not.toHaveClass(/line-through/)
  }
}

export const expectUnavailableFeatures = async (locator, features = []) => {
  for (const feature of features) {
    const featureLocator = locator.getByText(feature)
    await expect(featureLocator).toHaveClass(/line-through/)
  }
}

export const hasButtonTooltip = async (page, button, message) => {
  await button.hover()
  await expect(page.getByText(message)).toBeVisible()
  await page.mouse.move(0, 0)
  await expect(page.getByText(message)).not.toBeVisible()
}

export const isButtonWithToolipDisabled = async (page, button, message) => {
  await expect(button).toBeDisabled()
  await hasButtonTooltip(page, button, message)
}

export const isButtonWithTooltipEnabled = async (page, button, message) => {
  await expect(button).toBeEnabled()
  await hasButtonTooltip(page, button, message)
}

export const checkCurrentPromptBarsForLanguage = async (page) => {
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(
    page
      .getByTestId('language-filter-dropdown')
      .locator('.el-select__placeholder')
      .getByText('Angielski'),
  ).toBeVisible()
  await expect(
    page.locator('.el-select-dropdown').getByText('Workout', { exact: true }),
  ).toBeVisible()
  await expect(
    page.locator('.el-select-dropdown').getByText('Music', { exact: true }),
  ).toBeVisible()

  await page.getByTestId('language-filter-dropdown').click()

  await expect(page.getByTestId('language-filter-option').getByText('Polski')).toBeVisible()
  await page.getByTestId('language-filter-option').getByText('Polski').click()
  await expect(page.getByText('Brak pasków promptów', { exact: true })).toBeVisible()
  await page.getByTestId('language-filter-dropdown').click()

  await page.getByTestId('language-filter-option').getByText('Angielski').click()
}
