import { user } from './accounts'
import { expect, test } from './fixtures'
import { getPromptLocator, login, openTestPage } from './functions'

test('user can use the Better Prompt feature', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.free)

  await expect(getPromptLocator(page)).toBeVisible()
  await getPromptLocator(page).fill('Napisz przepis na pierogi')
  await expect(page.getByRole('button', { name: 'Better Prompt' })).toBeVisible()
  await page.getByRole('button', { name: 'Better Prompt' }).click()
  await expect(page.getByText('Better Prompt:')).toBeVisible()
  await expect(page.getByTestId('better-prompt-result')).toBeVisible()
  await expect(page.getByTestId('better-prompt-result')).toContainText('functionName: betterPrompt')
  await page.getByRole('button', { name: 'Insert' }).click()
  await expect(getPromptLocator(page)).toContainText('functionName: betterPrompt')
  await page.pause()
})
