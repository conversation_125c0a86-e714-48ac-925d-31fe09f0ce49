import path from 'path'

import { chromium } from 'playwright'

import { user } from './accounts'
import { expect, test } from './fixtures'
import {
  addDefaultPromptBar,
  addDuplicatedNameOfPromptBar,
  addPromptBar,
  addPromptBarWithDefaultPrompts,
  addPromptBarWithDestination,
  chceckVisibleDefaultPrompts,
  clickVisibleByTestId,
  editPromptAndAddDefaultPrompts,
  expectAvailableFeatures,
  expectUnavailableFeatures,
  getPromptLocator,
  getTabLocator,
  login,
  openTestPage,
  removeDefaultPromptBar,
  removePromptBarByName,
  testPageName,
} from './functions'

test('user can add own prompt bar', async ({ page }) => {
  const date = new Date()

  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  const time = currentDate.slice(-8)
  await openTestPage(page)
  await login(page, user.premium)

  await getTabLocator(page, 'My Prompts').click()
  await addDefaultPromptBar(page)
  await addDuplicatedNameOfPromptBar(page)
  await removeDefaultPromptBar(page)

  // add prompt bar
  await getTabLocator(page, 'My Prompts').click()
  const addTooltipText = 'Add a new prompt bar'
  await page.getByTestId('add-prompt-bar-button').hover()
  await expect(page.getByText(addTooltipText)).toBeVisible()
  await page.getByTestId('add-prompt-bar-button').click()

  await expect(page.getByText('Add Prompt Bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-dialog-input').fill('Prompt_Bar_title_' + currentDate)

  await page.getByTestId('add-prompt-bar-destination-dialog-select').click({ force: true })

  const elementDestination = page.getByTestId('ai-option-destination')

  const expectedAIdestination = [
    'Chat GPT',
    'Google Gemini',
    'Microsoft Copilot',
    'Perplexity',
    'Claude',
    'Vespa',
    'Ideogram',
    'Custom',
  ]

  for (const item of expectedAIdestination) {
    await expect(elementDestination.getByText(item)).toBeVisible()
  }

  await elementDestination.getByText('Chat GPT').click({ force: true })
  await page.getByTestId('add-prompt-bar-dialog-confirmation-button').click()

  // edit prompt bar
  await expect(page.locator('#prompt-bar-name')).toBeVisible()

  // change title
  await page.getByTestId('prompt-bar-title-edit-button').click({ force: true })
  await page.locator('#prompt-bar-name input').fill('Prompt_Bar_title_changed_' + time)
  await page.getByTestId('prompt-bar-title-confirmation-button').click({ force: true })

  await expect(page.getByText('Bar prompts')).toBeVisible()
  await expect(page.getByText('Bar links')).toBeVisible()
  await expect(page.getByText('Copy prompts')).toBeVisible()

  await expect(page.getByTestId('prompts-dropdown-edit-prompt-bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-folder-button').hover()
  await expect(page.getByText('Add new folder')).toBeVisible()
  await page.getByTestId('add-prompt-bar-prompt-button').hover()
  await expect(page.getByText('Add new Prompt')).toBeVisible()
  await page.getByTestId('add-prompt-bar-bookmark-button').hover()
  await expect(page.getByText('Add new Bookmark')).toBeVisible()

  await page.getByTestId('add-prompt-bar-prompt-button').click({ force: true })
  await page.getByTestId('prompt-edit-text').click({ force: true })

  await page.getByTestId('prompt-edit-text').fill('prompt_contents_text_area_' + currentDate)

  await page.getByTestId('prompt-edit-desc').click({ force: true })
  await page
    .getByTestId('prompt-edit-desc-text-area')
    .fill('prompt_description_text_area_' + currentDate)

  await page.getByTestId('prompt-edit-chat-link').click({ force: true })
  await page.getByTestId('prompt-chat-link-select-ref').click({ force: true })

  await page.getByTestId('prompts-dropdown-edit-prompt-bar').click({ force: true })

  await clickVisibleByTestId(page, 'description-dropdown-edit-prompt-bar')

  await page
    .getByTestId('main-description-dropdown-edit-prompt-bar')
    .fill('prompt_bar_description_text_area ' + currentDate)

  // close description
  await clickVisibleByTestId(page, 'description-dropdown-edit-prompt-bar')

  await clickVisibleByTestId(page, 'chat-link-dropdown-edit-prompt-bar')

  const inputWebLinksLocator = page.getByTestId('input-websites-ai-destination')
  await expect(inputWebLinksLocator).toHaveAttribute('placeholder', 'https://chatgpt.com')

  // close destination
  await clickVisibleByTestId(page, 'chat-link-dropdown-edit-prompt-bar')

  await clickVisibleByTestId(page, 'is-public-switch')
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)

  await expect(page.getByText('Share link')).toBeVisible()
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await expect(page.getByText('Not editable by others')).toBeVisible()

  await clickVisibleByTestId(page, 'available-in-public-switch')
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()
  await clickVisibleByTestId(page, 'available-in-public-switch')
  await expect(page.getByTestId('available-in-public-switch')).not.toHaveClass(/is-checked/)

  await clickVisibleByTestId(page, 'not-editable-by-others-switch')
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Allow others to edit')).toBeVisible()
  await clickVisibleByTestId(page, 'not-editable-by-others-switch')
  await expect(page.getByTestId('not-editable-by-others-switch')).not.toHaveClass(/is-checked/)

  await clickVisibleByTestId(page, 'is-public-switch')
  await expect(page.getByTestId('is-public-switch')).not.toHaveClass(/is-checked/)
  await expect(page.getByText('Link access only, hidden from public search')).not.toBeVisible()
  await expect(page.getByText('Not editable by others')).not.toBeVisible()

  // switch to public mode
  await clickVisibleByTestId(page, 'is-public-switch')
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)

  // Bar links
  await page.getByText('Bar links').click({ force: true })

  await page.getByTestId('bar-links-add-new-link-button').hover()
  await expect(page.getByText('Add new Link')).toBeVisible()
  await page.getByTestId('bar-links-add-new-link-button').click({ force: true })
  await page.getByTestId('link-edit-input-in-bar-links').click({ force: true })
  await page
    .getByTestId('link-edit-input-in-bar-links')
    .fill('https://example.com/link/' + new Date().toISOString())

  // link video
  await page.getByTestId('bar-links-add-new-video-button').hover()
  await expect(page.getByText('Add video Link')).toBeVisible()
  await page.getByTestId('bar-links-add-new-video-button').click({ force: true })
  await page
    .getByTestId('link-edit-input-in-bar-links')
    .fill('https://example.com/video/' + currentDate)

  // popup
  await page.getByTestId('bar-links-add-new-popup-button').hover()
  await expect(page.getByText('Add new Popup')).toBeVisible()
  await page.getByTestId('bar-links-add-new-popup-button').click({ force: true })
  await page
    .getByTestId('link-edit-prompt-input-in-bar-links')
    .fill('bar_links_popup_contents_text_area_' + currentDate)

  await page.getByLabel('Bar links').getByText('New external link').click({ force: true })
  await page.getByLabel('Bar links').getByText('New popup video').click({ force: true })

  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  // check title
  await expect(
    page
      .getByTestId('my-prompt-bar-dropdown')
      .getByText('Prompt_Bar_title_changed_' + time)
      .first(),
  ).toBeVisible()
  await page
    .getByTestId('my-prompt-bar-dropdown')
    .getByText('Prompt_Bar_title_changed_' + time)
    .first()
    .click({ force: true })
  await expect(
    page
      .locator('.el-scrollbar__view.el-select-dropdown__list')
      .getByText('Prompt_Bar_title_changed_' + time)
      .first(),
  ).toBeVisible()
  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(
    page.locator('#prompt-bar-name').getByText('Prompt_Bar_title_changed_' + time),
  ).toBeVisible()
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  await page.getByText('New prompt').hover()
  await expect(page.getByText('Insert prompt: New Prompt')).toBeVisible()

  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')
  // change new prompt name
  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(
    page.locator('#prompt-bar-name').getByText('Prompt_Bar_title_changed_' + time),
  ).toBeVisible()
  await promptBarEditLocator.getByText('New Prompt').dblclick({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(1)
    .fill('New_prompt_' + currentDate)
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  // check visible in main view
  await expect(page.getByText('New_prompt_' + currentDate)).toBeVisible()

  // add new prompt
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('add-prompt-bar-prompt-button').click({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(2)
    .fill('New_prompt_2_' + currentDate)
  await promptBarEditLocator.getByText('New_prompt_' + currentDate, { exact: true }).click()
  await promptBarEditLocator.getByText('New_prompt_2_' + currentDate, { exact: true }).click()
  await page.getByTestId('prompt-edit-text').fill('prompt_2_contents_text_area_' + currentDate)
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  // check visible in main view
  await expect(page.getByText('New_prompt_2_' + currentDate)).toBeVisible()

  // add new folder
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByTestId('add-prompt-bar-folder-button').click({ force: true })
  await promptBarEditLocator
    .locator('input')
    .nth(3)
    .fill('New_folder_' + currentDate)
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })
  await expect(page.getByText('Prompt contents')).not.toBeVisible()

  // check visible in main view
  await expect(page.getByText('New_folder_' + currentDate)).toBeVisible()

  // add prompt, bookmark, folder in folder
  const newFolderLocator = promptBarEditLocator
    .getByText('New_folder_' + currentDate)
    .locator('..')
    .locator('..')
    .locator('button')
  await page.getByTestId('edit-prompt-bar-button').click()
  await promptBarEditLocator.click()
  await promptBarEditLocator.getByText('New_folder_' + currentDate).hover()
  await newFolderLocator.nth(1).click()
  if (!newFolderLocator.getByText('New folder', { exact: true }).isVisible()) {
    await newFolderLocator.nth(1).click()
  }
  if (newFolderLocator.nth(2).isVisible()) {
    await newFolderLocator.nth(2).click()
  }
  if (newFolderLocator.nth(3).isVisible()) {
    await newFolderLocator.nth(3).click()
  }
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  // check visible in main view
  await getPromptLocator(page).click({ force: true })
  await page.getByText('New_folder_' + currentDate).hover()
  await expect(
    page
      .locator('.prompt-recursiveMenu')
      .getByRole('menuitem', { name: 'New folder', exact: true }),
  ).toBeVisible()
  await expect(
    page.locator('.prompt-recursiveMenu').getByText('New prompt', { exact: true }),
  ).toBeVisible()
  await expect(
    page.locator('.prompt-recursiveMenu').getByText(testPageName, { exact: true }),
  ).toBeVisible()

  // fill prompt area by click own prompt
  await page.locator('.prompt-recursiveMenu').first().click({ force: true })
  await expect(
    getPromptLocator(page).getByText('prompt_contents_text_area_' + currentDate),
  ).toBeVisible()
  await page.locator('.prompt-recursiveMenu').nth(1).click({ force: true })
  await expect(
    getPromptLocator(page).getByText('prompt_2_contents_text_area_' + currentDate),
  ).toBeVisible()

  // prompt bar info
  await clickVisibleByTestId(page, 'prompt-bar-info-button')
  const promptBarInfoShareButtonsLocator = page.getByTestId('prompt-bar-info-share-buttons')
  const promptBarInfoShareButtonsParentLocator = promptBarInfoShareButtonsLocator
    .first()
    .locator('..')
  await expect(
    promptBarInfoShareButtonsParentLocator
      .locator('h3')
      .getByText('Prompt_Bar_title_changed_' + time),
  ).toBeVisible()
  await expect(
    promptBarInfoShareButtonsParentLocator
      .locator('div')
      .getByText('prompt_bar_description_text_area ' + currentDate),
  ).toBeVisible()
  await expect(promptBarInfoShareButtonsParentLocator.getByText('New external link')).toBeVisible()
  await expect(promptBarInfoShareButtonsParentLocator.getByText('New popup video')).toBeVisible()
  await expect(
    promptBarInfoShareButtonsParentLocator.getByText('New popup information'),
  ).toBeVisible()

  // prompt bar share
  await getPromptLocator(page).click({ force: true })
  await clickVisibleByTestId(page, 'prompt-bar-share-button')
  const sharePromptBarLocator = page.getByTestId('share-prompt-bar-dialog')
  await expect(sharePromptBarLocator.getByText('Prompt_Bar_title_changed_' + time)).toBeVisible()

  await expect(page.getByRole('button', { name: 'Set to Private' })).toBeVisible()
  await expect(
    sharePromptBarLocator.getByText('Link access only, hidden from public search'),
  ).toBeVisible()
  await expect(sharePromptBarLocator.getByText('Not editable by others')).toBeVisible()
  await expect(sharePromptBarLocator.getByRole('button', { name: 'Close' }).first()).toBeVisible()
  await sharePromptBarLocator.getByRole('button', { name: 'Close' }).first().click()

  // Edit prompt bar - copy prompts - test remove items
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.getByText('Copy prompts').click({ force: true })
  const copyPropmptLocator = page.locator('#pane-Copyprompts')
  await copyPropmptLocator.getByText('New_prompt_' + currentDate).click()
  await copyPropmptLocator.getByText('Remove').click()
  await copyPropmptLocator.getByText('New_prompt_2_' + currentDate).click()
  await copyPropmptLocator.getByText('Remove').click()
  await copyPropmptLocator.getByText('New_folder_' + currentDate).click()
  await copyPropmptLocator.getByText('Remove').click()
  await copyPropmptLocator.getByText('Save').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()
  await expect(
    page.getByText(
      'Bar is currently empty. Click Edit to add some prompts or Copy them from another bar',
    ),
  ).toBeVisible()
  await page
    .getByText(
      'Bar is currently empty. Click Edit to add some prompts or Copy them from another bar',
    )
    .getByText('edit')
    .click({ force: true })

  // change to private
  await expect(page.getByText('Private')).toBeVisible()
  await clickVisibleByTestId(page, 'is-public-switch')

  await page.getByTestId('close-edit-prompt-bar-dialog').click()
  await removePromptBarByName(page, 'Prompt_Bar_title_changed_' + time)
  await expect(page.getByText('My Prompts')).toBeVisible()
})

test('user can add own prompt bar to favorites', async ({ page }) => {
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)
  await page.getByText('My Prompts').click()
  await addPromptBar(page, 'Prompt_Bar_title_fav_' + currentDate)

  await page.getByTestId('toggle-favorite-button').click()
  await page.getByText('Favorites').click()

  // find favorite item
  await clickVisibleByTestId(page, 'my-prompt-bar-dropdown')

  await expect(
    page
      .getByTestId('my-prompt-bar-dropdown')
      .getByText('Prompt_Bar_title_fav_' + currentDate)
      .first(),
  ).toBeVisible()

  await page
    .getByTestId('my-prompt-bar-dropdown')
    .getByText('Prompt_Bar_title_fav_' + currentDate)
    .click()

  // remove from favorites
  await page.getByTestId('toggle-favorite-button').click()

  await expect(page.getByText('Bar is currently empty')).toBeVisible()

  await page.getByText('My prompts').click({ force: true })
  await removePromptBarByName(page, 'Prompt_Bar_title_fav_' + currentDate)
  await expect(page.getByText('My Prompts')).toBeVisible()
})

test('user can sort prompt bars by name, date, destination', async ({ page }) => {
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)
  await page.getByText('My Prompts').click()
  await addPromptBarWithDestination(page, 'A_Prompt_Bar_title_' + currentDate, 'Claude')
  await addPromptBarWithDestination(page, 'B_Prompt_Bar_title_' + currentDate, 'Vespa')
  await addPromptBarWithDestination(page, 'C_Prompt_Bar_title_' + currentDate, 'Chat GPT')
  await page.getByTestId('my-prompt-bar-dropdown').click()

  // sort tests
  const promptBarDropdownListLocator = page
    .getByTestId('my-prompt-bar-dropdown')
    .locator('.el-scrollbar li')
  const sortButtonsLocator = page
    .getByTestId('my-prompt-bar-dropdown')
    .locator('.el-button-group button')
  await expect(
    promptBarDropdownListLocator.first().getByText('A_Prompt_Bar_title_' + currentDate),
  ).toBeVisible()
  await sortButtonsLocator.nth(0).click()
  await expect(
    promptBarDropdownListLocator.first().getByText('C_Prompt_Bar_title_' + currentDate),
  ).toBeVisible()
  await sortButtonsLocator.nth(1).click()
  // await expect(promptBarDropdownListLocator.first().getByText('A_Prompt_Bar_title_' + currentDate)).toBeVisible()
  await sortButtonsLocator.nth(2).click()
  // await expect(promptBarDropdownListLocator.first().getByText('C_Prompt_Bar_title_' + currentDate)).toBeVisible()

  await removePromptBarByName(page, 'A_Prompt_Bar_title_' + currentDate)
  await removePromptBarByName(page, 'B_Prompt_Bar_title_' + currentDate)
  await removePromptBarByName(page, 'C_Prompt_Bar_title_' + currentDate)
})

test('user can copy prompts between two prompt bars', async ({ page }) => {
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)
  await page.getByText('My Prompts').click()
  await addPromptBarWithDefaultPrompts(page, '1_Prompt_Bar_title_' + currentDate, '1')
  await addPromptBarWithDefaultPrompts(page, '2_Prompt_Bar_title_' + currentDate, '2')

  await page.getByTestId('edit-prompt-bar-button').click()

  await page.getByText('Copy prompts').click({ force: true })

  await page.getByTestId('copy-prompt-select').click({ force: true })

  await expect(
    page.getByTestId('copy-prompt-select-option').getByText('1_Prompt_Bar_title_' + currentDate),
  ).toBeVisible()
  await page
    .getByTestId('copy-prompt-select-option')
    .getByText('1_Prompt_Bar_title_' + currentDate)
    .click({ force: true })

  await expect(
    page
      .getByTestId('copy-prompt-select')
      .getByText('1_Prompt_Bar_title_' + currentDate)
      .first(),
  ).toBeVisible()
  await expect(page.getByTestId('copy-prompt-tree').getByText('New_prompt_1')).toBeVisible()
  await page.getByTestId('copy-prompt-tree').getByText('New_prompt_1').click({ force: true })
  await expect(
    page
      .getByTestId('copy-prompt-select')
      .getByText('1_Prompt_Bar_title_' + currentDate)
      .first(),
  ).toBeVisible()
  await expect(page.getByTestId('copy-prompt-tree').getByText('New_folder_1')).toBeVisible()
  await page.getByTestId('copy-prompt-tree').getByText('New_folder_1').click({ force: true })
  await expect(
    page
      .getByTestId('copy-prompt-select')
      .getByText('1_Prompt_Bar_title_' + currentDate)
      .first(),
  ).toBeVisible()
  await expect(page.getByTestId('copy-prompt-tree').getByText('New_bookmark_1')).toBeVisible()
  await page.getByTestId('copy-prompt-tree').getByText('New_bookmark_1').click({ force: true })
  await expect(
    page
      .getByTestId('copy-prompt-select')
      .getByText('1_Prompt_Bar_title_' + currentDate)
      .first(),
  ).toBeVisible()
  await page.getByText('All selected').click({ force: true })
  await page.getByText('Save').click({ force: true })

  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  await page.waitForTimeout(5000)
  await removePromptBarByName(page, '2_Prompt_Bar_title_' + currentDate)
  await removePromptBarByName(page, '1_Prompt_Bar_title_' + currentDate)
})

test('user can make prompt bars public and private', async ({ page }) => {
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)
  await page.getByText('My Prompts').click()
  await addPromptBarWithDestination(page, 'A_Prompt_Bar_title_' + currentDate, 'Claude')
  await page.getByTestId('edit-prompt-bar-button').click()

  // makes prompt public
  await clickVisibleByTestId(page, 'is-public-switch')
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await clickVisibleByTestId(page, 'available-in-public-switch')
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()

  await clickVisibleByTestId(page, 'not-editable-by-others-switch')
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Allow others to edit')).toBeVisible()

  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'Public').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()

  await expect(page.getByText('A_Prompt_Bar_title_' + currentDate)).toBeVisible()
  await page.getByText('A_Prompt_Bar_title_' + currentDate).click({ force: true })
  // can share, add to favorites, copy
  // await page.getByTestId('prompt-bar-info-share-buttons').locator('button').nth(1).hover()
  // await expect(page.getByText('Share prompt bar')).toBeVisible()
  // await page.getByTestId('toggle-favorite-button').hover()
  // await expect(page.getByText('Toggle favorite')).toBeVisible()
  // await page.getByTestId('copy-prompt-button').hover()
  // await expect(page.getByText('Copy to your bar')).toBeVisible()

  await getTabLocator(page, 'My Prompts').click()

  // makes prompt private
  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()
  await page.getByTestId('available-in-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await page.getByTestId('is-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('is-public-switch')).not.toHaveClass(/is-checked/)
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'Public').click()
  await expect(page.getByText('Bar is currently empty')).toBeVisible()
  await getTabLocator(page, 'My Prompts').click()
  await removePromptBarByName(page, 'A_Prompt_Bar_title_' + currentDate)
})

test('user can make prompt bar public with prompt, bookmark, folder and another user can see it in real time', async ({
  page,
}) => {
  //user 1 make prompt bar public with prompt, bookmark, folder
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)

  await getTabLocator(page, 'My Prompts').click()
  await addPromptBarWithDestination(page, 'A_Prompt_Bar_title', 'Chat GPT')
  await page.getByTestId('edit-prompt-bar-button').click()

  await expect(page.getByTestId('prompts-dropdown-edit-prompt-bar')).toBeVisible()
  await page.getByTestId('add-prompt-bar-folder-button').click()
  await expect(page.getByText('Add new folder')).toBeVisible()
  await page.getByTestId('add-prompt-bar-prompt-button').click()
  await expect(page.getByText('Add new Prompt')).toBeVisible()
  await page.getByTestId('add-prompt-bar-bookmark-button').click()
  await expect(page.getByText('Add new Bookmark')).toBeVisible()

  const promptBarEditLocator = page.getByTestId('prompts-dropdown-edit-prompt-bar')

  await promptBarEditLocator.getByText('New Prompt').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(2).fill('New_prompt')

  await promptBarEditLocator.getByText(testPageName).dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(3).fill('New_bookmark')

  await promptBarEditLocator.getByText('New folder').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(1).fill('New_folder')
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })

  //makes prompt public
  await page.getByTestId('is-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await page.getByTestId('available-in-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()
  await clickVisibleByTestId(page, 'not-editable-by-others-switch')
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Allow others to edit')).toBeVisible()

  const shareLinkInput = await page.getByTestId('share-link-input').getAttribute('value')

  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await expect(page.getByText('New_folder')).toBeVisible()
  await expect(page.getByText('New_prompt')).toBeVisible()
  await expect(page.getByText('New_bookmark')).toBeVisible()

  await page.getByText('Public').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('A_Prompt_Bar_title')).toBeVisible()
  await page.getByText('A_Prompt_Bar_title').click({ force: true })

  //user 2 can see public prompt bar

  const pathToExtension = path.join(process.cwd(), 'AI-Prompt-Lab')

  const context = await chromium.launchPersistentContext('', {
    channel: 'chromium',
    args: [`--disable-extensions-except=${pathToExtension}`, `--load-extension=${pathToExtension}`],
  })
  const page2 = await context.newPage()
  await openTestPage(page2)
  await login(page2, user.standard)

  const isCookieBannerVisible2 = await page2.getByText('We use cookies').isVisible()
  if (isCookieBannerVisible2) {
    await page2.getByText('Reject non-essential').click()
  }
  await getTabLocator(page2, 'Public').click()
  await page2.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page2.getByText('A_Prompt_Bar_title')).toBeVisible()
  await page2.getByText('A_Prompt_Bar_title').click()
  // temporary comment
  // await expect(page2.getByText('New_folder')).toBeVisible()
  // await expect(page2.getByText('New_prompt')).toBeVisible()
  // await expect(page2.getByText('New_bookmark')).toBeVisible()

  //change prompt name
  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('edit-prompt-bar-button').click()

  await promptBarEditLocator.getByText('New_prompt').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(2).fill('X_New_prompt')

  await promptBarEditLocator.getByText('New_bookmark').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(3).fill('X_New_bookmark')

  await promptBarEditLocator.getByText('New_folder').dblclick({ force: true })
  await promptBarEditLocator.locator('input').nth(1).fill('X_New_folder')
  await promptBarEditLocator.locator('button').first().click({ force: true })
  await promptBarEditLocator.click({ force: true })
  await page.getByTestId('close-edit-prompt-bar-dialog').click({ force: true })

  // temporary comment
  // await expect(page2.getByText('X_New_folder')).toBeVisible()
  // await expect(page2.getByText('X_New_prompt')).toBeVisible()
  // await expect(page2.getByText('X_New_bookmark')).toBeVisible()
  //edit by user 2
  const promptBarEditLocator2 = page2.getByTestId('prompts-dropdown-edit-prompt-bar')
  await page2.getByRole('button', { name: 'Edit' }).click()
  await promptBarEditLocator2.getByText('X_New_prompt').dblclick({ force: true })
  await promptBarEditLocator2.locator('input').nth(2).fill('Y_New_prompt')

  await promptBarEditLocator2.getByText('X_New_bookmark').dblclick({ force: true })
  await promptBarEditLocator2.locator('input').nth(3).fill('Y_New_bookmark')

  await promptBarEditLocator2.getByText('X_New_folder').dblclick({ force: true })
  await promptBarEditLocator2.locator('input').nth(1).fill('Y_New_folder')
  await promptBarEditLocator2.locator('button').first().click({ force: true })

  await page2.getByText('Close').click()

  await expect(page2.getByText('Y_New_folder')).toBeVisible()
  await expect(page2.getByText('Y_New_prompt')).toBeVisible()
  await expect(page2.getByText('Y_New_bookmark')).toBeVisible()

  await expect(page.getByText('Y_New_folder')).toBeVisible()
  await expect(page.getByText('Y_New_prompt')).toBeVisible()
  await expect(page.getByText('Y_New_bookmark')).toBeVisible()

  await page2.goto(shareLinkInput.toString())
  await expect(page2.getByText('Open in AI PromptLab')).toBeVisible()
  await page2.getByText('Open in AI PromptLab').click({ force: true })
  // temporary comment
  // await page2.waitForTimeout(10000)
  // await expect(page2.getByText('Y_New_folder')).toBeVisible()
  // await expect(page2.getByText('Y_New_prompt')).toBeVisible()
  // await expect(page2.getByText('Y_New_bookmark')).toBeVisible()
  await context.close()
  //change to private
  await page.getByTestId('edit-prompt-bar-button').click()
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()
  await page.getByTestId('available-in-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await page.getByTestId('is-public-switch').click({ force: true })
  await page.waitForTimeout(500)
  await expect(page.getByTestId('is-public-switch')).not.toHaveClass(/is-checked/)
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'My Prompts').click()
  await removePromptBarByName(page, 'A_Prompt_Bar_title')
})

test.skip('user can switch between different AI destinations', async ({ page }) => {
  //user 1 make prompt bar public with prompt, bookmark, folder
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)

  await getTabLocator(page, 'My Prompts').click()
  await addPromptBarWithDestination(page, 'A_Prompt_Bar_title', 'Claude')
  await editPromptAndAddDefaultPrompts(page)

  await expect(page.getByText('New_folder')).toBeVisible()
  await expect(page.getByText('New_prompt')).toBeVisible()
  await expect(page.getByText('New_bookmark')).toBeVisible()

  await page.goto('https://www.perplexity.ai/')
  await chceckVisibleDefaultPrompts(page)
  await page.goto('https://vespa-engine-colpali-vespa-visual-retrieval.hf.space')
  await chceckVisibleDefaultPrompts(page)
  await page.goto('https://claude.ai')
  const isClaudeCookieBannerVisible = await page.getByText('Reject all Cookies').isVisible()
  if (isClaudeCookieBannerVisible) {
    await page.getByText('Reject all Cookies').click()
  }
  await chceckVisibleDefaultPrompts(page)

  // need login
  // await page.goto('https://gemini.google.com')
  // const isGeminiCookieBannerVisible = await page.getByText('Reject all').nth(1).isVisible()
  // if (isGeminiCookieBannerVisible) {
  //   await page.getByText('Reject all').nth(1).click()
  // }
  // await chceckVisibleDefaultPrompts(page)

  // await page.goto('https://ideogram.ai')
  // const isIdeogramCookieBannerVisible = await page.getByText('We use analytics tools').isVisible()
  // if (isIdeogramCookieBannerVisible) {
  //   await page.getByText('Decline').click()
  // }
  // await chceckVisibleDefaultPrompts(page)

  // await page.goto('https://copilot.microsoft.com')
  // await chceckVisibleDefaultPrompts(page)
})

test('user can open copy prompt dialog using copy button', async ({ page }) => {
  const date = new Date()
  const currentDate = date.toISOString().slice(0, 19).replace(/:/g, '-')
  await openTestPage(page)
  await login(page, user.premium)
  await page.getByText('My Prompts').click()
  await addPromptBarWithDestination(page, 'A_Prompt_Bar_title_' + currentDate, 'Claude')
  await page.getByTestId('edit-prompt-bar-button').click()
  await clickVisibleByTestId(page, 'is-public-switch')
  await expect(page.getByTestId('is-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Link access only, hidden from public search')).toBeVisible()
  await clickVisibleByTestId(page, 'available-in-public-switch')
  await expect(page.getByTestId('available-in-public-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Available in Public bars')).toBeVisible()

  await clickVisibleByTestId(page, 'not-editable-by-others-switch')
  await expect(page.getByTestId('not-editable-by-others-switch')).toHaveClass(/is-checked/)
  await expect(page.getByText('Allow others to edit')).toBeVisible()

  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await getTabLocator(page, 'Public').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()

  await page.getByText('A_Prompt_Bar_title_' + currentDate).click()
  await page.getByTestId('copy-prompt-button').click()
  await expect(page.getByText('Copy from right')).toBeVisible()
})

test('user can use frameworks', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.premium)

  await expect(getPromptLocator(page)).toBeVisible()
  await getPromptLocator(page).fill('workout')

  const balanceTextBefore = await page.getByTestId('credits-balance').innerText()

  await expect(page.getByText('Frameworks')).toBeVisible()
  await page.getByText('Frameworks').click()

  await page.waitForTimeout(5000)
  const balanceTextAfter = await page.getByTestId('credits-balance').innerText()
  expect(balanceTextAfter).toBe((Number(balanceTextBefore) - 1).toString())
})

test('user can use subscription dialog', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.premium)

  await page.getByText('Subscription').click()
  await expect(page.getByText('Subscription - AI Prompt Lab')).toBeVisible()
  const freeLocator = page.getByTestId('subscription-card-Free')

  await expect(freeLocator.getByText('Free')).toBeVisible()

  await expectAvailableFeatures(freeLocator, [
    '5 Prompt bars',
    'Unlimited prompts per bar',
    '10 Credits per month',
    'Access to library of prompts',
  ])
  await expectUnavailableFeatures(freeLocator, [
    'Copy prompts',
    'Prompt folders',
    'Access to premium prompts',
  ])
  await expect(freeLocator.getByText('$0 / Month')).toBeVisible()

  const standardLocator = page.getByTestId('subscription-card-Standard')

  await expect(standardLocator.getByText('Standard')).toBeVisible()

  await expectAvailableFeatures(standardLocator, [
    'Unlimited prompt bars',
    'Unlimited prompts per bar',
    '200 Credits per month',
    'Access to library of prompts',
    'Prompt folders',
    'Copy prompts',
    'Access to premium prompts',
  ])
  await expect(standardLocator.getByText('$10 / Month')).toBeVisible()

  const premiumLocator = page.getByTestId('subscription-card-Premium')

  await expect(premiumLocator.getByText('Premium').first()).toBeVisible()

  await expectAvailableFeatures(premiumLocator, [
    'Unlimited prompt bars',
    'Unlimited prompts per bar',
    '1000 Credits per month',
    'Access to library of prompts',
    'Prompt folders',
    'Copy prompts',
    'Access to premium prompts',
  ])
  await expect(premiumLocator.getByText('$20 / Month')).toBeVisible()

  const teamLocator = page.getByTestId('subscription-card-Team')

  await expect(teamLocator.getByText('Team').first()).toBeVisible()

  await expectAvailableFeatures(teamLocator, [
    'Unlimited prompt bars',
    'Unlimited prompts per bar',
    '1000 Credits per month',
    'Access to library of prompts',
    'Prompt folders',
    'Copy prompts',
    'Access to premium prompts',
    'Manage Team',
  ])
  await expect(teamLocator.getByText('$40 / Month')).toBeVisible()

  await page.getByTestId('subscription-interval-button-year').click()
  await expect(teamLocator.getByText('$400 / Year')).toBeVisible()
  await expect(premiumLocator.getByText('$200 / Year')).toBeVisible()
  //await expect(standardLocator.getByText('$100Annual')).toBeVisible()
  await expect(freeLocator.getByText('$0 / Year')).toBeVisible()

  const subscriptionCardLocator = page.locator('.subscription')
  await expect(subscriptionCardLocator).toBeVisible()

  await subscriptionCardLocator.getByTestId('subscription-coupon-input').fill('FREE6MONTH-PRE')
  await subscriptionCardLocator.getByText('Apply').click()
  await expect(subscriptionCardLocator.getByText('Coupon code already used')).toBeVisible()

  await subscriptionCardLocator.getByText('History').click()
  await expect(subscriptionCardLocator.getByText('Start')).toBeVisible()
  await expect(subscriptionCardLocator.getByText('End')).toBeVisible()
  await expect(subscriptionCardLocator.getByText('Price')).toBeVisible()
  await expect(subscriptionCardLocator.getByText('No data')).toBeVisible()

  await subscriptionCardLocator.getByText('Close').click()
})

test.skip('premium user can copy own prompt bar', async ({ page }) => {
  await openTestPage(page)
  await login(page, user.premium)

  await getTabLocator(page, 'My Prompts').click()
  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(
    page.locator('.el-select-dropdown').getByText('A_Prompt_Bar_title', { exact: true }),
  ).toBeVisible()
  await page.locator('.el-select-dropdown').getByText('A_Prompt_Bar_title', { exact: true }).click()
  await page.getByTestId('edit-prompt-bar-button').click()
  await page.locator('#tab-Translation').click()

  await page.getByTestId('copy-my-prompt-bar-button').click()
  await page.getByTestId('close-edit-prompt-bar-dialog').click()

  await page.getByTestId('my-prompt-bar-dropdown').click()
  await expect(page.getByText('A_Prompt_Bar_title COPY', { exact: true })).toBeVisible()
  await page.getByText('A_Prompt_Bar_title COPY', { exact: true }).click()
})
