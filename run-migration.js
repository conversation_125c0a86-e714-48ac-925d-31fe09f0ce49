#!/usr/bin/env node

/**
 * Simple Node.js script to run the sites migration
 * This script can be run directly without needing a browser environment
 */
import { readFileSync } from 'fs'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Read the sites data directly from the file
const sitesFilePath = join(__dirname, 'src/content/sitesWithTags.js')

console.log('🚀 AI Prompt Lab - Sites Migration Tool')
console.log('=====================================')

try {
  // Read and parse the sites data file
  const sitesFileContent = readFileSync(sitesFilePath, 'utf-8')

  // Extract the data using regex (simple approach for this script)
  const sitesMatch = sitesFileContent.match(/export const sitesWithTags = (\[[\s\S]*?\])/m)
  const tagsMatch = sitesFileContent.match(/export const allTags = (\[[\s\S]*?\])/m)

  if (!sitesMatch || !tagsMatch) {
    throw new Error('Could not parse sites data from file')
  }

  // Evaluate the arrays (note: this is safe since we control the source)
  const sitesWithTags = eval(sitesMatch[1])
  const allTags = eval(tagsMatch[1])

  console.log('📊 Data Summary:')
  console.log(`   Sites: ${sitesWithTags.length}`)
  console.log(`   Tags: ${allTags.length}`)
  console.log('')

  // Create the migration data structure
  const migrationData = {
    sitesWithTags,
    allTags,
    lastUpdated: new Date().toISOString(),
    version: '1.0.0',
    migratedAt: new Date().toISOString(),
    totalSites: sitesWithTags.length,
    totalTags: allTags.length,
    source: 'node-migration-script',
  }

  console.log('📋 Migration Data Structure:')
  console.log('   ✓ sitesWithTags: Array of site objects')
  console.log('   ✓ allTags: Array of tag strings')
  console.log('   ✓ lastUpdated: ISO timestamp')
  console.log('   ✓ version: Data version')
  console.log('   ✓ migratedAt: Migration timestamp')
  console.log('   ✓ totalSites: Number of sites')
  console.log('   ✓ totalTags: Number of tags')
  console.log('   ✓ source: Migration source identifier')
  console.log('')

  console.log('📝 Sample Sites:')
  sitesWithTags.slice(0, 3).forEach((site, index) => {
    console.log(`   ${index + 1}. ${site.name} (${site.id})`)
    console.log(`      URL: ${site.url}`)
    console.log(`      Tags: ${site.tags.join(', ')}`)
  })
  console.log('')

  console.log('🏷️  All Tags:')
  console.log(`   ${allTags.join(', ')}`)
  console.log('')

  console.log('✅ Data validation successful!')
  console.log('')
  console.log('📤 To upload this data to Firestore:')
  console.log('   1. Open your browser and navigate to your app')
  console.log('   2. Open browser console (F12)')
  console.log('   3. Run: migrateSitesToFirestore()')
  console.log('   4. Or use the test page: open test-migration.html')
  console.log('')
  console.log('🔍 To verify the migration:')
  console.log('   1. Run: verifyMigration()')
  console.log('   2. Check the browser console for results')
  console.log('')
  console.log('📋 Migration data is ready!')
} catch (error) {
  console.error('❌ Error:', error.message)
  console.error('')
  console.error('💡 Troubleshooting:')
  console.error('   1. Make sure the sitesWithTags.js file exists')
  console.error('   2. Check that the file has the correct export format')
  console.error('   3. Verify the file path is correct')
  process.exit(1)
}
