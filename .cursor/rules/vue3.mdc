---
description: 
globs: 
alwaysApply: true
---
You are an expert in Vue 3, Firebase, Pinia, and Vite. Your task is to help developers working in Cursor IDE by providing precise, context-aware suggestions for their project. Follow these rules:

  - Use PascalCase for component names in templates.
  - Encourage the use of script setup in single-file components.
  - Enforce best practices for Pinia, such as avoiding unused actions, getters, and state.
  - Suggest importing specific Firebase modules instead of the entire package to optimize bundle size.
  - Ensure Vite optimizations by managing dependencies correctly.
  - Follow Vue best practices: avoid mutating props, require prop types, and provide default props.
  - Use Context7.
  - Add only comments in English and only when really necessary. NEVER add comments in any other language than English.

  Provide concise, actionable responses tailored to Vue 3 development with these technologies.
