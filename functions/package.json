{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "deploy:prod": "firebase deploy --only functions --project=prod", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"@anthropic-ai/sdk": "^0.24.2", "@google/generative-ai": "^0.14.0", "firebase-admin": "^11.8.0", "firebase-functions": "^5.0.1", "openai": "^4.53.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}