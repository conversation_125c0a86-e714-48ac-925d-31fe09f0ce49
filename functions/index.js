const functions = require('firebase-functions')
const { onCustomEventPublished } = require('firebase-functions/v2/eventarc')
const admin = require('firebase-admin')
const { FieldValue, Timestamp } = require('firebase-admin/firestore')
const { onRequest, onCall } = require('firebase-functions/v2/https')
const { GoogleGenerativeAI } = require('@google/generative-ai')
const { Anthropic } = require('@anthropic-ai/sdk')
const OpenAI = require('openai')

// Check if we're running in the Firebase emulator
const useEmulator = process.env.FUNCTIONS_EMULATOR === 'true'

console.log('FUNCTIONS_EMULATOR_index:', process.env.FUNCTIONS_EMULATOR)
console.log('useEmulator:', useEmulator)

if (useEmulator) {
  console.log('Initializing app for Functions emulator')
  admin.initializeApp({
    projectId: 'chat-gpt-assistant-3671a',
  })

  // Konfiguracja dla emulatora funkcji

  console.log('Functions emulator configuration set')
} else {
  console.log('Initializing app for production')
  admin.initializeApp()
}

const firestore = admin.firestore()

const MAX_CREDITS = {
  FREE: 10,
  STANDARD: 200,
  PREMIUM: 1000,
}

function getMaxCreditsByPlan(planName) {
  switch (planName) {
    case 'Standard':
      return MAX_CREDITS.STANDARD
    case 'Premium':
    case 'Team':
      return MAX_CREDITS.PREMIUM
    default:
      return MAX_CREDITS.FREE
  }
}

exports.aggregateLibraryTags = functions.firestore
  .document('bar_info/library')
  .onWrite(async (change, context) => {
    console.log('Function triggered')

    const before = change.before.data()
    const after = change.after.data()

    // Check if document was deleted
    if (!after) {
      console.log('Document was deleted')
      return null
    }

    if (!before) {
      console.log('New document was created')
    } else {
      console.log('Document was updated')
    }

    console.log('Before data:', before ? JSON.stringify(before) : 'No document')
    console.log('After data:', JSON.stringify(after))

    const tagsSet = new Set()

    try {
      // Process the library document
      Object.values(after).forEach((data) => {
        if (data && data.tags && Array.isArray(data.tags)) {
          data.tags.forEach((tag) => {
            tagsSet.add(tag)
            console.log(`Added tag: ${tag}`)
          })
        }
      })

      const tagsArray = Array.from(tagsSet)
      console.log('Final aggregated tags:', tagsArray)

      // Check if tags have actually changed
      const settingsRef = admin.firestore().collection('settings').doc('library_tags')
      const currentTags = (await settingsRef.get()).data()?.tags || []

      if (JSON.stringify(currentTags.sort()) !== JSON.stringify(tagsArray.sort())) {
        await settingsRef.set({ tags: tagsArray })
        console.log('Tags saved to settings/library_tags')
      } else {
        console.log('Tags unchanged, skipping update')
      }

      return null
    } catch (error) {
      console.error('Error in aggregateLibraryTags:', error)
      throw new functions.https.HttpsError('internal', 'Error aggregating tags', error)
    }
  })
exports.aggregateLibraryTags_pl = functions.firestore
  .document('bar_info/library_pl')
  .onWrite(async (change, context) => {
    console.log('Function triggered')

    const before = change.before.data()
    const after = change.after.data()

    // Check if document was deleted
    if (!after) {
      console.log('Document was deleted')
      return null
    }

    if (!before) {
      console.log('New document was created')
    } else {
      console.log('Document was updated')
    }

    console.log('Before data:', before ? JSON.stringify(before) : 'No document')
    console.log('After data:', JSON.stringify(after))

    const tagsSet = new Set()

    try {
      // Process the library document
      Object.values(after).forEach((data) => {
        if (data && data.tags && Array.isArray(data.tags)) {
          data.tags.forEach((tag) => {
            tagsSet.add(tag)
            console.log(`Added tag: ${tag}`)
          })
        }
      })

      const tagsArray = Array.from(tagsSet)
      console.log('Final aggregated tags:', tagsArray)

      // Check if tags have actually changed
      const settingsRef = admin.firestore().collection('settings').doc('library_tags_pl')
      const currentTags = (await settingsRef.get()).data()?.tags || []

      if (JSON.stringify(currentTags.sort()) !== JSON.stringify(tagsArray.sort())) {
        await settingsRef.set({ tags: tagsArray })
        console.log('Tags saved to settings/library_tags_pl')
      } else {
        console.log('Tags unchanged, skipping update')
      }

      return null
    } catch (error) {
      console.error('Error in aggregateLibraryTags:', error)
      throw new functions.https.HttpsError('internal', 'Error aggregating tags', error)
    }
  })

exports.createUser = functions.auth.user().onCreate(async (user) => {
  console.log('createUser', user)
  const email = user.email
  const name = user.displayName || email.split('@')[0] || 'Anonymous'

  const userSnapshots = await firestore
    .collection('users')
    .where('subscription.maxMembers', '>', 1)
    .where('subscription.members', 'array-contains', email)
    .get()

  const hasTeamSubscription = userSnapshots.docs?.length > 0
  console.log('hasTeamSubscription', email, hasTeamSubscription)
  const maxMembers = hasTeamSubscription ? 1 : 0
  const subscriptionPlan = hasTeamSubscription ? 'Team' : ''

  console.log(`Creating document for user ${user.uid}`)
  await firestore
    .collection('users')
    .doc(user.uid)
    .set({
      createdAt: FieldValue.serverTimestamp(),
      email,
      name,
      creditsOwned: getMaxCreditsByPlan(subscriptionPlan),
      hasTeamSubscription,
      subscription: {
        plan: subscriptionPlan,
        maxMembers,
        members: [],
      },
      ...(subscriptionPlan === 'Team' && { teams: userSnapshots.docs.map((doc) => doc.ref) }),
    })
})

exports.deleteUser = functions.auth.user().onDelete(async (user) => {
  console.log(`Deleting document for user ${user.uid}`)
  await firestore.collection('users').doc(user.uid).delete()
})

exports.onCustomerSubscriptionDeleted = onCustomEventPublished(
  {
    eventType: 'com.stripe.v1.customer.subscription.deleted',
    maxInstances: 10,
  },
  async (event) => {
    console.log('onCustomerSubscriptionDeleted', event)
    const eventData = event.data

    const customerId = eventData.customer

    if (eventData.object === 'subscription') {
      const customerSnapshots = await firestore
        .collection('customers')
        .where('stripeId', '==', customerId)
        .get()

      if (customerSnapshots.empty) {
        return
      }

      const customer = customerSnapshots.docs[0].data()

      const userSnapshots = await firestore
        .collection('users')
        .where('email', '==', customer.email)
        .get()

      if (userSnapshots.empty) {
        return
      }

      const updateObject = {
        creditsOwned: MAX_CREDITS.FREE,
        subscription: { plan: '', maxMembers: 0, members: [] },
        ...(product.name === 'Team' && { teams: [userSnapshots.docs[0].ref] }),
      }

      const result = await userSnapshots.docs[0].ref.update(updateObject)
      console.log('Result of removing subscription for user', customer.email, result)
    }
  },
)

exports.onUserDocumentChange = functions.firestore
  .document('users/{userId}')
  .onUpdate(async (change, context) => {
    const newValue = change.after.data()
    const previousValue = change.before.data()

    console.log('onUserDocumentChange:', newValue, context)

    await handleMaxMembersChange(newValue, previousValue)
    await handleCouponChange(newValue, previousValue)
  })

async function handleMaxMembersChange(newValue, previousValue) {
  if (
    newValue.subscription?.maxMembers === 0 &&
    previousValue.subscription?.maxMembers > 1 &&
    newValue.subscription?.maxMembers !== previousValue.subscription?.maxMembers
  ) {
    console.log('Max members resetted to 0. Removing team subscription for all team members')

    const teamMembers = previousValue.subscription?.members || []

    if (!teamMembers.length) {
      return
    }

    const userSnapshots = await firestore
      .collection('users')
      .where('email', 'in', teamMembers)
      .get()

    if (userSnapshots.empty) {
      return
    }

    userSnapshots.forEach((user) => {
      user.ref.update({
        creditsOwned: MAX_CREDITS.FREE,
        hasTeamSubscription: false,
        subscription: { plan: '', maxMembers: 0, members: [] },
      })
    })
  }
}

async function handleCouponChange(newValue, previousValue) {
  if (!newValue.coupon || previousValue.coupon) {
    return
  }
  const codesDoc = await admin.firestore().collection('settings').doc('codes').get()

  const codes = codesDoc.data() || {}
  const code = codes[newValue.coupon]

  if (!code) {
    console.log(`Coupon code not found: ${newValue.coupon}`)
    return
  }
  const userSnapshots = await firestore
    .collection('users')
    .where('email', '==', newValue.email)
    .get()

  if (userSnapshots.empty) {
    return
  }

  const maxCredits = getMaxCreditsByPlan(code.plan)
  const maxMembers = code.maxMembers || 1
  const hasTeamSubscription = code.maxMembers > 1
  const endAt = Timestamp.now().toDate()
  endAt.setDate(endAt.getDate() + code.days)

  const updateObject = {
    creditsOwned: maxCredits,
    hasTeamSubscription,
    subscription: { plan: code.plan, maxMembers, members: [], endAt },
    ...(code.plan === 'Team' && { teams: [userSnapshots.docs[0].ref] }),
  }

  await userSnapshots.docs[0].ref.update(updateObject)

  console.log(`Code: ${newValue.coupon} applied to user ${newValue.email}`, updateObject)
}

exports.onCustomerSubscriptionChanged = functions.firestore
  .document('customers/{customerId}/subscriptions/{subscriptionId}')
  .onWrite(async (change, context) => {
    console.log('onCustomerSubscriptionChanged:', change, context)

    const newValue = change.after.data()

    console.log('newValue', newValue)

    const customerId = context.params.customerId
    const subscriptionId = context.params.subscriptionId

    const customerDoc = await firestore.collection('customers').doc(customerId).get()

    if (!customerDoc) {
      console.log(`No customer with id: ${customerId} found`)
      return
    }

    const customer = customerDoc.data()

    const subscriptionDoc = await customerDoc.ref
      .collection('subscriptions')
      .doc(subscriptionId)
      .get()

    const subscription = subscriptionDoc.data()
    const productSnapshot = await subscription.product.get()
    const product = productSnapshot.data()

    const hasTeamSubscription = product.name === 'Team'
    const maxMembers = hasTeamSubscription ? subscription.quantity || 2 : 1

    const userSnapshots = await firestore
      .collection('users')
      .where('email', '==', customer.email)
      .get()

    if (userSnapshots.empty) {
      return
    }

    const user = userSnapshots.docs[0].data()
    const userRef = userSnapshots.docs[0].ref

    if (newValue.status === 'canceled') {
      const updateObject = {
        subscription: {
          plan: '',
          maxMembers: 0,
          members: [],
          teams: arrayRemove(userRef),
        },
      }

      const result = await userRef.update(updateObject)
      console.log('Result of removing subscription for user', customer.email, result)
    } else {
      let updateObject = {
        creditsOwned: getMaxCreditsByPlan(product.name),
        hasTeamSubscription,
        subscription: { plan: product.name, maxMembers },
        ...(product.name === 'Team' && { teams: [userRef] }),
      }
      if (!user.subscription?.members) {
        updateObject.subscription.members = []
      }

      const result = await userRef.update(updateObject)
      console.log(
        'Result of setting subscription for user',
        customer.email,
        'with maxMembers:',
        maxMembers,
        result,
      )
    }
  })

exports.onCustomerSubDeleted = functions.firestore
  .document('customers/{customerId}/subscriptions/{subscriptionId}')
  // .onDelete(async (change, context) => {
  .onDelete((change, context) => {
    console.log('onCustomerSubDeleted:', change, context)
  })

const apiKey = 'AIzaSyAQ9B5DSWeqWLLZSmpLAAOCScSyz0lv7KE'
if (!apiKey) {
  throw new Error('GOOGLE_AI_API_KEY is not set in environment variables')
}

const genAI = new GoogleGenerativeAI(apiKey)

const getLibraryTags = async (locale, text) => {
  const tagsDocRef = `library_tags${locale === 'en' ? '' : '_' + locale}`

  const libraryTagsDoc = await admin.firestore().collection('settings').doc(tagsDocRef).get()

  const libraryTags = libraryTagsDoc.exists ? libraryTagsDoc.data().tags || [] : []
  console.log(`libraryTags for locale ${locale}:`, libraryTags)

  if (libraryTags.length === 0) {
    return ''
  }

  prompt = `
      Select from this list of tags only those that match this prompt. Write only tag names separated by space.
      Tags: ${libraryTags.join(', ')}
      Prompt: [${text}]
      `

  return prompt
}

exports.askAIGoogle = onCall(async (request, context) => {
  console.log('Received data askAIGoogle:', request)
  let prompt = ''
  try {
    console.log('Function started')
    if (request.data.functionName === 'betterPrompt') {
      prompt = `
  Optimize this prompt:
  1. Increase conciseness and clarity
  2. Break complex tasks into steps
  3. Specify desired output format
  4. Add context if needed
  5. Include relevant constraints
  6. Encourage creativity when appropriate
  7. Ensure prompt is ethical and within model capabilities
  8. Format for readability (use line breaks, bullet points, or numbering as needed)
  Return only the optimized prompt in the language the prompt was written, without any labels or extra text.
  Prompt: [${request.data.prompt}]
  `
    }
    if (request.data.functionName === 'tipPrompt') {
      prompt = `
    Write me tips on how to improve this prompt below , write advice in the same language what prompt is written. Write me advice only in a separate paragraph. Each paragraph should begin with - . 
    Prompt: [${request.data.prompt}]
    `
    }
    if (request.data.functionName === 'test') {
      prompt = `${request.data.prompt}`
    }
    if (request.data.functionName === 'library') {
      prompt = await getLibraryTags(request.data.locale, request.data.prompt)
    }
    console.log('Prompt:', prompt)

    if (!prompt) {
      return { result: '' }
    }

    const message = await run(prompt)
    console.log('Generated message:', message)

    return { result: message }
  } catch (error) {
    console.error('Error in askAI:', error)
    throw new functions.https.HttpsError('internal', `An error occurred: ${error.message}`)
  }
})

exports.askAIGoogleGemini = onCall({ timeoutSeconds: 360 }, async (request, context) => {
  console.log('Received data askAIGoogleGemini:', request.data.prompt)
  try {
    const message = await run(request.data.prompt)
    console.log('Generated message:', message)

    return { result: message }
  } catch (error) {
    console.error('Error in askAI:', error)
    throw new functions.https.HttpsError('internal', `An error occurred: ${error.message}`)
  }
})

async function run(prompt) {
  console.log('run function started')
  const model = genAI.getGenerativeModel({ model: 'models/gemini-2.5-flash-preview-05-20' })

  console.log('Generating content...')
  const result = await model.generateContent(prompt)
  console.log('Content generated')

  console.log('Full result:', JSON.stringify(result, null, 2))

  if (result.response) {
    const text = result.response.text()
    console.log('Extracted text:', text)
    return text
  } else {
    throw new Error('No response in the result')
  }
}

function formatResponse(text) {
  const lines = text.split('\n')

  const formattedLines = lines.map((line, index) => {
    line = line.trim()
    if (line.match(/^\d+\./)) {
      return `\n${line}`
    } else if (line.startsWith('-') || line.startsWith('•')) {
      return `  ${line}`
    } else if (line === '') {
      return line
    } else {
      return index === 0 ? line : `  ${line}`
    }
  })

  return formattedLines.join('\n')
}

exports.askAnthropic = onCall(async (request, context, locale) => {
  console.log('Function started')
  console.log('Received data:', request)
  let prompt = ''
  let systemPrompt = ''

  try {
    console.log('Function started')
    if (request.data.functionName === 'betterPrompt') {
      systemPrompt =
        'Analyze the given prompt and return an improved version using the following criteria. ' +
        'Important: Return ONLY the optimized prompt text, without any introductory phrases or explanations. ' +
        'The response should start directly with the optimized prompt content. ' +
        'Criteria for optimization: ' +
        'Clearly define the purpose of the prompt. ' +
        'Provide a logical and coherent structure. ' +
        'Adjust the level of detail as needed. ' +
        'Add the necessary context. ' +
        'Use clear and unambiguous language. ' +
        'Identify relevant constraints or parameters. ' +
        'Suggest the desired format of the answer, if necessary. ' +
        'Add examples if needed. ' +
        'Allow for creativity, if desired. ' +
        'Include ethical aspects if relevant.' +
        'Return only the optimised prompt in the language in which it was written, without any labels or additional text and without explaining what you are doing.'
      prompt = `Prompt to optimize: ${request.data.prompt}`
    }

    if (request.data.functionName === 'library') {
      prompt = await getLibraryTags(request.data.locale, request.data.prompt)
    }

    if (!prompt) {
      return { result: '' }
    }

    console.log('Prompt:', prompt)
    console.log('System Prompt:', systemPrompt)

    const anthropic = new Anthropic({
      apiKey:
        '************************************************************************************************************', // defaults to process.env["ANTHROPIC_API_KEY"]
    })

    const msg = await anthropic.messages.create({
      model: 'claude-3-haiku-20240307',
      max_tokens: 500,
      temperature: 0,
      system: systemPrompt,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    })

    console.log('Generated message:', JSON.stringify(msg, null, 2))

    if (msg.content && msg.content[0] && msg.content[0].text) {
      return { result: msg.content[0].text }
    } else {
      throw new Error('Unexpected response structure from Anthropic API')
    }
  } catch (error) {
    console.error('Error in askAnthropic:', error)
    throw new functions.https.HttpsError('internal', `An error occurred: ${error.message}`)
  }
})
// exports.askAnthropic = onRequest(async (request, response) => {
//   response.set('Access-Control-Allow-Origin', '*')
//   response.set('Access-Control-Allow-Methods', 'GET, POST')
//   response.set('Access-Control-Allow-Headers', 'Content-Type')

//   if (request.method === 'OPTIONS') {
//     response.status(204).send('')
//     return
//   }

//   try {
//     console.log('Function started')

//     let prompt
//     if (request.method === 'POST') {
//       prompt = request.body.prompt
//       console.log('POST request body:', request.body)
//     } else if (request.method === 'GET') {
//       prompt = request.query.prompt
//       console.log('GET request query:', request.query)
//     } else {
//       throw new Error('Unsupported HTTP method')
//     }

//     if (!prompt) {
//       throw new Error('No prompt provided')
//     }

//     console.log('Prompt:', prompt)

//     const systemPrompt =
//       'Analyse the given prompt and return an improved version using the following criteria: ' +
//       'Clearly define the purpose of the prompt. ' +
//       'Provide a logical and coherent structure. ' +
//       'Adjust the level of detail as needed. ' +
//       'Add the necessary context. ' +
//       'Use clear and unambiguous language. ' +
//       'Identify relevant constraints or parameters. ' +
//       'Suggest the desired format of the answer, if necessary. ' +
//       'Add examples if needed. ' +
//       'Allow for creativity, if desired. ' +
//       'Include ethical aspects if relevant.' +
//       'Return only the optimized prompt in the language the prompt was written, without any labels or extra text.'
//     const msg = await anthropic.messages.create({
//       model: 'claude-3-haiku-20240307',
//       max_tokens: 500,
//       temperature: 0,
//       system: systemPrompt,
//       messages: [
//         {
//           role: 'user',
//           content: `Prompt to optimize: ${prompt}`,
//         },
//       ],
//     })

//     console.log('Generated message:', JSON.stringify(msg, null, 2))

//     if (msg.content && msg.content[0] && msg.content[0].text) {
//       response.setHeader('Content-Type', 'text/plain; charset=utf-8')
//       response.send(msg.content[0].text)
//     } else {
//       console.error('Unexpected response structure from Anthropic API:', msg)
//       response.status(500).send('Unexpected response structure from Anthropic API')
//     }
//   } catch (error) {
//     console.error('Detailed error:', error)
//     if (error.status) {
//       response.status(error.status).send(`Anthropic API error: ${error.message}`)
//     } else {
//       response.status(500).send(`An unexpected error occurred: ${error.message}`)
//     }
//   }
// })

const openai = new OpenAI({
  apiKey: '********************************************************',
})

exports.askAIGPT = onCall(async (request, context, locale) => {
  console.log('askGPT function started')
  console.log('Received data:', request.data)
  let prompt = ''
  let systemPrompt = ''

  const frameworkPrompts = {
    RTF: 'Role, Task, Format',
    TAG: 'Task, Action, Goal',
    BAB: 'Before, After, Bridge',
    CARE: 'Challenge, Action, Result, Elaboration',
    RISE: 'Role, Input, Steps, Expectation',
    COAST: 'Context, Objective, Action, Scenario, Task',
    TAKE: 'Task, Action, Expectation, Result',
    PAIN: 'Problem, Action, Information, Next Step',
    CREATE: 'Character, Request, Example, Adjustment, Type of output, Extras',
  }

  try {
    console.log('Setting up prompt and systemPrompt')

    if (request.data.functionName === 'betterPrompt') {
      systemPrompt = `
  Optimize this prompt:
  1. Increase conciseness and clarity
  2. Break complex tasks into steps
  3. Specify desired output format
  4. Add context if needed
  5. Include relevant constraints
  6. Encourage creativity when appropriate
  7. Ensure prompt is ethical and within model capabilities
  8. Format for readability (use line breaks, bullet points, or numbering as needed)
  Return only the optimized prompt in the language the prompt was written, without any labels or extra text. `
      prompt = `Prompt to optimize: ${request.data.prompt}`
    } else if (Object.keys(frameworkPrompts).includes(request.data.functionName)) {
      const framework = frameworkPrompts[request.data.functionName]
      systemPrompt = `
      Return only the optimized prompt not example. The prompt should be in the language the prompt was written.
      Analyze the given prompt and convert it to the ${request.data.functionName} (${framework}) framework while maintaining the original language. Return an improved version using the following criteria.
      Important: Return ONLY the optimized prompt text, without any introductory phrases or explanations.
      The response should start directly with the optimized prompt content.
      Criteria for optimization:
      Clearly define the purpose: Convert to ${request.data.functionName} while preserving original language.
      Provide a logical structure: Use ${framework} sections.
      Adjust the level of detail: Be comprehensive yet concise.
      Add the necessary context: Explain ${request.data.functionName} if needed.
      Use clear and unambiguous language in instructions.
      Identify relevant constraints: Specify to return only the converted prompt.
      Suggest the desired format: Outline the ${request.data.functionName} structure.
      Allow for creativity in interpreting roles and tasks.
      Ensure the conversion maintains the original intent.
      Do not add any examples or explanations just prompt ${framework}.
      `
      prompt = `Prompt to optimize: ${request.data.prompt}`
    } else if (request.data.functionName === 'betterPromptAdvanced') {
      systemPrompt =
        'Analyze the given prompt and return an improved version using the following criteria. ' +
        'Important: Return ONLY the optimized prompt text, without any introductory phrases or explanations. ' +
        'The response should start directly with the optimized prompt content. ' +
        'Criteria for optimization: ' +
        'Clearly define the purpose of the prompt. ' +
        'Provide a logical and coherent structure. ' +
        'Adjust the level of detail as needed. ' +
        'Add the necessary context. ' +
        'Use clear and unambiguous language. ' +
        'Identify relevant constraints or parameters. ' +
        'Suggest the desired format of the answer, if necessary. ' +
        'Add examples if needed. ' +
        'Allow for creativity, if desired. ' +
        'Include ethical aspects if relevant.' +
        'Return only the optimised prompt in the language in which it was written, without any labels or additional text and without explaining what you are doing.'
      prompt = `Prompt to optimize: ${request.data.prompt}`
    } else if (request.data.functionName === 'tipPrompt') {
      systemPrompt = `
    Write me tips on how to improve this prompt below , write advice in the same language what prompt is written. Write me advice only in a separate paragraph. Each paragraph should begin with - . 
    `
      prompt = `Prompt to optimize: ${request.data.prompt}`
    } else if (request.data.functionName === 'library') {
      prompt = await getLibraryTags(request.data.locale, request.data.prompt)
      if (!prompt) {
        return { result: '' }
      }
    } else if (request.data.functionName === 'betterPromptInfo') {
      systemPrompt = `
      Compare the following two prompts on the same topic.Analyse them and explain why the improve prompt may return better, more detailed results than the your prompt. For each of these criteria, give the percentage by how much the prompt is better. At the end of your answer, give the percentage by which the prompt is better. 

Clarity and Precision: The prompt should be clear and unambiguous, leaving no room for misunderstanding. It should precisely convey what is expected.

Specificity and Detail: A good prompt provides enough specific information to guide the response effectively. It should outline the context and any necessary details.

Relevance to the Objective: The prompt should be directly related to the desired outcome or topic. It should stay focused on the subject matter to elicit the most appropriate responses.

Engagement and Creativity: An effective prompt encourages thoughtful and creative responses. It should be engaging to stimulate interest and originality.

Appropriateness and Compliance: The prompt must adhere to ethical guidelines and be appropriate for the intended audience. It should avoid disallowed content and respect cultural sensitivities.
Format: Use html format.  
- Use appropriate HTML tags.
- Include line breaks, bullet points, and numbering.
- Organize content with headings and paragraphs.

Return only the optimized prompt in the language the prompt was written, without any labels or extra text.

Your prompt: [${request.data.promptSource}]

Improve prompt: [ ${request.data.prompt} ]


in the end show only percent. `
    } else if (request.data.functionName === 'generateTopicName') {
      systemPrompt = `
      Generate a short, concise topic name for the given prompt. The topic name should be:
      1. Between one to six words
      2. Descriptive of the prompt's main focus
      3. Without any brackets or extra formatting
      Return only the topic name, without any additional text or explanation.
      `
      prompt = `Prompt to generate topic name for: ${request.data.prompt}`
    } else {
      throw new Error(`Unsupported function name: ${request.data.functionName}`)
    }
    console.log('Prompt:', prompt)
    console.log('System Prompt:', systemPrompt)

    console.log('Calling OpenAI API')
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt },
      ],
      max_tokens: 500,
      temperature: 0,
    })

    console.log('OpenAI API response received')
    console.log('Generated message:', JSON.stringify(completion, null, 2))

    if (completion.choices && completion.choices[0] && completion.choices[0].message) {
      return { result: completion.choices[0].message.content }
    } else {
      throw new Error('Unexpected response structure from OpenAI API')
    }
  } catch (error) {
    console.error('Error in askGPT:', error)
    if (error.response) {
      console.error('OpenAI API error response:', error.response.data)
    }
    throw new functions.https.HttpsError('internal', `An error occurred: ${error.message}`)
  }
})

// If you're using the emulator, you can log the local function URL
if (useEmulator) {
  console.log(
    `askAIGPT function URL (local): http://127.0.0.1:5001/chat-gpt-assistant-3671a/us-central1/askAIGPT`,
  )
}
